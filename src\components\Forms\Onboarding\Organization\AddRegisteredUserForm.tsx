import React from 'react';
import { Listbox, ListboxButton, ListboxOption, ListboxOptions, Transition } from '@headlessui/react';
import { SelectorIcon, CheckIcon } from '@heroicons/react/solid';

interface Role {
    id: number;
    name: string;
    label: string;
    description: string;
    permissions: Array<{
        id: number;
        name: string;
        description: string;
        created_at: string;
        updated_at: string;
        deleted_at: string | null;
    }>;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
}

interface AddRegisteredUserFormProps {
    handleAddRegisteredUser: (title: string, firstName: string, lastName: string, email: string, roleId: number) => void;
    registeredUserTitle: string;
    setRegisteredUserTitle: (value: string) => void;
    registeredUserFirstName: string;
    setRegisteredUserFirstName: (value: string) => void;
    registeredUserLastName: string;
    setRegisteredUserLastName: (value: string) => void;
    registeredUserEmail: string;
    setRegisteredUserEmail: (value: string) => void;
    registeredUserRoleId: number;
    setRegisteredUserRoleId: (value: number) => void;
    errors: { [key: string]: string };
    roles: Role[];
}

const titles = ["Mr", "Ms", "Mrs", "Dr", "Prof"];

const AddRegisteredUserForm: React.FC<AddRegisteredUserFormProps> = ({
    handleAddRegisteredUser,
    registeredUserTitle,
    setRegisteredUserTitle,
    registeredUserFirstName,
    setRegisteredUserFirstName,
    registeredUserLastName,
    setRegisteredUserLastName,
    registeredUserEmail,
    setRegisteredUserEmail,
    registeredUserRoleId,
    setRegisteredUserRoleId,
    errors,
    roles
}) => {
    const rolesToDisplay = ["reviewer", "approver", "initiator"];
    const filteredRoles = roles.filter(role => rolesToDisplay.includes(role.name.toLowerCase()));

    return (
        <form
            onSubmit={(e) => {
                e.preventDefault();
                handleAddRegisteredUser(registeredUserTitle, registeredUserFirstName, registeredUserLastName, registeredUserEmail, registeredUserRoleId);
                setRegisteredUserTitle('');
                setRegisteredUserFirstName('');
                setRegisteredUserLastName('');
                setRegisteredUserEmail('');
                setRegisteredUserRoleId(0);
            }}
        >
            <div className="mt-2">
                <label
                    htmlFor="registered_title"
                    className="block text-sm font-medium text-gray-700"
                >
                    Title <span className="text-red-500">*</span>
                </label>
                <Listbox value={registeredUserTitle} onChange={setRegisteredUserTitle}>
                    <div className="relative mt-1">
                        <ListboxButton className={`relative w-full cursor-default rounded-md border border-gray-300 py-2 pl-3 pr-10 text-left shadow-md focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-11 ${registeredUserTitle ? "bg-[#e8f0fe]" : ""}`} style={{ fontSize: "14px" }}>
                            <span className="block truncate">{registeredUserTitle || "Select Title"}</span>
                            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                <SelectorIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                            </span>
                        </ListboxButton>
                        <Transition
                            as={React.Fragment}
                            leave="transition ease-in duration-100"
                            leaveFrom="opacity-100"
                            leaveTo="opacity-0"
                        >
                            <ListboxOptions className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none md:text-md" style={{ fontSize: "14px" }}>
                                {titles.map((title) => (
                                    <ListboxOption
                                        key={title}
                                        className={({ active }) =>
                                            `relative cursor-default select-none py-2 pl-10 pr-4 ${active ? "bg-[#f1f1f1] text-gray-700" : "text-gray-900"
                                            }`}
                                        value={title}
                                    >
                                        {({ selected }) => (
                                            <>
                                                <span
                                                    className={`block truncate ${selected ? "font-medium" : "font-normal"
                                                        }`}
                                                >
                                                    {title}
                                                </span>
                                                {selected ? (
                                                    <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-white">
                                                        <CheckIcon className="h-5 w-5" aria-hidden="true" />
                                                    </span>
                                                ) : null}
                                            </>
                                        )}
                                    </ListboxOption>
                                ))}
                            </ListboxOptions>
                        </Transition>
                    </div>
                </Listbox>
                {errors.registeredUserTitle && <p className="text-red-500 text-sm">{errors.registeredUserTitle}</p>}
            </div>
            <div className="mt-2">
                <label
                    htmlFor="registered_first_name"
                    className="block text-sm font-medium text-gray-700"
                >
                    First Name<span className="text-red-500">*</span>
                </label>
                <input
                    type="text"
                    id="registered_first_name"
                    name="registered_first_name"
                    value={registeredUserFirstName}
                    onChange={(e) => setRegisteredUserFirstName(e.target.value)}
                    className={`mt-1 w-full rounded-md shadow-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-wbv-theme ${registeredUserFirstName ? "bg-[#e8f0fe]" : ""}`}
                    style={{ fontSize: "14px" }}
                />
                {errors.registeredUserFirstName && <p className="text-red-500 text-sm">{errors.registeredUserFirstName}</p>}
            </div>
            <div className="mt-2">
                <label
                    htmlFor="registered_last_name"
                    className="block text-sm font-medium text-gray-700"
                >
                    Last Name <span className="text-red-500">*</span>
                </label>
                <input
                    type="text"
                    id="registered_last_name"
                    name="registered_last_name"
                    value={registeredUserLastName}
                    onChange={(e) => setRegisteredUserLastName(e.target.value)}
                    className={`mt-1 w-full rounded-md shadow-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-wbv-theme ${registeredUserLastName ? "bg-[#e8f0fe]" : ""}`}
                    style={{ fontSize: "14px" }}
                />
                {errors.registeredUserLastName && <p className="text-red-500 text-sm">{errors.registeredUserLastName}</p>}
            </div>
            <div className="mt-2">
                <label
                    htmlFor="registered_email"
                    className="block text-sm font-medium text-gray-700"
                >
                    Email <span className="text-red-500">*</span>
                </label>
                <input
                    type="email"
                    id="registered_email"
                    name="registered_email"
                    value={registeredUserEmail}
                    onChange={(e) => setRegisteredUserEmail(e.target.value)}
                    className={`mt-1 w-full rounded-md shadow-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-wbv-theme ${registeredUserEmail ? "bg-[#e8f0fe]" : ""}`}
                    style={{ fontSize: "14px" }}
                />
                {errors.registeredUserEmail && <p className="text-red-500 text-sm">{errors.registeredUserEmail}</p>}
            </div>
            <div className="mt-2">
                <label
                    htmlFor="registered_role"
                    className="block text-sm font-medium text-gray-700"
                >
                    User Role <span className="text-red-500">*</span>
                </label>
                <Listbox value={registeredUserRoleId} onChange={setRegisteredUserRoleId}>
                    <div className="relative mt-1">
                        <ListboxButton className={`relative w-full cursor-default rounded-md border border-gray-300 py-2 pl-3 pr-10 text-left shadow-md focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-11 ${registeredUserRoleId ? "bg-[#e8f0fe]" : ""}`} style={{ fontSize: "14px" }}>
                            <span className="block truncate">
                                {(() => {
                                    const selectedRole = roles.find(role => role.id === registeredUserRoleId);
                                    return selectedRole ? selectedRole.name.charAt(0).toUpperCase() + selectedRole.name.slice(1) : "Select User Role";
                                })()}
                            </span>
                            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                <SelectorIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                            </span>
                        </ListboxButton>
                        <Transition
                            as={React.Fragment}
                            leave="transition ease-in duration-100"
                            leaveFrom="opacity-100"
                            leaveTo="opacity-0"
                        >
                            <ListboxOptions className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none md:text-md" style={{ fontSize: "14px" }}>
                                {filteredRoles.map((role) => (
                                    <ListboxOption
                                        key={role.id}
                                        className={({ active }) =>
                                            `relative cursor-default select-none py-2 pl-10 pr-4 ${active ? "bg-[#f1f1f1] text-gray-700" : "text-gray-900"
                                            }`}
                                        value={role.id}
                                    >
                                        {({ selected }) => (
                                            <>
                                                <span
                                                    className={`block truncate ${selected ? "font-medium" : "font-normal"
                                                        }`}
                                                >
                                                    {role.name.charAt(0).toUpperCase() + role.name.slice(1)}
                                                </span>
                                                {selected ? (
                                                    <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-white">
                                                        <CheckIcon className="h-5 w-5" aria-hidden="true" />
                                                    </span>
                                                ) : null}
                                            </>
                                        )}
                                    </ListboxOption>
                                ))}
                            </ListboxOptions>
                        </Transition>
                    </div>
                </Listbox>
                {errors.registeredUserRoleId && <p className="text-red-500 text-sm">{errors.registeredUserRoleId}</p>}
            </div>
            <button
                type="submit"
                className="mt-4 w-full rounded-md bg-wbv-theme px-4 py-2 font-semibold text-white transition duration-300 hover:bg-opacity-90"
            >
                Add Registered User
            </button>
        </form>
    );
};

export default AddRegisteredUserForm;