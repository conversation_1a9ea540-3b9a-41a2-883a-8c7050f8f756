"use client";

import Cookies from "js-cookie";
import { getUserById } from "@/utils/user";
import Link from "next/link";
import Image from "next/image";
import SidebarItem from "@/components/Sidebar/SidebarItem";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import ClickOutside from "@/components/ClickOutside";
import useLocalStorage from "@/hooks/useLocalStorage";
import {
  faCar,
  faCircleDollarToSlot,
  faGauge,
  faCode,
  faRectangleAd,
  faUsers,
  faBuildingUser,
  faHandshake,
  faBoxOpen,
} from "@fortawesome/free-solid-svg-icons";
import { useEffect, useState } from "react";

interface SidebarProps {
  sidebarOpen: boolean;
  setSidebarOpen: (arg: boolean) => void;
  userRole?: string;
}

const Sidebar = ({ sidebarOpen, setSidebarOpen, userRole }: SidebarProps) => {
  const [user, setUser] = useState<{
    first_name: string;
    last_name: string;
    role_name: string;
  } | null>(null);
  const [pageName, setPageName] = useLocalStorage("selectedMenu", "dashboard");
  const menuGroups = [
    {
      name: "MENU",
      menuItems: [
        {
          icon: <FontAwesomeIcon icon={faGauge} />,
          label: "Dashboard",
          route: "/",
        },
        {
          icon: <FontAwesomeIcon icon={faCircleDollarToSlot} />,
          label: "Billing",
          route: "/billing",
        },
        {
          icon: <FontAwesomeIcon icon={faBoxOpen} />,
          label: "Products",
          route: "/products",
        },
        ...(userRole === "super_admin"
          ? [
              {
                icon: <FontAwesomeIcon icon={faCar} />,
                label: "Vehicles",
                route: "#",
                children: [
                  { label: "Makes", route: "/vehicles/makes" },
                  { label: "Models", route: "/vehicles/models" },
                  { label: "Variants", route: "/vehicles/variants" },
                ],
              },
              {
                icon: <FontAwesomeIcon icon={faBuildingUser} />,
                label: "Organizations",
                route: "/organizations",
              },
              {
                icon: <FontAwesomeIcon icon={faUsers} />,
                label: "Users",
                route: "/users",
              },
              {
                icon: <FontAwesomeIcon icon={faHandshake} />,
                label: "Onboarding",
                route: "/onboarding/requests",
              },
              {
                icon: <FontAwesomeIcon icon={faCode} />,
                label: "Developers",
                route: "#",
                children: [{ label: "Sandbox", route: "/developers/sandbox" }],
              },
            ]
          : userRole === "admin"
            ? [
                {
                  icon: <FontAwesomeIcon icon={faCar} />,
                  label: "Vehicles",
                  route: "#",
                  children: [
                    { label: "Makes", route: "/vehicles/makes" },
                    { label: "Models", route: "/vehicles/models" },
                    { label: "Variants", route: "/vehicles/variants" },
                  ],
                },
                // Add any admin-specific routes here, or remove some from the super_admin list
              ]
            : []),
      ],
    },
  ];
  useEffect(() => {
    const fetchUserInfo = async () => {
      const userId = Cookies.get("id");
      if (userId) {
        const response = await getUserById(Number(userId));
        console.log("userInfo in sidebar:", response);
        setUser(response.data);
      }
    };
    fetchUserInfo();
  }, []);

  return (
    <ClickOutside onClick={() => setSidebarOpen(false)}>
      <aside
        className={`fixed left-0 top-0 z-9999 flex h-screen w-72.5 flex-col overflow-y-hidden bg-[#2C7DA5] duration-300 ease-linear dark:bg-boxdark lg:translate-x-0 ${
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        {/* <!-- SIDEBAR HEADER --> */}
        <div className="flex items-center justify-between gap-2 px-6 py-5.5 lg:py-6.5">
          {/* <!-- Logo --> */}
          <Link href="/">
            <Image
              width={156}
              height={32}
              src={"/images/logos/WBV LOGO 3.png"}
              alt="Logo"
              priority
            />
          </Link>

          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            aria-controls="sidebar"
            className="block lg:hidden"
          >
            <svg
              className="fill-current"
              width="20"
              height="18"
              viewBox="0 0 20 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19 8.175H2.98748L9.36248 1.6875C9.69998 1.35 9.69998 0.825 9.36248 0.4875C9.02498 0.15 8.49998 0.15 8.16248 0.4875L0.399976 8.3625C0.0624756 8.7 0.0624756 9.225 0.399976 9.5625L8.16248 17.4375C8.31248 17.5875 8.53748 17.7 8.76248 17.7C8.98748 17.7 9.17498 17.625 9.36248 17.475C9.69998 17.1375 9.69998 16.6125 9.36248 16.275L3.02498 9.8625H19C19.45 9.8625 19.825 9.4875 19.825 9.0375C19.825 8.55 19.45 8.175 19 8.175Z"
                fill=""
              />
            </svg>
          </button>
        </div>
        {/* <!-- SIDEBAR HEADER --> */}

        <div className="no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear">
          {/* <!-- Sidebar Menu --> */}
          <nav className="px-4 lg:mt-2 lg:px-6">
            {menuGroups.map((group, groupIndex) => (
              <div key={groupIndex}>
                <h3 className="mb-4 ml-4 text-sm font-semibold text-bodydark2">
                  {group.name}
                </h3>

                <ul className="mb-6 flex flex-col gap-1.5">
                  {group.menuItems.map((menuItem, menuIndex) => (
                    <SidebarItem
                      key={menuIndex}
                      item={menuItem}
                      pageName={pageName}
                      setPageName={setPageName}
                    />
                  ))}
                </ul>
              </div>
            ))}
          </nav>
          {/* <!-- Sidebar Menu --> */}
        </div>
      </aside>
    </ClickOutside>
  );
};

export default Sidebar;
