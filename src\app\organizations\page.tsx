"use client";

import DefaultLayout from "@/components/Layouts/DefaultLayout";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import React, { useEffect, useState } from "react";
import Organizations from "@/components/Organizations/Organizations";
import { useSession } from "next-auth/react";

interface Organization {
  id: number;
  name: string;
  street_address: string;
  postal_address: string;
  email: string;
  phone_number: string;
  registration_number: string;
}

const OrganizationsPage: React.FC = () => {
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<any>(null);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [user, setUser] = useState<{
    first_name: string;
    last_name: string;
    role_name: string;
  } | null>(null);
  const { data: session, status } = useSession();

  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        const response = await fetch(`/api/users/${session?.user?.id}`, {
          method: "GET",
        });
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const userData = await response.json();
        setUser({
          first_name: userData.first_name,
          last_name: userData.last_name,
          role_name: userData.role_name,
        });
      } catch (error) {
        console.error("Error fetching user info:", error);
      }
    };

    if (session) {
      fetchUserInfo();
    }
  }, [session]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch("/api/organizations", { method: "GET" });
        if (!response.ok) {
          setError(`HTTP error! status: ${response.status}`);
        }
        const result = await response.json();
        setOrganizations(result.data);
      } catch (error) {
        setError(error);
        console.error("Error fetching organizations:", error);
      } finally {
        setLoading(false); // Update loading state
      }
    };

    fetchData();
  }, []); // Empty dependency array means this effect runs once after the initial render

  const handleSubmit = async (newOrganization: Partial<Organization>) => {
    setLoading(true);
    try {
      const response = await fetch("/api/organizations", {
        method: "POST",
        body: JSON.stringify(newOrganization),
      });
      if (!response.ok) {
        throw new Error("Failed to add organization");
      }
      const result = await response.json();
      setOrganizations((prev) => [...prev, result]);
      setIsModalOpen(false);
      setIsModalOpen(false);
    } catch (err) {
      setError("Failed to add organization");
    } finally {
      setLoading(false);
    }
  };

  const handleUpdate = async (updatedOrganization: Partial<Organization>) => {
    setLoading(true);
    try {
      const response = await fetch("/api/organizations", {
        method: "PUT",
        body: JSON.stringify(updatedOrganization),
      });
      if (!response.ok) {
        throw new Error("Failed to update organization");
      }
      console.log("response\n", response);
      const result = await response.json();

      setOrganizations((prev) =>
        prev.map((org) => (org.id === updatedOrganization.id ? result : org)),
      ); // Update state with updated organization
      setIsModalOpen(false);
    } catch (err) {
      setError("Failed to update organization");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    setLoading(true);
    try {
      console.log("deleting org with id: ", id);
      await fetch(`/api/organizations`, {
        method: "DELETE",
        body: JSON.stringify({ id }),
      });
      setOrganizations((prev) => prev.filter((org) => org.id !== id));
    } catch (err) {
      setError("Failed to delete organization");
    } finally {
      setLoading(false);
    }
  };

  const handleSuspend = async (id: number) => {
    setLoading(true);
    try {
      console.log("suspending org with id: ", id);
    } catch (err) {
      setError("Failed to suspend organization");
    } finally {
      setLoading(false);
    }
  };

  return (
    <DefaultLayout>
      <Breadcrumb pageName="Organizations" />
      <Organizations
        organizations={organizations}
        loading={loading}
        error={error}
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        handleUpdate={handleUpdate}
        handleSubmit={handleSubmit}
        handleDelete={handleDelete}
        handleSuspend={handleSuspend}
        user={user}
      />
    </DefaultLayout>
  );
};

export default OrganizationsPage;
