import { NextRequest, NextResponse } from "next/server";
import { v2EngineAxiosInstance } from "@/utils/axios";

export async function GET(
    req: NextRequest,
    { params }: { params: { id: string } },
) {
    const { id } = params;
    console.log("Fetching payment with id:", id);

    try {
        const response = await v2EngineAxiosInstance.get(`/payments/${id}`);
        const payment = response.data;

        return NextResponse.json(payment);
    } catch (error) {
        console.error("Error fetching invoice:", error);
        return NextResponse.json(
            { error: "Error fetching invoice" },
            { status: 500 },
        );
    }
}