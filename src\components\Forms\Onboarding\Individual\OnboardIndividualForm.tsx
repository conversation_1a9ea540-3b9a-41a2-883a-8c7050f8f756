import { faEye, faEyeSlash } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React, { useState } from "react";
import PhoneInput from "react-phone-input-2";
import { Role } from "@/utils/types/core.whitebook.types";
import "react-phone-input-2/lib/style.css";

interface OnboardIndividualFormProps {
  formData: {
    first_name: string;
    last_name: string;
    email: string;
    phone_number: string;
    password: string;
  };
  errors: {
    general?: string;
    first_name?: string;
    last_name?: string;
    email?: string;
    phone_number?: string;
    password?: string;
  };
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handlePhoneChange: (value: string) => void;
  handleSubmit: (e: React.FormEvent, roles: Role[]) => void;
  showPassword: boolean;
  setShowPassword: (value: boolean) => void;
  roles: Role[];
}

const OnboardIndividualForm: React.FC<OnboardIndividualFormProps> = ({
  formData,
  errors,
  handleChange,
  handlePhoneChange,
  handleSubmit,
  showPassword,
  setShowPassword,
  roles,
}) => {
  const [phoneError, setPhoneError] = useState<string | null>(null);

  const validatePhoneNumber = () => {
    if (
      formData.phone_number.length <= 3 ||
      !formData.phone_number.startsWith("260")
    ) {
      setPhoneError("Valid Zambian phone number is required");
    } else {
      setPhoneError(null);
    }
  };

  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    validatePhoneNumber();
    if (!phoneError) {
      handleSubmit(e, roles);
    }
  };

  return (
    <form onSubmit={onSubmit} className="space-y-4">
      {errors.general && (
        <div className="rounded bg-red-100 p-4 text-red-700">
          <p className="text-center text-sm">{errors.general}</p>
        </div>
      )}
      <div>
        <label
          htmlFor="first_name"
          className="block text-sm font-medium text-gray-700"
        >
          First Name
        </label>
        <input
          type="text"
          id="first_name"
          name="first_name"
          value={formData.first_name}
          onChange={handleChange}
          className={`mt-1 w-full rounded-md border border-gray-300 px-3 py-2 shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${formData.first_name ? "bg-[#e8f0fe]" : ""}`}
          style={{ fontSize: "14px" }}
          required
        />
        {errors.first_name && (
          <p className="mt-1 text-xs text-red-500">{errors.first_name}</p>
        )}
      </div>
      <div>
        <label
          htmlFor="last_name"
          className="block text-sm font-medium text-gray-700"
        >
          Last Name
        </label>
        <input
          type="text"
          id="last_name"
          name="last_name"
          value={formData.last_name}
          onChange={handleChange}
          className={`mt-1 w-full rounded-md border border-gray-300 px-3 py-2 shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${formData.last_name ? "bg-[#e8f0fe]" : ""}`}
          style={{ fontSize: "14px" }}
          required
        />
        {errors.last_name && (
          <p className="mt-1 text-xs text-red-500">{errors.last_name}</p>
        )}
      </div>
      <div>
        <label
          htmlFor="email"
          className="block text-sm font-medium text-gray-700"
        >
          Email
        </label>
        <input
          type="email"
          id="email"
          name="email"
          value={formData.email}
          onChange={handleChange}
          className={`mt-1 w-full rounded-md border border-gray-300 px-3 py-2 shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${formData.email ? "bg-[#e8f0fe]" : ""}`}
          style={{ fontSize: "14px" }}
          required
        />
        {errors.email && (
          <p className="mt-1 text-xs text-red-500">{errors.email}</p>
        )}
      </div>
      <div>
        <label
          htmlFor="phone_number"
          className="mb-1 block text-sm font-medium text-gray-700"
        >
          Phone Number
        </label>
        <PhoneInput
          country={"zm"}
          value={formData.phone_number}
          onChange={handlePhoneChange}
          inputProps={{
            name: "phone_number",
            required: true,
            autoFocus: false,
          }}
          inputStyle={{
            width: "100%",
            height: "2.75rem",
            borderRadius: "0.375rem",
            boxShadow:
              "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
            backgroundColor: formData.phone_number ? "#e8f0fe" : "white",
            fontSize: "14px",
          }}
          onlyCountries={["zm"]}
        />
        {phoneError && (
          <p className="mt-1 text-xs text-red-500">{phoneError}</p>
        )}
      </div>
      <div>
        <label
          htmlFor="password"
          className="block text-sm font-medium text-gray-700"
        >
          Password
        </label>
        <div className="relative">
          <input
            type={showPassword ? "text" : "password"}
            name="password"
            value={formData.password}
            onChange={handleChange}
            placeholder="Password"
            className={`mt-1 w-full rounded-md border border-gray-300 px-3 py-2 shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${formData.password ? "bg-[#e8f0fe]" : ""}`}
            style={{ fontSize: "14px" }}
            required
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute inset-y-0 right-0 flex items-center pr-3 text-sm leading-5"
          >
            <FontAwesomeIcon icon={showPassword ? faEye : faEyeSlash} />
          </button>
        </div>
        {errors.password && (
          <p className="mt-1 text-xs text-red-500">{errors.password}</p>
        )}
      </div>
      <button
        type="submit"
        className="w-full rounded-md bg-wbv-theme px-4 py-2 text-white transition duration-300 hover:bg-opacity-90"
      >
        Submit
      </button>
      <div className="border-t pt-2 text-center">
        <p className="text-sm text-gray-600">
          Already have an account?{" "}
          <a href="/auth/sign-in" className="text-blue-500 hover:underline">
            Sign In
          </a>
        </p>
      </div>
    </form>
  );
};

export default OnboardIndividualForm;
