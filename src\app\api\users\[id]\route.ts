import { NextRequest, NextResponse } from "next/server";
import { getUserById } from "@/utils/user";

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  const { id } = params;

  try {
    const userResponse = await getUserById(Number(id));
    const user = userResponse.data;
    return NextResponse.json(user);
  } catch (error) {
    console.error("Error fetching organization:", error);
    return NextResponse.json(
      { error: "Error fetching organization" },
      { status: 500 },
    );
  }
}
