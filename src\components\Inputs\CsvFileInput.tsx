import React from "react";
import <PERSON> from "papaparse";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUpload } from "@fortawesome/free-solid-svg-icons";

interface CsvFileInputProps {
  onFileLoad: (data: any[]) => void;
}

const CsvFileInput: React.FC<CsvFileInputProps> = ({ onFileLoad }) => {
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const file = e.target.files?.[0];

    if (file) {
      Papa.parse(file, {
        complete: (result) => {
          onFileLoad(result.data);
        },
        header: true,
        dynamicTyping: true,
        skipEmptyLines: true,
      });
    }
  };

  return (
    <label className="ml-2 inline-flex items-center rounded-lg bg-wbv-theme px-4 py-0 text-sm font-semibold text-white hover:bg-opacity-90">
      <FontAwesomeIcon className="mr-2 h-4 w-4 fill-current" icon={faUpload} />
      <span>Upload</span>
      <input
        type="file"
        accept=".csv"
        onChange={handleFileChange}
        style={{ display: "none" }}
      />
    </label>
  );
};

export default CsvFileInput;
