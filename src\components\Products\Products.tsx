import React, { useState, useEffect } from "react";
import { create<PERSON><PERSON>umnHelper, SortingState } from "@tanstack/react-table";
import "@/css/table.css";
import DebouncedSearchInput from "@/components/Inputs/DebouncedSearchInput";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faEye,
  faTrash,
  faMagnifyingGlass,
} from "@fortawesome/free-solid-svg-icons";
import TanStackTable from "@/components/Tables/TanStackTable";
import ConfirmModal from "../Modals/ConfirmModal";
import ProductModal from "../Modals/ProductModal";
import ProductImageModal from "../Modals/ProductImageModal";
import Link from "next/link";
import { getPageSizeOptions } from "../Tables/utils";
import InlineLoader from '../common/Loader/InlineLoader';
import { toast } from 'react-toastify';

// Import types from the types folder
import { Product } from '@/types/product/index';

// Simplified User interface, adapt as needed for permissions
interface User {
  role_name?: string;
  first_name?: string;
  last_name?: string;
}

interface ProductsProps {
  products: Product[];
  loading: boolean;
  error: string | null;
  handleSubmitProduct: (productData: Partial<Product>) => Promise<Product>; // Returns the created product
  handleUpdateProduct: (updatedProduct: Partial<Product>) => void; // Renamed handler
  handleDeleteProduct: (id: number) => void; // Renamed handler

  user?: User | null; // For permission checks (optional)
}

const Products: React.FC<ProductsProps> = ({
  products,
  loading,
  error,
  handleSubmitProduct,
  handleUpdateProduct,
  handleDeleteProduct,

  user, // Kept for future permission checks
}) => {
  const [isProductModalOpen, setIsProductModalOpen] = useState<boolean>(false);
  const [selectedProduct, setSelectedProduct] = useState<Partial<Product> | null>(null);
  const [isConfirmDeleteModalOpen, setIsConfirmDeleteModalOpen] = useState<boolean>(false);
  const [productToDelete, setProductToDelete] = useState<number | null>(null);

  // State for the ProductImageModal
  const [isProductImageModalOpen, setIsProductImageModalOpen] = useState<boolean>(false);
  const [newlyCreatedProduct, setNewlyCreatedProduct] = useState<{id: number, name: string} | null>(null);

  // Reset selected product when modal closes
  useEffect(() => {
    if (!isProductModalOpen) {
      setSelectedProduct(null);
    }
  }, [isProductModalOpen]);

  const openAddModal = () => {
    setSelectedProduct(null); // Ensure it's an add operation
    setIsProductModalOpen(true);
  };

  const openDeleteModal = (id: number) => {
    setProductToDelete(id);
    setIsConfirmDeleteModalOpen(true);
  };

  const confirmDelete = () => {
    if (productToDelete !== null) {
      handleDeleteProduct(productToDelete);
      setProductToDelete(null);
      setIsConfirmDeleteModalOpen(false);
    }
  };

  const getProductNameById = (id: number): string => {
    const product = products?.find((p) => p.id === id);
    return product ? product.name : "Unknown";
  };

  // This function handles submissions from ProductModal
  const handleModalSubmit = async (productData: Partial<Product>) => {
    if (selectedProduct && selectedProduct.id) {
      // Update existing product
      handleUpdateProduct({ ...productData, id: selectedProduct.id });
      setIsProductModalOpen(false); // Close modal on submit
    } else {
      // Create new product
      try {
        const createdProduct = await handleSubmitProduct(productData);
        setIsProductModalOpen(false); // Close product modal

        // If product was created successfully and has an ID, show the image upload modal
        if (createdProduct && createdProduct.id) {
          setNewlyCreatedProduct({
            id: createdProduct.id,
            name: createdProduct.name || 'New Product'
          });
          setIsProductImageModalOpen(true);
        }
      } catch (error) {

        // Keep the modal open if there was an error
      }
    }
  };

  // Handle when images are uploaded in the ProductImageModal
  const handleImagesUploaded = (imagesAdded: boolean = false) => {
    // Close the image modal
    setIsProductImageModalOpen(false);

    // Show success toast with appropriate message based on whether images were added
    if (newlyCreatedProduct) {
      if (imagesAdded) {
        toast.success(`Product "${newlyCreatedProduct.name}" created successfully with images!`, {
          position: "bottom-right",
          autoClose: 3000,
        });
      } else {
        toast.success(`Product "${newlyCreatedProduct.name}" created successfully!`, {
          position: "bottom-right",
          autoClose: 3000,
        });
      }
    }

    setNewlyCreatedProduct(null);
  };


  const [globalFilter, setGlobalFilter] = React.useState("");
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const columnHelper = createColumnHelper<Product>();

  // Table Columns Definition from tanstack table
  const columns = [
    columnHelper.accessor("name", {
      cell: (info) => (
        <div className="max-width-cell truncated-cell">{info?.getValue()}</div>
      ),
      header: "Name",
    }),
    columnHelper.accessor("productType", { // Using productType from the interface
      cell: (info) => (
        <div className="max-width-cell">{info?.getValue()}</div>
      ),
      header: "Type",
    }),
     columnHelper.accessor("price", {
      cell: (info) => (
        <div className="max-width-cell">{info.row.original.currency_code} {info?.getValue()?.toFixed(2)}</div>
      ),
      header: "Price",
    }),
    columnHelper.accessor("description", {
        cell: (info) => (
          <div className="max-width-cell truncated-cell">{info?.getValue()}</div>
        ),
        header: "Description",
      }),

 // Add View button linking to the detail page
    columnHelper.display({
      id: "actions",
      cell: (info) => (
        <div className="flex space-x-4">
          <Link
            href={`/products/${info.row.original.id}`}
            className="text-blue-500 hover:text-opacity-90"
            title="View"
          >
            <FontAwesomeIcon icon={faEye} />
          </Link>
          <button
            onClick={() => openDeleteModal(info.row.original.id)}
            className="text-danger hover:text-opacity-90"
            title="Delete"
          >
            <FontAwesomeIcon icon={faTrash} />
          </button>
        </div>
      ),
      header: "Actions",
      size: 120,
    }),
  ];



  if (loading) {
    return (
      <div className="rounded-sm border border-stroke bg-white p-6 shadow-default dark:border-strokedark dark:bg-boxdark">
        <InlineLoader message="Loading products..." size="large" />
      </div>
    );
  }
  if (error) return <div className="p-4 text-red-500 text-center rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">Error loading products: {error}</div>;

  // Check for empty products array *after* loading and error checks
  if (!products || products.length === 0) {
    return (
        <div className="flex flex-col pt-4">
            <div className="mb-4 flex flex-col items-stretch gap-2 px-6 md:flex-row md:items-center md:justify-between lg:px-8">
                <div className="flex flex-grow items-center gap-1">
                    <FontAwesomeIcon icon={faMagnifyingGlass} className="text-gray-400" />
                    <DebouncedSearchInput
                        value={globalFilter ?? ""}
                        onChange={(value) => setGlobalFilter(String(value))}
                        debounced={500}
                        placeholder="Search products..."
                    />
                </div>
                <button
                    className="ml-0 mt-2 whitespace-nowrap rounded bg-wbv-theme px-4 py-2 text-sm font-semibold text-white hover:bg-opacity-90 md:ml-2 md:mt-0"
                    onClick={openAddModal}
                    >
                    Add Product
                </button>
            </div>

            <div className="p-4 text-center text-gray-500">
                No records found.
            </div>
            <ProductModal
                isOpen={isProductModalOpen}
                onClose={() => setIsProductModalOpen(false)}
                onSubmit={handleModalSubmit}
                currentProduct={selectedProduct}
            />
            <ConfirmModal
                isOpen={isConfirmDeleteModalOpen}
                onClose={() => setIsConfirmDeleteModalOpen(false)}
                onConfirm={confirmDelete}
                title="Confirm Deletion"
                message={`Are you sure you want to delete the product "${getProductNameById(productToDelete ?? 0)}"? This action cannot be undone.`}
                cta="Delete"
                ctaBtnColor="danger"
            />
            {/* Product Image Upload Modal - Shown after product creation */}
            {newlyCreatedProduct && (
              <ProductImageModal
                isOpen={isProductImageModalOpen}
                onClose={() => {
                  // This is now handled in handleImagesUploaded
                  handleImagesUploaded(false);
                }}
                onImagesUploaded={handleImagesUploaded}
                productId={newlyCreatedProduct.id}
                productName={newlyCreatedProduct.name}
              />
            )}
      </div>
    );
  }


  return (
    <div className="flex flex-col pt-4">
      <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
          <div className="mb-4 flex flex-col items-stretch gap-2 md:flex-row md:items-center md:justify-between">
             <div className="flex flex-grow items-center gap-1">
                <FontAwesomeIcon icon={faMagnifyingGlass} className="text-gray-400" />
                <DebouncedSearchInput
                    value={globalFilter ?? ""}
                    onChange={(value) => setGlobalFilter(String(value))}
                    debounced={500}
                    placeholder="Search products by name, type, description..."
                 />
             </div>
             <button
                className="ml-0 mt-2 whitespace-nowrap rounded bg-wbv-theme px-4 py-2 text-sm font-semibold text-white hover:bg-opacity-90 md:ml-2 md:mt-0"
                onClick={openAddModal}
              >
                Add Product
              </button>
          </div>

          <div className="overflow-hidden border-b border-gray-200 shadow sm:rounded-lg">
                <TanStackTable
                    data={products}
                    columns={columns}
                    sorting={sorting}
                    setSorting={setSorting}
                    globalFilter={globalFilter}
                    setGlobalFilter={setGlobalFilter}
                    pageSizeOptions={getPageSizeOptions(products)}
                    />
          </div>
        </div>
      </div>

      <ProductModal
        isOpen={isProductModalOpen}
        onClose={() => setIsProductModalOpen(false)}
        onSubmit={handleModalSubmit}
        currentProduct={selectedProduct}
      />

      {/* Confirm Delete Modal */}
      <ConfirmModal
        isOpen={isConfirmDeleteModalOpen}
        onClose={() => setIsConfirmDeleteModalOpen(false)}
        onConfirm={confirmDelete}
        title="Confirm Deletion"
        message={`Are you sure you want to delete the product "${getProductNameById(productToDelete ?? 0)}"? This action cannot be undone.`}
        cta="Delete"
        ctaBtnColor="danger"
      />

      {/* Product Image Upload Modal - Shown after product creation */}
      {newlyCreatedProduct && (
        <ProductImageModal
          isOpen={isProductImageModalOpen}
          onClose={() => {
            // This is now handled in handleImagesUploaded
            handleImagesUploaded(false);
          }}
          onImagesUploaded={handleImagesUploaded}
          productId={newlyCreatedProduct.id}
          productName={newlyCreatedProduct.name}
        />
      )}

    </div>
  );
};

export default Products;