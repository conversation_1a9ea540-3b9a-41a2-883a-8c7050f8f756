import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faFilePdf } from "@fortawesome/free-solid-svg-icons";
import { formatCurrency, formatDateTime } from "@/components/utils/utils";
import { InvoiceStruct } from "@/schemas/billingSchema";
import ReceiptPDF from "./ReceiptPDF";
import Link from "next/link";
import { PDFDownloadLink } from "@react-pdf/renderer";

interface ReceiptProps {
  invoice: InvoiceStruct;
  loading: boolean;
  error: string | null;
  handleReceiptDownload: () => void;
}

const Receipt: React.FC<ReceiptProps> = ({
  invoice,
  loading,
  error,
  handleReceiptDownload,
}) => {
  if (loading) return <div>Loading...</div>;
  if (error) return <div>{error}</div>;

  return (
    <div className="pb-6 pt-4">
      <div className="mb-4 flex items-center justify-between">
        <h1 className="whitespace-nowrap text-3xl font-bold">
          Receipt No: {invoice?.receipts[0].id}&nbsp;
        </h1>
        <Link href="/billing" className="text-blue-500 hover:underline">
          Back to Billing
        </Link>
      </div>
      <div className="mx-auto max-w-xl rounded-lg bg-white px-8 py-10 shadow-lg">
        <div className="mb-8 flex items-center justify-between">
          <div className="flex items-center">
            <img
              className="mr-2 h-16"
              src={"/images/logo/Kuala-Horizontal-Black.png"}
              alt="Logo"
            />
          </div>
          <div className="text-right text-gray-700">
            <div className="mb-2 text-xl font-bold">RECEIPT</div>
            <div className="text-sm">
              Date:{" "}
              {invoice?.receipts[0]?.created_at
                ? formatDateTime(invoice?.receipts[0]?.created_at)
                : ""}
            </div>
            <div className="text-sm">Receipt No: {invoice?.receipts[0]?.id}</div>
          </div>
        </div>
        <div className="mb-8 border-b-2 border-gray-300 pb-8">
          <h2 className="mb-4 text-2xl font-bold">Bill To:</h2>
          {/* Billing Address */}
          <div className="mb-2 text-gray-700">{invoice?.address.name}</div>
          <div className="mb-2 text-gray-700">{invoice?.address.street}</div>
          <div className="mb-2 text-gray-700">
            {invoice?.address.city}, {invoice?.address.state},{" "}
            {invoice?.address.country}
          </div>
          <div className="text-gray-700">{invoice?.contact_email}</div>
        </div>
        <table className="mb-8 w-full border-b-2 border-gray-300 text-left">
          <thead>
            <tr>
              <th className="py-2 font-bold uppercase text-gray-700">
                Description
              </th>
              <th className="py-2 font-bold uppercase text-gray-700">Quantity</th>
              <th className="py-2 font-bold uppercase text-gray-700">Price</th>
            </tr>
          </thead>
          <tbody>
            {invoice?.receipts[0]?.line_items.map((item, index) => (
              <tr key={index}>
                <td className="py-4 text-gray-700">{item.note}</td>
                <td className="py-4 text-gray-700">{item.quantity}</td>
                <td className="py-4 text-gray-700">
                  {formatCurrency(item.total, "ZMW")}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        <div className="mb-8 flex justify-end">
          <div className="mr-8 text-gray-700">
            <div className="mb-2">Subtotal:</div>
            <div className="text-xl font-bold">Total:</div>
          </div>
          <div className="text-right text-gray-700">
            <div className="mb-2">{formatCurrency(invoice?.subtotal, "ZMW")}</div>
            <div className="text-xl font-bold">
              {formatCurrency(invoice?.total, "ZMW")}
            </div>
          </div>
        </div>
        {invoice?.status === "paid" ? (
          <>
            <PDFDownloadLink
              document={<ReceiptPDF invoiceId={invoice?.id} />}
              fileName={`receipt-${invoice?.receipts[0].id}.pdf`}
              className="text-black hover:opacity-90"
              title="Download Receipt PDF"
            >
              <button
                className="w-full rounded-lg bg-black px-3 py-3 font-bold text-white hover:bg-opacity-90"
              >
                <span>
                  <FontAwesomeIcon className="px-2" icon={faFilePdf} />
                </span>
                Download
              </button>
            </PDFDownloadLink>
          </>
        ) : null}
      </div>
    </div>
  );
};

export default Receipt;
