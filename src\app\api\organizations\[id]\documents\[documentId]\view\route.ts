import { NextRequest, NextResponse } from "next/server";
import { Client as MinioClient } from 'minio';

const {
    S3_ENDPOINT,
    S3_BUCKET,
    S3_USE_SSL,
    S3_ACCESS_KEY,
    S3_SECRET_KEY,
} = process.env;

const minioClient = new MinioClient({
    endPoint: S3_ENDPOINT!,
    port: S3_USE_SSL === 'true' ? 443 : 80,
    useSSL: S3_USE_SSL === 'true',
    accessKey: S3_ACCESS_KEY!,
    secretKey: S3_SECRET_KEY!,
});

export async function GET(
    request: NextRequest,
    { params }: { params: { organizationId: string; documentId: string } }
) {
    console.log("params in view route.ts", params);

    try {
        // Get document details from your backend first
        const response = await fetch(
            `${process.env.WHITEBOOK_API_URL_V2}/documents/${Number(params.documentId)}`,
            {
                headers: {
                    "X-WHITEBOOK-API-Key": process.env.WHITEBOOK_ADMIN_API_KEY!,
                },
            }
        );

        if (!response.ok) {
            throw new Error('Failed to fetch document details');
        }

        const document = await response.json();

        // Generate presigned URL for viewing
        const presignedUrl = await minioClient.presignedGetObject(
            S3_BUCKET!,
            document.data.path,
            60 * 5 // URL expires in 5 minutes
        );

        return NextResponse.json({ url: presignedUrl });
    } catch (error) {
        console.error('Error generating view URL:', error);
        return NextResponse.json(
            { error: 'Failed to generate view URL' },
            { status: 500 }
        );
    }
}