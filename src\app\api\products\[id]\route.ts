import { NextRequest, NextResponse } from "next/server";
import { v2EngineAxiosInstance, APIErrorResponse } from "@/utils/axios";

// GET /api/products/[id] - Fetch a single product by ID
export async function GET(
  _req: NextRequest,
  { params }: { params: { id: string } },
) {
  const { id } = params;

  if (isNaN(Number(id))) {
      return NextResponse.json({ error: "Invalid Product ID format" }, { status: 400 });
  }

  try {
    const response = await v2EngineAxiosInstance.get(`/products/${id}`);

    // Safely extract the actual product data, likely nested under 'data'
    const data = response.data as { data?: Record<string, unknown>; id?: number } | undefined;
    if (data && typeof data.data === 'object' && data.data !== null) {
        return NextResponse.json(data.data);
    } else if (data && typeof data === 'object' && data !== null && 'id' in data) {
        // Fallback if not nested under 'data'
        return NextResponse.json(data);
    } else {
      console.error("Unexpected product data structure from V2 engine:", response.data);
      return NextResponse.json(
        { error: "Failed to fetch product due to unexpected format." },
        { status: 500 }
      );
    }

  } catch (error: unknown) {
    const err = error as APIErrorResponse;
    const status = err.response?.status || 500;
    let message = `Error fetching product ${id}`;

    if (status === 404) {
        message = `Product with ID ${id} not found.`;
    } else if (err.response?.data?.message) {
        message = err.response.data.message as string;
    } else if (err.response?.data?.error) {
        message = err.response.data.error as string;
    } else if (err.response?.message) {
        message = err.response.message as string;
    } else if (err && typeof err === "object" && "message" in err) {
        message = (err as { message?: string }).message ?? message;
    }
    // Log the essential error details
    console.error(`Error fetching product ${id} (Status: ${status}): ${message}`, err.response?.data || err);
    return NextResponse.json({ error: message }, { status });
  }
}

// PUT /api/products/[id] - Update a product by ID
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  const { id } = params;
  let requestBody = {};
  try {
    requestBody = await req.json() as Record<string, unknown>;

    const response = await v2EngineAxiosInstance.put(
      `/products/${id}`,
      requestBody,
      {
        headers: { "Content-Type": "application/json" }
      }
    );
    return NextResponse.json(response.data);

  } catch (error: unknown) {
    const err = error as APIErrorResponse;
    const status = err.response?.status || 500;
    let message = "Error updating product";
    let details: unknown = undefined;

    if (err.response) {
      message = 
        (typeof err.response.data?.message === "string" ? err.response.data.message :
        (typeof err.response.data?.error === "string" ? err.response.data.error :
        (status === 400 ? "Bad Request to V2 engine" : "Error updating product")));
      details = err.response.data?.errors;
    }

    // Log essential error details
    console.error(`Error updating product ${id} (Status: ${status}): ${message}`, { details: details, requestBody: requestBody });

    return NextResponse.json(
      { error: message, details: details },
      { status: status },
    );
  }
}

// DELETE /api/products/[id] - Delete a product by ID
export async function DELETE(
  _req: NextRequest,
  { params }: { params: { id: string } },
) {
  const { id } = params;

  if (isNaN(Number(id))) {
      return NextResponse.json({ error: "Invalid Product ID format" }, { status: 400 });
  }

  try {
    await v2EngineAxiosInstance.delete(`/products/${id}`);
    return new NextResponse(null, { status: 204 });

  } catch (error: unknown) {
    const err = error as APIErrorResponse;
    const status = err.response?.status || 500;
    let message = "Error deleting product";

    if (err.response) {
      message = (typeof err.response.data?.message === "string" && err.response.data?.message) ||
                (typeof err.response.data?.error === "string" && err.response.data?.error) ||
                "Error deleting product";
    }

    // Log essential error details
    console.error(`Error deleting product ${id} (Status: ${status}): ${message}`, err);

    return NextResponse.json(
      { error: message },
      { status: status }
    );
  }
}