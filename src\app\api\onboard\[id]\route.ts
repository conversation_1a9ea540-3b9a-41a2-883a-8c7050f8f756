import { NextRequest, NextResponse } from "next/server";
import { Client as MinioClient } from 'minio';
import axios from "axios";
import { v2EngineAxiosInstance } from "@/utils/axios";


export async function PUT(
    req: NextRequest,
    { params }: { params: { id: string } },
) {
    const { id } = params;
    const body = await req.json();
    const { status } = body;

    if (!status) {
        console.log("Status is missing in the request");
        return NextResponse.json(
            { error: "Status is required" },
            { status: 400 }
        );
    }

    try {
        const response = await v2EngineAxiosInstance.put(
            `/organizations/${id}/statuses?status=${status}`, // Send status as query param
        );
        console.log("Status update response:", response.data);
        return NextResponse.json(response.data);
    } catch (error) {
        console.error("Error updating organization status:", error);
        return NextResponse.json(
            { error: "Error updating organization status" },
            { status: 500 }
        );
    }
}