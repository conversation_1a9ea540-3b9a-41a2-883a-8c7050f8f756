export interface AdminUser {
    firstName: string;
    lastName: string;
    email: string;
}

export interface RegisteredUser {
    firstName: string;
    lastName: string;
    email: string;
    role: string;
}

export interface OnboardingStruct {
    id: number;
    isOrganization: boolean;
    dateApplied: string;
    status: string;
    email: string;
    companyName?: string;
    businessType?: string;
    tpin?: string;
    address?: string;
    phoneNumber: string;
    incorporationCertificate?: string[];
    adminUsers?: AdminUser[];
    registeredUsers?: RegisteredUser[];
    firstName?: string;
    lastName?: string;
}