"use client";

import DefaultLayout from "@/components/Layouts/DefaultLayout";
import User from "@/components/Users/<USER>";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast, ToastContainer } from "react-toastify";

interface User {
  id: number;
  email: string;
  phone_number: string;
  title: string;
  first_name: string;
  last_name: string;
  role_id: number;
  organization_id: number;
}

interface UserStruct {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  role_name: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  created_by: number;
  updated_by: number;
  deleted_by: number;
  last_login: string;
  organization_id: number;
  role_description: string;
  role_id: number;
  role_label: string;
  title: string;
}

const UserPage = () => {
  const router = useRouter();
  const params = useParams();
  const userId = Array.isArray(params.id)
    ? parseInt(params.id[0] || "0", 10)
    : parseInt(params.id || "0", 10);

  const [user, setUser] = useState<UserStruct>({
    id: 0,
    first_name: "",
    last_name: "",
    email: "",
    phone_number: "",
    role_name: "",
    created_at: "",
    updated_at: "",
    deleted_at: "",
    created_by: 0,
    updated_by: 0,
    deleted_by: 0,
    last_login: "",
    organization_id: 0,
    role_description: "",
    role_id: 0,
    role_label: "",
    title: "",
  });
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<any>(null);
  const [toastMessage, setToastMessage] = useState<string | null>(null);
  const [toastError, setToastError] = useState<string | null>(null);

  const [notFound, setNotFound] = useState<boolean>(false);
  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        if (userId) {
          const userResponse = await fetch(`/api/users/${userId}`, {
            method: "GET",
          });
          if (!userResponse.ok) {
            setNotFound(true);
            throw new Error(`HTTP error! status: ${userResponse.status}`);
          }
          const userData = await userResponse.json();
          setUser(userData);
        }
      } catch (error) {
        console.error("Error fetching user info:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserInfo();
  }, []);

  useEffect(() => {
    if (toastMessage) {
      toast.success(toastMessage, {
        position: "bottom-right",
      });
      setToastMessage(null);
    }
    if (toastError) {
      toast.error(toastError, {
        position: "bottom-right",
      });
      setToastError(null);
    }
  }, [toastMessage, toastError]);

  const handleUpdate = async (updatedUser: Partial<User>) => {
    if (updatedUser.role_id) {
      updatedUser.role_id = Number(updatedUser.role_id);
    }
    try {
      const response = await fetch("/api/users", {
        method: "PUT",
        body: JSON.stringify(updatedUser),
      });
      if (!response.ok) {
        setIsModalOpen(false);
        setToastError("Error updating user. Please try again.");
      } else {
        setIsModalOpen(false);
        setToastMessage("User updated successfully!");
        const userResponse = await fetch(`/api/users/${userId}`, {
          method: "GET",
        });
        if (!userResponse.ok) {
          setIsModalOpen(false);
          setToastError("Error fetching updated user. Please try again.");
        } else {
          const userData = await userResponse.json();
          setUser(userData);
        }
      }
    } catch (err) {
      console.error("Failed to update user: ", err);
      setToastError("Error updating user. Please try again.");
    }
  };

  const handleDelete = async (id: number) => {
    setLoading(true);
    try {
      const response = await fetch("/api/users", {
        method: "DELETE",
        body: JSON.stringify({ id }),
      });
      if (!response.ok) {
        setToastError("Error deleting user. Please try again.");
      } else {
        setToastMessage("User deleted successfully!");
        setTimeout(() => {
          router.push("/users");
        }, 5000); // Delay the redirection to allow the toast to be displayed
      }
    } catch (err) {
      setToastError("Error deleting user. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  if (notFound) {
    return (
      <DefaultLayout>
        <div className="flex min-h-screen items-center justify-center">
          <h1 className="text-2xl font-bold">404 - User Not Found</h1>
        </div>
      </DefaultLayout>
    );
  }

  return (
    <DefaultLayout>
      <User
        user={user}
        setIsModalOpen={setIsModalOpen}
        isModalOpen={isModalOpen}
        showPassword={showPassword}
        setShowPassword={setShowPassword}
        loading={loading}
        error={error}
        handleDelete={handleDelete}
        handleUpdate={handleUpdate}
      />
      <ToastContainer />
    </DefaultLayout>
  );
};

export default UserPage;
