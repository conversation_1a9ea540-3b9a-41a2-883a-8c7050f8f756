import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});


const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript", "plugin:diff/diff"),
  {
    rules: {
      "max-len": ["warn", { code: 120 }], // Set the maximum line length to 120 characters and flag as warning
      "@next/next/no-img-element": "warn", // Flag <img> usage as warning
      "@next/next/google-font-display": "warn", // Flag missing font-display as warning
      "@next/next/no-page-custom-font": "warn", // Flag custom fonts not added in _document.js as warning
      "@next/next/no-html-link-for-pages": "warn", // Flag <a> element usage for navigation as warning
      "@typescript-eslint/no-explicit-any": "warn", // Flag usage of 'any' type as warning
      "@typescript-eslint/no-unused-vars": "warn", // Flag unused variables as warning
      "@typescript-eslint/no-unsafe-assignment": "error", // Flag unsafe assignments as error
      "@typescript-eslint/no-unsafe-call": "error", // Flag unsafe calls as error
      "@typescript-eslint/no-unsafe-member-access": "error", // Flag unsafe member access as error
      "@typescript-eslint/no-unsafe-return": "error", // Flag unsafe returns as error
      "@typescript-eslint/no-mixed-enums": "error", // Flag mixed enums as error
    },
  },
  {
    languageOptions: {
      parserOptions: {
        projectService: true,
        tsconfigRootDir: __dirname,
      },
    },
  },
];

export default eslintConfig;