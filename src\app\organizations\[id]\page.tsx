"use client";

import React, { useEffect, useState } from "react";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import Organization from "@/components/Organizations/Organization";
import { useParams } from "next/navigation";

interface OrganizationStruct {
  name: string;
  registration_number: string;
  tax_id: string;
  organization_type_id: number;
  parent_id: number;
  email: string;
  phone_number: string;
  postal_address: string;
  town_id: number;
  street_address: string;
  id: number;
  created_by: number;
  updated_by: number;
  deleted_by: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

const OrganizationPage = () => {
  const [organization, setOrganization] = useState<OrganizationStruct>({
    name: "",
    registration_number: "",
    tax_id: "",
    organization_type_id: 0,
    parent_id: 0,
    email: "",
    phone_number: "",
    postal_address: "",
    town_id: 0,
    street_address: "",
    id: 0,
    created_by: 0,
    updated_by: 0,
    deleted_by: 0,
    created_at: "",
    updated_at: "",
    deleted_at: "",
  });
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const params = useParams();
  const organizationId = Array.isArray(params.id)
    ? parseInt(params.id[0] || "0", 10)
    : parseInt(params.id || "0", 10);

  useEffect(() => {
    const fetchOrganizationInfo = async () => {
      try {
        if (organizationId) {
          const response = await fetch(`/api/organizations/${organizationId}`, {
            method: "GET",
          });
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          const result = await response.json();
          setOrganization(result);
        }
      } catch (err) {
        setError("Failed to fetch organization information");
        console.error("Error fetching single organization:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchOrganizationInfo();
  }, [organizationId]);

  const handleUpdate = async (
    updatedOrganization: Partial<OrganizationStruct>,
  ) => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/organizations/${updatedOrganization.id}`,
        {
          method: "PUT",
          body: JSON.stringify(updatedOrganization),
        },
      );
      if (!response.ok) {
        throw new Error("Failed to update organization");
      }
      console.log("response\n", response);
      // const result = await response.json();
      setIsModalOpen(false);
    } catch (err) {
      setError("Failed to update organization");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    setLoading(true);
    try {
    } catch (err) {
      setError("Failed to delete organization");
    } finally {
      setLoading(false);
    }
  };

  return (
    <DefaultLayout>
      <Organization
        organization={organization}
        handleDelete={handleDelete}
        handleUpdate={handleUpdate}
        loading={loading}
        error={error}
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
      />
    </DefaultLayout>
  );
};

export default OrganizationPage;
