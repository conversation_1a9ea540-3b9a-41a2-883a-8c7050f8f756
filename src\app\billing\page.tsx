"use client";

import { useEffect, useState } from "react";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import { InvoiceStruct } from "@/schemas/billingSchema";
import Billing from "@/components/Billing/Billing";
import { useSession } from "next-auth/react";

const BillingPage = () => {
  const [invoices, setInvoices] = useState<InvoiceStruct[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  const { data: session, status } = useSession();
  const accessToken = session?.user?.token;

  useEffect(() => {
    const fetchBillingData = async () => {
      if (session && session.user.organizationId === null) {
        // Use the user endpoint to fetch invoices
        try {
          const userInvoicesResponse = await fetch(
            `/api/billing/invoices?userId=${session?.user?.id}`,
            {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${accessToken}`,
              },
            },
          );
          const invoicesData = (await userInvoicesResponse.json()) as {
            data: InvoiceStruct[];
          };
          setInvoices(invoicesData.data);
        } catch (error) {
          setError(error instanceof Error ? error.message : String(error));
          console.error("Error fetching user invoices:", error);
        } finally {
          setLoading(false);
        }
        return;
      }
      // If the user has an organization, fetch organization invoices
      try {
        const organizationInvoicesResponse = await fetch(
          `/api/billing/invoices?organizationId=${session?.user?.organizationId}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${accessToken}`,
            },
          },
        );
        const invoicesData = (await organizationInvoicesResponse.json()) as {
          data: InvoiceStruct[];
        };
        setInvoices(invoicesData.data);
      } catch (error) {
        setError(error instanceof Error ? error.message : String(error));
        console.error("Error fetching organization invoices:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchBillingData();
  }, []);

  const handleCancellation = async (id: number) => {
    setLoading(true);
    try {
      console.log("Cancelling invoice with id: ", id);
    } catch (err) {
      setError("Failed to cancel invoice");
    } finally {
      setLoading(false);
    }
  };

  return (
    <DefaultLayout>
      <Breadcrumb pageName="Billing" />
      {error && <div className="text-red-500">{error}</div>}
      <Billing
        invoices={invoices}
        loading={loading}
        error={error}
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        handleCancellation={handleCancellation}
      />
    </DefaultLayout>
  );
};

export default BillingPage;
