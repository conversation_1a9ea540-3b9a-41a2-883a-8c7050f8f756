import { z } from "zod";

export const organizationDetailsSchema = z.object({
    companyName: z.string().min(1, "Company name is required"),
    organizationEmail: z.string().email("Invalid email address"),
    tpin: z.string().length(10, "TPIN must be exactly 10 digits").regex(/^\d+$/, "TPIN must be a number"),
    phoneNumber: z.string().min(1, "Phone number is required"),
    address: z.object({
        street: z.string().min(1, "Street is required"),
        city: z.string().min(1, "City is required"),
        state: z.string().min(1, "State is required"),
        zip_code: z.string().min(1, "Zip code is required"),
        country: z.string().min(1, "Country is required"),
    }),
    businessTypeId: z.number().min(1, "Business type is required"),
    incorporationDocuments: z.array(z.instanceof(File).refine(file => file !== null, "File is required"))
});

export const adminUserSchema = z.object({
    adminUserTitle: z.string().min(1, "Title is required"),
    adminUserFirstName: z.string().min(1, "Admin first name is required"),
    adminUserLastName: z.string().min(1, "Admin last name is required"),
    adminUserEmail: z.string().email("Invalid admin email address"),
});

export const registeredUserSchema = z.object({
    registeredUserTitle: z.string().min(1, "Title is required"),
    registeredUserFirstName: z.string().min(1, "User first name is required"),
    registeredUserLastName: z.string().min(1, "User last name is required"),
    registeredUserEmail: z.string().email("Invalid user email address"),
    registeredUserRoleId: z.number().min(1, "User role is required"),
});


