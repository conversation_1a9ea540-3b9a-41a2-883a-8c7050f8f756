import { Product, ProductCustomDetail, UploadedImageData, ImageUploadStatus, VehicleDetailsType, Currency } from './index';
import React from 'react';

export interface ProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (productData: Partial<Product> & { imagesData?: UploadedImageData[] }) => void;
  currentProduct?: Partial<Product> | null;
}

export interface ProductDetailsSectionProps {
  productDetails: ProductCustomDetail[];
  onAddDetail: () => void;
  onChangeDetail: (index: number, field: keyof ProductCustomDetail, value: string) => void;
  onRemoveDetail: (index: number) => void;
  isLoading?: boolean;
}

export interface ProductImageSectionProps {
  imageUploads: ImageUploadStatus[];
  onImageUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onRemoveImage: (localId: string) => void;
  isEditing: boolean;
  isSubmitting?: boolean;
}

export interface ProductBasicFormSectionProps {
  productData: Partial<Product>;
  currencies: Currency[];
  loadingCurrencies: boolean;
  currencyError: string | null;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  isLoading?: boolean;
}

export interface VehicleSelectorSectionProps {
  isVehicleSelectorChecked: boolean;
  setIsVehicleSelectorChecked: (checked: boolean) => void;
  vehicleDetails: VehicleDetailsType;
  setIsVehicleModalOpen: (isOpen: boolean) => void;
  variant?: number;
  isLoading?: boolean;
}

export interface ProductCustomDetailsSectionProps {
  productDetails: ProductCustomDetail[];
  onAddDetail: () => void;
  onChangeDetail: (index: number, field: keyof ProductCustomDetail, value: string) => void;
  onRemoveDetail: (index: number) => void;
}
