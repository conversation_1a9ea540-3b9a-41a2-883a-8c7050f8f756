import { NextRequest, NextResponse } from "next/server";
import { randomUUID } from "crypto";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { invoiceId, total } = body;
    const clientId = process.env.PRIMENET_CLIENT_ID;
    const baseUrl = process.env.PRIMENET_BASE_URL;

    if (!clientId || !total) {
      return NextResponse.json(
        { error: "Missing required parameters for Primenet payment" },
        { status: 400 },
      );
    }

    const externalId = `kuala-${randomUUID()}-${invoiceId}`;

    const url = `${baseUrl}/payment-checkout/${clientId}/${total}/${externalId}/card/ZMW`;

    return NextResponse.json({ url }, { status: 200 });
  } catch (error) {
    console.error("Error creating payment link:", error);
    return NextResponse.json(
      { error: "Error creating payment link" },
      { status: 500 },
    );
  }
}