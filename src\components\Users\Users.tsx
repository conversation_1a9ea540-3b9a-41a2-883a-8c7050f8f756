import React, { useState } from "react";
import { create<PERSON><PERSON>umn<PERSON><PERSON><PERSON>, SortingState } from "@tanstack/react-table";
import "@/css/table.css";
import DebouncedSearchInput from "@/components/Inputs/DebouncedSearchInput";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faUserEdit,
  faEye,
  faMagnifyingGlass,
  faTrash,
} from "@fortawesome/free-solid-svg-icons";
import TanStackTable from "@/components/Tables/TanStackTable";
import ConfirmModal from "../Modals/ConfirmModal";
import Link from "next/link";
import { getPageSizeOptions } from "../Tables/utils";
import UserModal from "../Modals/UserModal";

interface OrganizationUser {
  id: number;
  first_name: string;
  last_name: string;
  role_name: string;
  email: string;
  phone_number: string;
}

interface User {
  id: number;
  email: string;
  phone_number: string;
  title: string;
  first_name: string;
  last_name: string;
  role_id: number;
  organization_id: number;
}

interface UsersProps {
  organizationUsers: OrganizationUser[];
  loading: boolean;
  error: string | null;
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  handleSubmit: (user: Partial<User>) => void;
  handleUpdate: (updatedUser: Partial<User>) => void;
  handleDelete: (id: number) => void;
}

const Users: React.FC<UsersProps> = ({
  organizationUsers,
  loading,
  error,
  isModalOpen,
  setIsModalOpen,
  handleSubmit,
  handleUpdate,
  handleDelete,
}) => {
  const initialUser: Partial<User> = {
    email: "",
    phone_number: "",
    title: "",
    first_name: "",
    last_name: "",
    role_id: 0,
    organization_id: 0,
  };

  const [newUser, setNewUser] = useState<Partial<User>>(initialUser);

  const [selectedUser, setSelectedUser] = useState<Partial<User> | null>(null);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState<boolean>(false);
  const [userToDelete, setUserToDelete] = useState<number | null>(null);

  const openUpdateModal = (user: OrganizationUser) => {
    setSelectedUser(user);
    setNewUser(user);
    setIsModalOpen(true);
  };

  const openDeleteModal = (id: number) => {
    setUserToDelete(id);
    setIsConfirmModalOpen(true);
  };

  const confirmDelete = () => {
    if (userToDelete !== null) {
      handleDelete(userToDelete);
      setUserToDelete(null);
      setIsConfirmModalOpen(false);
    }
  };

  const getUserFullnameById = (id: number): string => {
    const user = organizationUsers?.find(
      (usr: OrganizationUser) => usr.id === id,
    );
    return user ? `${user.first_name} ${user.last_name}` : "Unknown";
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target;
    setNewUser((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handlePhoneChange = (value: string) => {
    setNewUser((prevData) => ({
      ...prevData,
      phone_number: value,
    }));
  };

  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedUser) {
      handleUpdate(newUser);
    } else {
      handleSubmit(newUser);
    }
    setNewUser(initialUser);
  };

  const [globalFilter, setGlobalFilter] = useState<string>("");
  const [sorting, setSorting] = useState<SortingState>([]);
  const columnHelper = createColumnHelper<OrganizationUser>();

  const columns = [
    columnHelper.accessor("first_name", {
      cell: (info) => (
        <div className="max-width-cell truncated-cell">
          {info?.getValue() || "N/A"}
        </div>
      ),
      header: "First Name",
    }),
    columnHelper.accessor("last_name", {
      cell: (info) => (
        <div className="max-width-cell truncated-cell">
          {info?.getValue() || "N/A"}
        </div>
      ),
      header: "Last Name",
    }),
    columnHelper.accessor("email", {
      cell: (info) => (
        <div className="max-width-cell truncated-cell">
          {info?.getValue() || "N/A"}
        </div>
      ),
      header: "Email",
    }),
    columnHelper.accessor("role_name", {
      cell: (info) => (
        <div className="max-width-cell">{info?.getValue() || "N/A"}</div>
      ),
      header: "Role",
    }),
    columnHelper.display({
      id: "actions",
      cell: (info) => (
        <div className="flex space-x-4">
          <Link
            href={`users/${info.row.original.id}`}
            className="text-blue-500 hover:text-blue-700"
            title="View"
          >
            <FontAwesomeIcon icon={faEye} />
          </Link>
          <button
            onClick={() => openUpdateModal(info.row.original)}
            className="text-yellow-500 hover:text-yellow-700"
            title="Update"
          >
            <FontAwesomeIcon icon={faUserEdit} />
          </button>
          <button
            onClick={() => openDeleteModal(info.row.original.id)}
            className="text-red-500 hover:text-red-700"
            title="Cancel"
          >
            <FontAwesomeIcon icon={faTrash} />
          </button>
        </div>
      ),
      header: "Actions",
    }),
  ];

  if (loading) return <div>Loading...</div>;
  if (error) return <div className="text-red-500">Error: {error}</div>;

  return (
    <div className="flex flex-col pt-4">
      <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
          {/* {Search and Add User} */}
          <div className="mb-2 flex justify-between">
            <div className="flex w-full items-center gap-1">
              <FontAwesomeIcon icon={faMagnifyingGlass} />
              <DebouncedSearchInput
                value={globalFilter ?? ""}
                onChange={(value) => setGlobalFilter(String(value))}
                debounced={500}
                placeholder="Search users..."
              />
            </div>
            <div className="flex flex-col items-start gap-2">
              <div className="flex items-center gap-2">
                <button
                  className="ml-2 inline-flex items-center whitespace-nowrap rounded bg-wbv-theme px-4 py-2 text-xs font-semibold text-white hover:bg-opacity-90"
                  onClick={() => {
                    setNewUser(initialUser);
                    setSelectedUser(null);
                    setIsModalOpen(true);
                  }}
                >
                  <span>Add User</span>
                </button>
              </div>
            </div>
            {/* Modal */}
            {isModalOpen && (
              <UserModal
                selectedUser={selectedUser}
                handleChange={handleChange}
                newUser={newUser}
                handlePhoneChange={handlePhoneChange}
                onSubmit={onSubmit}
                setIsModalOpen={setIsModalOpen}
              />
            )}
          </div>
          {/* Confirm Delete Modal */}
          <ConfirmModal
            isOpen={isConfirmModalOpen}
            onClose={() => setIsConfirmModalOpen(false)}
            onConfirm={confirmDelete}
            title="Confirm Delete"
            message={`Are you sure you want to delete the user <strong>${userToDelete !== null ? getUserFullnameById(userToDelete) : "Unknown"}</strong>?`}
            cta="Delete"
            ctaBtnColor="danger"
          />
          {/* Users Table */}
          {loading ? (
            <p>Loading...</p>
          ) : error ? (
            <p className="text-red-500">{error}</p>
          ) : (
            <div className="my-5 overflow-hidden border-b border-gray-200 shadow">
              <TanStackTable
                data={organizationUsers}
                columns={columns}
                globalFilter={globalFilter}
                sorting={sorting}
                setGlobalFilter={setGlobalFilter}
                setSorting={setSorting}
                pageSizeOptions={getPageSizeOptions(organizationUsers)}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Users;
