import { NextRequest, NextResponse } from "next/server";
import { v2EngineAxiosInstance } from "@/utils/axios";

export async function GET(
    req: NextRequest,
    { params }: { params: { id: string } },
) {
    const { id } = params;

    try {
        const response = await v2EngineAxiosInstance.get(`/organizations/${Number(id)}/admins`);

        const organizationUsers = response.data;
        return NextResponse.json(organizationUsers);
    } catch (error) {
        console.error("Error fetching organization users:", error);
        return NextResponse.json(
            { error: "Error fetching organization users" },
            { status: 500 },
        );
    }
}