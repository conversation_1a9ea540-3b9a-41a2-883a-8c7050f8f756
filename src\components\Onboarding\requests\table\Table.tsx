import React from "react";
import { createColumnHelper } from "@tanstack/react-table";
import Link from "next/link";
import { OnboardingStruct } from "@/schemas/onboardingSchema";

const columnHelper = createColumnHelper<OnboardingStruct>();

export const getColumns = (
    approveApplication: (id: number) => void,
    rejectApplication: (id: number) => void,
    isOrganization: boolean
) => {
    const commonColumns = [
        columnHelper.accessor("email", {
            cell: (info) => <div>{info.getValue()}</div>,
            header: "Email",
        }),
        columnHelper.accessor("dateApplied", {
            cell: (info) => <div>{info.getValue()}</div>,
            header: "Date Applied",
        }),
    ];

    const organizationColumns = [
        columnHelper.accessor("companyName", {
            cell: (info) => <div>{info.getValue()}</div>,
            header: "Company Name",
        }),
        columnHelper.accessor("businessType", {
            cell: (info) => <div>{info.getValue()}</div>,
            header: "Business Type",
        }),
    ];

    const individualColumns = [
        columnHelper.accessor("firstName", {
            cell: (info) => <div>{info.getValue()}</div>,
            header: "First Name",
        }),
        columnHelper.accessor("lastName", {
            cell: (info) => <div>{info.getValue()}</div>,
            header: "Last Name",
        }),
    ];

    const statusAndActionsColumns = [
        columnHelper.accessor("status", {
            cell: (info) => {
                const status = info.getValue();
                let statusClass = "";
                if (status === "Pending") {
                    statusClass = "bg-yellow-100 text-yellow-800";
                } else if (status === "Approved") {
                    statusClass = "bg-green-100 text-green-800";
                } else if (status === "Rejected") {
                    statusClass = "bg-red-100 text-red-800";
                }
                return (
                    <span className={`px-2 py-1 rounded-full text-xs font-semibold ${statusClass}`}>
                        {status}
                    </span>
                );
            },
            header: "Status",
        }),
        columnHelper.display({
            id: "actions",
            cell: (info) => (
                <div className="flex space-x-4">
                    <Link
                        href={`/onboarding/requests/${info.row.original.id}`}
                        title="View Request"
                        className="text-blue-500 underline"
                    >
                        View Request
                    </Link>
                </div>
            ),
            header: "Actions",
        }),
    ];

    return [
        columnHelper.accessor("id", {
            cell: (info) => <div>{info.getValue()}</div>,
            header: "ID",
        }),
        ...(isOrganization ? organizationColumns : individualColumns),
        ...commonColumns,
        ...statusAndActionsColumns,
    ];
};