import { NextRequest, NextResponse } from "next/server";
import { v2EngineAxiosInstance } from "@/utils/axios";

export async function GET(req: NextRequest) {
  try {
    const response = await v2EngineAxiosInstance.get("/searches");
    const data = response.data.map((item: any) => ({
      ID: item.ID,
      CreatedAt: item.CreatedAt,
      UpdatedAt: item.UpdatedAt,
      DeletedAt: item.DeletedAt,
      user_id: item.user_id,
      search_type: item.search_type,
      search_term: JSON.parse(item.search_term),
    }));
    const result = { data: data };
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error fetching searches", error);
    return NextResponse.json(
      { error: "Error fetching searches" },
      { status: 500 },
    );
  }
}
