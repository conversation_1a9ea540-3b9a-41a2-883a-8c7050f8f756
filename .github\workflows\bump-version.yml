name: Bump version on main branch
on:
  push:
    branches:
      - main

permissions:
  actions: write
  contents: read

jobs:
  prepare:
    name: get tags
    if:
      github.event_name == 'push' &&
      startsWith(github.event.head_commit.message, 'chore(version)') != true
    runs-on: ubuntu-latest
    outputs:
      head_tag: ${{ steps.check.outputs.head_tag }}
      foreign_pr: ${{ steps.check.outputs.foreign_pr }}
      has_tag: ${{ steps.check.outputs.has_tag }}
      must_tag: ${{ steps.check.outputs.must_tag }}
    steps:
      - name: checkout
        uses: actions/checkout@v4
        with:
          fetch-tags: 'true'

      - name: retrieve tags
        run: git fetch --depth=1 origin +refs/tags/*:refs/tags/*

      - name: set output variables
        id: check
        # Check if the push is a tag or if it's from another repo
        run: |
          tag=""
          if [[ "${{ github.ref }}" == refs/heads/* ]]; then
            tag="$(git tag --points-at HEAD)"
          fi
          echo "head_tag=${tag}" >> $GITHUB_OUTPUT
          echo "has_tag=$(git tag --contains ${{ github.sha }} | wc -l)" >> $GITHUB_OUTPUT
          echo "must_tag=$(git log -1 --format=%B | grep -e "\stag\s" | wc -l)" >> $GITHUB_OUTPUT

      - name: workflow initiator
        run: |
          echo "Event: ${{ github.event_name }}"
          echo "Branch or tag: ${{ github.ref_name }}"
          echo "Actor: ${{ github.actor }}"

  calculate-version:
    name: get gitversion
    runs-on: ubuntu-latest
    needs: prepare
    if:
      github.event_name == 'push' &&
      startsWith(github.event.head_commit.message, 'chore(version)') != true
    outputs:
      semVer: ${{ steps.gitversion.outputs.majorMinorPatch }}
      fullSemVer: ${{ steps.gitversion.outputs.fullSemVer }}
      sha: ${{ steps.gitversion.outputs.sha }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: install gitversion
        uses: gittools/actions/gitversion/setup@v3.1.1
        with:
          versionSpec: '6.0.x'

      - name: run gitversion
        id: gitversion
        uses: gittools/actions/gitversion/execute@v3.1.1
        with:
          useConfigFile: true

  bump-version:
    name: bump version
    runs-on: ubuntu-latest
    needs: [prepare, calculate-version]
    if:
      github.event_name == 'push' && needs.prepare.outputs.head_tag !=
      needs.calculate-version.outputs.semVer
    permissions:
      contents: write
    steps:
      - name: checkout repo
        uses: actions/checkout@v4

      - name: bump version to ${{ env.SEM_VER }}
        env:
          SEM_VER: ${{ needs.calculate-version.outputs.semVer }}
        run: echo $SEM_VER > version

      - name: commit changes
        env:
          FULL_SEM_VER: ${{ needs.calculate-version.outputs.fullSemVer }}
          SEM_VER: ${{ needs.calculate-version.outputs.semVer }}
        run: |
          git config --local user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"
          git commit -am "chore(version): bump version to $SEM_VER"
          git tag -a -m "Version: $FULL_SEM_VER" "v$SEM_VER"

      - name: merge to main branch
        uses: CasperWA/push-protected@v2
        with:
          # Depending on branch protection rules, a  manually populated
          # `GITHUB_TOKEN_WORKAROUND` secret with permissions to push to
          # a protected branch must be used. This secret can have an arbitrary
          # name, as an example, this repository uses `DOTTBOTT_TOKEN`.
          #
          # When using a custom token, it is recommended to leave the following
          # comment for other developers to be aware of the reasoning behind it:
          #
          # This must be used as GitHub Actions token does not support pushing
          # to protected branches.
          token: ${{ secrets.DOTTBOTT_TOKEN }}
          branch: main
          timeout: 5
          tags: 'true'
