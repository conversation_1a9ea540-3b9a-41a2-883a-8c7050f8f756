import React from 'react';

interface FormHeaderProps {
    title?: string;
}

const WBVFormHeader: React.FC<FormHeaderProps> = (props) => {
    return (
        <div className="mb-10 text-center" {...props}>
            <img
                src="/images/logo/whitebook-value-logo.png"
                alt="Logo"
                className="mx-auto max-h-45"
            />
            <h2 className="text-center text-2xl font-bold text-gray-900">
                {props.title}
            </h2>
            <hr
                className="mx-auto mt-4 w-full border-0"
                style={{
                    height: "1px",
                    background:
                        "linear-gradient(to right, transparent, #06b6d4, transparent)",
                }}
            />
        </div>
    );
};

export default WBVFormHeader;