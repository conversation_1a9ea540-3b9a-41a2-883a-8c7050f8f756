import React, { useEffect, useState } from 'react';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUserEdit, faUserPlus } from "@fortawesome/free-solid-svg-icons";
import { Listbox, ListboxButton, ListboxOption, ListboxOptions, Transition } from '@headlessui/react';
import { SelectorIcon, CheckIcon } from '@heroicons/react/solid';

interface Role {
    id: number;
    name: string;
    label: string;
    description: string;
    permissions: Array<{
        id: number;
        name: string;
        description: string;
        created_at: string;
        updated_at: string;
        deleted_at: string | null;
    }>;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
}

interface User {
    id?: number;
    title: string;
    first_name: string;
    last_name: string;
    email: string;
    role_id: number;
}

interface UserFormData {
    title: string;
    firstName: string;
    lastName: string;
    email: string;
    roleId: number;
}

interface StaffMembersModalProps {
    isOpen: boolean;
    onClose: () => void;
    title: string;
    currentUser: User | null;
    onSubmit: (userData: UserFormData) => void;
}

const titles = ["Mr", "Ms", "Mrs", "Dr", "Prof"];

const StaffMembersModal: React.FC<StaffMembersModalProps> = ({
    isOpen,
    onClose,
    title,
    currentUser,
    onSubmit
}) => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [roles, setRoles] = useState<Role[]>([]);

    // Form state
    const [userTitle, setUserTitle] = useState(currentUser?.title || '');
    const [userFirstName, setUserFirstName] = useState(currentUser?.first_name || '');
    const [userLastName, setUserLastName] = useState(currentUser?.last_name || '');
    const [userEmail, setUserEmail] = useState(currentUser?.email || '');
    const [userRoleId, setUserRoleId] = useState(currentUser?.role_id || 0);

    useEffect(() => {
        // Update form state when currentUser changes
        if (currentUser) {
            setUserTitle(currentUser.title || '');
            setUserFirstName(currentUser.first_name || '');
            setUserLastName(currentUser.last_name || '');
            setUserEmail(currentUser.email || '');
            setUserRoleId(currentUser.role_id || 0);
        } else {
            setUserTitle('');
            setUserFirstName('');
            setUserLastName('');
            setUserEmail('');
            setUserRoleId(0);
        }
    }, [currentUser]);

    useEffect(() => {
        const fetchRolesData = async () => {
            try {
                setLoading(true);
                const response = await fetch("/api/roles", {
                    method: "GET",
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => null);
                    const errorMessage = errorData?.message || `HTTP error! status: ${response.status}`;
                    throw new Error(errorMessage);
                }

                const result = await response.json();

                // Check if data exists and is an array
                if (!result.data || !Array.isArray(result.data)) {
                    setRoles([]);
                    throw new Error('Invalid response format: missing data array');
                }

                const filteredRoles = result.data.filter(
                    (role: Role) => ['reviewer', 'approver', 'individual'].includes(role.name.toLowerCase())
                );

                setRoles(filteredRoles);
                setError(null);
            } catch (err) {
                console.error("Error fetching roles:", err);
                setError(err instanceof Error ? err.message : 'Failed to load roles');
                // Don't close the modal automatically, let the user decide
            } finally {
                setLoading(false);
            }
        };

        if (isOpen) {
            fetchRolesData();
        }
    }, [isOpen]); // Only depend on isOpen, not onClose

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSubmit({
            title: userTitle,
            firstName: userFirstName,
            lastName: userLastName,
            email: userEmail,
            roleId: userRoleId
        });
    };

    if (!isOpen) return null;

    if (loading) {
        return (
            <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50">
                <div className="rounded-lg bg-white p-6 shadow-lg">
                    <p className="text-center">Loading roles...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50 p-4">
            <div className="w-full max-w-2xl rounded-lg bg-white p-6 shadow-lg">
                <div className="flex flex-col items-center">
                    <FontAwesomeIcon
                        icon={currentUser ? faUserEdit : faUserPlus}
                        className="mb-2 text-3xl text-wbv-theme"
                    />
                    <h2 className="mb-2 text-center text-xl font-bold">
                        {title}
                    </h2>
                    <div className="mb-4 h-1 w-full bg-gradient-to-r from-transparent via-wbv-theme to-transparent"></div>
                </div>

                {error && (
                    <div className="mb-4 rounded-md bg-red-50 p-4">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-red-800">Error loading roles</h3>
                                <div className="mt-2 text-sm text-red-700">
                                    <p>{error}</p>
                                    <p className="mt-1">You can still proceed with the form, but role selection may be limited.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                <form onSubmit={handleSubmit}>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        <div className="flex flex-col">
                            <label className="mb-1 block text-sm font-medium text-gray-700">Title <span className="text-red-500">*</span></label>
                            <Listbox value={userTitle} onChange={setUserTitle}>
                                <div className="relative mt-1">
                                    <ListboxButton className={`relative w-full cursor-default rounded-md border border-gray-300 py-2 pl-3 pr-10 text-left shadow-md focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-11 ${userTitle ? "bg-[#e8f0fe]" : ""}`} style={{ fontSize: "14px" }}>
                                        <span className="block truncate">{userTitle || "Select Title"}</span>
                                        <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                            <SelectorIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                                        </span>
                                    </ListboxButton>
                                    <Transition
                                        as={React.Fragment}
                                        leave="transition ease-in duration-100"
                                        leaveFrom="opacity-100"
                                        leaveTo="opacity-0"
                                    >
                                        <ListboxOptions className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none md:text-md" style={{ fontSize: "14px" }}>
                                            {titles.map((title) => (
                                                <ListboxOption
                                                    key={title}
                                                    className={({ active }) =>
                                                        `relative cursor-default select-none py-2 pl-10 pr-4 ${active ? "bg-[#f1f1f1] text-gray-700" : "text-gray-900"
                                                        }`}
                                                    value={title}
                                                >
                                                    {({ selected }) => (
                                                        <>
                                                            <span
                                                                className={`block truncate ${selected ? "font-medium" : "font-normal"
                                                                    }`}
                                                            >
                                                                {title}
                                                            </span>
                                                            {selected ? (
                                                                <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-white">
                                                                    <CheckIcon className="h-5 w-5" aria-hidden="true" />
                                                                </span>
                                                            ) : null}
                                                        </>
                                                    )}
                                                </ListboxOption>
                                            ))}
                                        </ListboxOptions>
                                    </Transition>
                                </div>
                            </Listbox>
                        </div>

                        <div className="flex flex-col">
                            <label className="mb-1 block text-sm font-medium text-gray-700">First Name <span className="text-red-500">*</span></label>
                            <input
                                type="text"
                                value={userFirstName}
                                onChange={(e) => setUserFirstName(e.target.value)}
                                className={`mt-1 w-full rounded-md shadow-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-wbv-theme ${userFirstName ? "bg-[#e8f0fe]" : ""}`}
                                style={{ fontSize: "14px" }}
                                required
                            />
                        </div>

                        <div className="flex flex-col">
                            <label className="mb-1 block text-sm font-medium text-gray-700">Last Name <span className="text-red-500">*</span></label>
                            <input
                                type="text"
                                value={userLastName}
                                onChange={(e) => setUserLastName(e.target.value)}
                                className={`mt-1 w-full rounded-md shadow-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-wbv-theme ${userLastName ? "bg-[#e8f0fe]" : ""}`}
                                style={{ fontSize: "14px" }}
                                required
                            />
                        </div>

                        <div className="flex flex-col">
                            <label className="mb-1 block text-sm font-medium text-gray-700">Email <span className="text-red-500">*</span></label>
                            <input
                                type="email"
                                value={userEmail}
                                onChange={(e) => setUserEmail(e.target.value)}
                                className={`mt-1 w-full rounded-md shadow-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-wbv-theme ${userEmail ? "bg-[#e8f0fe]" : ""}`}
                                style={{ fontSize: "14px" }}
                                required
                            />
                        </div>

                        <div className="flex flex-col sm:col-span-2">
                            <label className="mb-1 block text-sm font-medium text-gray-700">User Role <span className="text-red-500">*</span></label>
                            <Listbox value={userRoleId} onChange={setUserRoleId}>
                                <div className="relative mt-1">
                                    <ListboxButton className={`relative w-full cursor-default rounded-md border border-gray-300 py-2 pl-3 pr-10 text-left shadow-md focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-11 ${userRoleId ? "bg-[#e8f0fe]" : ""}`} style={{ fontSize: "14px" }}>
                                        <span className="block truncate">
                                            {(() => {
                                                const selectedRole = roles.find(role => role.id === userRoleId);
                                                return selectedRole ? selectedRole.name.charAt(0).toUpperCase() + selectedRole.name.slice(1) : "Select User Role";
                                            })()}
                                        </span>
                                        <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                            <SelectorIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                                        </span>
                                    </ListboxButton>
                                    <Transition
                                        as={React.Fragment}
                                        leave="transition ease-in duration-100"
                                        leaveFrom="opacity-100"
                                        leaveTo="opacity-0"
                                    >
                                        <ListboxOptions className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none md:text-md" style={{ fontSize: "14px" }}>
                                            {roles.map((role) => (
                                                <ListboxOption
                                                    key={role.id}
                                                    className={({ active }) =>
                                                        `relative cursor-default select-none py-2 pl-10 pr-4 ${active ? "bg-[#f1f1f1] text-gray-700" : "text-gray-900"
                                                        }`}
                                                    value={role.id}
                                                >
                                                    {({ selected }) => (
                                                        <>
                                                            <span
                                                                className={`block truncate ${selected ? "font-medium" : "font-normal"
                                                                    }`}
                                                            >
                                                                {role.name.charAt(0).toUpperCase() + role.name.slice(1)}
                                                            </span>
                                                            {selected ? (
                                                                <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-white">
                                                                    <CheckIcon className="h-5 w-5" aria-hidden="true" />
                                                                </span>
                                                            ) : null}
                                                        </>
                                                    )}
                                                </ListboxOption>
                                            ))}
                                        </ListboxOptions>
                                    </Transition>
                                </div>
                            </Listbox>
                        </div>
                    </div>
                    <div className="mt-4 flex justify-center">
                        <button
                            type="button"
                            onClick={onClose}
                            className="mr-2 rounded bg-gray-500 p-2 text-white hover:bg-gray-600"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            className="rounded bg-wbv-theme p-2 text-white hover:bg-opacity-90"
                        >
                            {currentUser ? "Update User" : "Add User"}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default StaffMembersModal;