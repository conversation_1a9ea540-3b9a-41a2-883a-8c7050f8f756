// Main product types
export interface ProductCustomDetail {
  customFieldName: string;
  fieldType: string;
  value: string;
}

export interface Product {
  id: number;
  name: string;
  productType: string;
  currency_id: number;
  currency_code: string;
  price: number;
  variant?: number;
  description: string;
  productDetails?: ProductCustomDetail[];
  images?: File[] | string[];
  shop_id?: number;
  created_at?: string;
  updated_at?: string;
}

export interface Currency {
  id: number;
  code: string;
  name?: string;
}

// Interface for the data returned by the upload API
export interface UploadedImageData {
  id?: number; // API might not return ID immediately
  name: string;
  type: string;
  is_default: boolean;
  file: string;
  path: string;
}

// Interface to track upload status locally
export interface ImageUploadStatus extends UploadedImageData {
  _localId: string; // Temporary local ID for tracking
  status: 'uploading' | 'success' | 'error';
  errorMessage?: string;
  originalFile?: File; // Keep original file for potential preview
  isExisting?: boolean; // Flag to identify existing images
  markedForDeletion?: boolean; // Flag to mark images for deletion without actually deleting them
}

// Vehicle details type
export interface VehicleDetailsType {
  submodelId: number | null;
  manufacturerName: string;
  modelName: string;
  year: number | null;
  submodelName: string;
  displayText: string;
}

export interface ProductApiResponse {
  id?: number;
  data?: {
    id?: number;
  };
};