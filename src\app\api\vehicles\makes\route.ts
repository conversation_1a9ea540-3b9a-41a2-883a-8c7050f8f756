import { NextRequest, NextResponse } from "next/server";
import { v2EngineAxiosInstance } from "@/utils/axios";

export async function GET(req: NextRequest) {
  try {
    // console.log("fetching makes route");
    const response = await v2EngineAxiosInstance.get("/makes");
    // console.log("fetching makes route", response.data, typeof response.data);
    return NextResponse.json(response.data);
  } catch (error) {
    console.error("Error fetching makes:", error);
    return NextResponse.json(
      { error: "Error fetching makes" },
      { status: 500 },
    );
  }
}
