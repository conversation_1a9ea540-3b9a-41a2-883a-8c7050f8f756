import axios from "axios";
import Cookies from "js-cookie";

export const registerUser = async (formData: {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  role_id: number;
}) => {
  try {
    // Send form data to backend
    const response = await axios.post(
      `${process.env.WHITEBOOK_API_URL_V2}/auth/sign-up`,
      formData,
      {
        headers: {
          "Content-Type": "application/json",
          "X-WHITEBOOK-API-Key": process.env.WHITEBOOK_ADMIN_API_KEY,
        },
      },
    );

    if (response.status === 201) {
      console.log("Register successful:", response.data);
      return { ...response.data, status: response.status };
    } else {
      console.log("Register failed:", response.data);
      return null;
    }
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      console.error("Error during Register:", error.response.data.errors[0].message);
      return error.response.data.errors[0].message;
    } else {
      console.error("Error during Register:", error);
    }
    return null;
  }
};

export const loginUser = async (formData: {
  email: string;
  password: string;
}) => {
  console.log("loginUser hit");
  try {
    console.log(
      "process.env.WHITEBOOK_API_URL_V2 in loginUser -> ",
      process.env.WHITEBOOK_API_URL_V2,
    );
    const response = await axios.post(
      `${process.env.WHITEBOOK_API_URL_V2}/login`,
      formData,
      {
        headers: {
          "Content-Type": "application/json",
          "X-WHITEBOOK-API-Key": process.env.WHITEBOOK_ADMIN_API_KEY,
        },
      },
    );

    if (response.status === 200) {
      console.log("Signin successful");

      // Store the token in cookies, expires in 7 days
      Cookies.set("token", response.data.data.token, { expires: 7 });
      Cookies.set("id", response.data.data.id, { expires: 7 });
      return response.data;
    } else {
      console.log("Signin failed:", response.data);
      return null;
    }
  } catch (error) {
    console.error("Error during signin:", error);
    return null;
  }
};

export const logoutUser = () => {
  // Remove the token and id from cookies
  Cookies.remove("token");
  Cookies.remove("id");

  // Redirect to login page
  window.location.href = "/auth/sign-in";
};
