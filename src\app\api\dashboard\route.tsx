import { NextRequest, NextResponse } from "next/server";
import {
  DashboardResponse,
  ApiResponse,
} from "@/utils/types/core.whitebook.types";

export async function GET(req: NextRequest) {
  try {
    const authorizationHeader = req.headers.get("Authorization");

    const response = await fetch(
      `${process.env.WHITEBOOK_API_URL_V2}/dashboard`,
      {
        method: "GET",
        headers: {
          Authorization: authorizationHeader || "", // Use the header if it exists
          "Content-Type": "application/json",
        },
      },
    );

    if (!response.ok) {
      console.error(
        "Failed to fetch dashboard data:",
        response.status,
        response.statusText,
      );
      return NextResponse.json(
        { error: "Failed to fetch dashboard data" },
        { status: response.status },
      );
    }

    const data = (await response.json()) as ApiResponse<DashboardResponse>;

    return NextResponse.json(data);
  } catch (error) {
    console.error("Error fetching dashboard data:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred while fetching dashboard data" },
      { status: 500 },
    );
  }
}
