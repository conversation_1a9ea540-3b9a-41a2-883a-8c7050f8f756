export const getVariantsResponse = `[
    {
        "model": "A1",
        "make": "AUDI",
        "variant_id": 43060,
        "name": "1.0 TFSI 1ST",
        "vehicle_model_id": 900,
        "body_type": "hatchback",
        "manufacture_year": 2017,
        "engine_number": "",
        "transmission_type": "AT",
        "drive_type": "",
        "number_of_doors": 5,
        "number_of_seats": 5,
        "number_of_years": 0,
        "fuel_type": "petrol",
        "cubic_capacity": 1000,
        "battery_capacity": 0,
        "gvm": 0,
        "registration_year": 0,
        "axle_configuration": "",
        "top_speed": 0,
        "fuel_tank_size": 0,
        "co2_output": 0,
        "valves": 0,
        "range": 0,
        "combined_energy_consumption": 0,
        "charging_power_ac": 0,
        "charge_time": 0,
        "fast_charging_power_dc": 0,
        "fast_charge_time": 0,
        "engine_power": 0,
        "engine_torque": 0,
        "gcm": 0,
        "tare": 0,
        "front_number_tires": 0,
        "front_tyre_type": 0,
        "rear_tyre_size": 0,
        "rear_number_tires": 0,
        "length": 0,
        "height": 2017,
        "width": 0,
        "wheelbase": 0,
        "cif": 10652.25,
        "cif_currency": "USD",
        "market_value": 282710.72
    },
    {
        "model": "PIXIS",
        "make": "TOYOTA",
        "variant_id": 47128,
        "name": "EPOCH",
        "vehicle_model_id": 1239,
        "body_type": "hatchback",
        "manufacture_year": 2014,
        "engine_number": "",
        "transmission_type": "AT",
        "drive_type": "",
        "number_of_doors": 5,
        "number_of_seats": 5,
        "number_of_years": 0,
        "fuel_type": "petrol",
        "cubic_capacity": 700,
        "battery_capacity": 0,
        "gvm": 0,
        "registration_year": 0,
        "axle_configuration": "",
        "top_speed": 0,
        "fuel_tank_size": 0,
        "co2_output": 0,
        "valves": 0,
        "range": 0,
        "combined_energy_consumption": 0,
        "charging_power_ac": 0,
        "charge_time": 0,
        "fast_charging_power_dc": 0,
        "fast_charge_time": 0,
        "engine_power": 0,
        "engine_torque": 0,
        "gcm": 0,
        "tare": 0,
        "front_number_tires": 0,
        "front_tyre_type": 0,
        "rear_tyre_size": 0,
        "rear_number_tires": 0,
        "length": 4378,
        "height": 2014,
        "width": 0,
        "wheelbase": 0,
        "cif": 4733.75,
        "cif_currency": "USD",
        "market_value": 133819.72
    }
]`;

export const getModelsResponse = `[
    {
        "vehicle_model_id": 900,
        "vehicle_make_id": 25,
        "model": "A1",
        "vehicle_variants": [],
        "year_range": [
            2017,
            2018
        ],
        "total_vehicle_variants": 0,
        "total_year_range": 0
    },
    {
        "vehicle_model_id": 901,
        "vehicle_make_id": 25,
        "model": "A3",
        "vehicle_variants": [],
        "year_range": [
            2017,
            2018,
            2019,
            2020
        ],
        "total_vehicle_variants": 0,
        "total_year_range": 0
    }
]`;

export const getMakesResponse = `[
    {
        "vehicle_id": 25,
        "name": "AUDI",
        "vehicle_models": [],
        "year_range": [
            2017,
            2018,
            2019,
            2020,
            2021,
            2022
        ],
        "total_vehicle_models": 14,
        "total_vehicle_variants": 8,
        "total_year_range": 6
    },
    {
        "vehicle_id": 26,
        "name": "BMW",
        "vehicle_models": [],
        "year_range": [
            2004,
            2005,
            2006,
            2007,
            2019,
            2020,
            2021,
            2022
        ],
        "total_vehicle_models": 24,
        "total_vehicle_variants": 71,
        "total_year_range": 23
    }
]`;

export const getVariantResponse = `{
    "model": "UP!",
    "make": "VOLKSWAGEN",
    "variant_id": 1,
    "name": "1.0 TSI",
    "vehicle_model_id": 1286,
    "body_type": "hatchback",
    "manufacture_year": 2020,
    "engine_number": "",
    "transmission_type": "AT",
    "drive_type": "",
    "number_of_doors": 5,
    "number_of_seats": 5,
    "number_of_years": 0,
    "fuel_type": "diesel",
    "cubic_capacity": 1000,
    "battery_capacity": 0,
    "gvm": 0,
    "registration_year": 0,
    "axle_configuration": "",
    "top_speed": 0,
    "fuel_tank_size": 0,
    "co2_output": 0,
    "valves": 0,
    "range": 0,
    "combined_energy_consumption": 0,
    "charging_power_ac": 0,
    "charge_time": 0,
    "fast_charging_power_dc": 0,
    "fast_charge_time": 0,
    "engine_power": 0,
    "engine_torque": 0,
    "gcm": 0,
    "tare": 0,
    "front_number_tires": 0,
    "front_tyre_type": 0,
    "rear_tyre_size": 0,
    "rear_number_tires": 0,
    "length": 4908,
    "height": 2020,
    "width": 0,
    "wheelbase": 0,
    "cif": 18166.994,
    "cif_currency": "USD",
    "market_value": 482152.2
}`;

export const getModelResponse = `{
    "vehicle_model_id": 1,
    "vehicle_make_id": 25,
    "model": "A1",
    "vehicle_variants": [],
    "year_range": [
        2017,
        2018
    ],
    "total_vehicle_variants": 0,
    "total_year_range": 0
}`;

export const getMakeResponse = `{
    "vehicle_id": 1,
    "name": "ISUZU",
    "vehicle_models": [],
    "year_range": [
        2009,
        2012,
        2013,
        2014,
        2015,
        2017,
        2018,
        2010,
        2011,
        2016,
        2019,
        2008
    ],
    "total_vehicle_models": 4,
    "total_vehicle_variants": 11,
    "total_year_range": 12
}`;
