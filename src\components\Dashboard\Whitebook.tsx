"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, Button } from "@material-tailwind/react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import {
  DashboardResponse,
  ApiResponse,
} from "@/utils/types/core.whitebook.types";

const Whitebook: React.FC = () => {
  const { data: session } = useSession();
  const router = useRouter();

  const [recentSearches, setRecentSearches] = useState<
    DashboardResponse["top_searches"]
  >([]);
  const [billingSummary, setBillingSummary] = useState<
    DashboardResponse["billing_summary"]
  >({
    pending_invoices: 0,
    total_due: 0,
    currency: "ZMW",
  });
  const [insights, setInsights] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        const response = await fetch("/api/dashboard", {
          method: "GET",
          headers: {
            Authorization: `Bearer ${session?.user?.token || ""}`, // Pass the token if available
            "Content-Type": "application/json",
          },
        });

        if (response.status === 409) {
          throw new Error(
            "You are not authorized to view this dashboard. Please contact support.",
          );
        }

        if (!response.ok) {
          throw new Error(
            `Failed to fetch dashboard data: ${response.statusText}`,
          );
        }

        const { data } =
          (await response.json()) as ApiResponse<DashboardResponse>;

        setRecentSearches(data.top_searches || []);
        setBillingSummary(
          data.billing_summary || {
            pending_invoices: 0,
            total_due: 0,
            currency: "ZMW",
          },
        );
        setInsights([]);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "An unexpected error occurred",
        );
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [session]);

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        Loading...
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-500">
        {"Failed to fetch dashboard data"}
      </div>
    );
  }

  return (
    <div className="grid gap-6">
      {/* First Row */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {/* Recent Motor Vehicle Searches */}
        <Card
          className="rounded-2xl p-6"
          placeholder={undefined}
          onPointerEnterCapture={undefined}
          onPointerLeaveCapture={undefined}
        >
          <h2 className="mb-4 text-xl font-bold">
            Recent Motor Vehicle Searches
          </h2>
          <div className="mb-2 grid grid-cols-2 gap-4 font-bold md:grid-cols-4">
            <div>Manufacturer</div>
            <div>Submodel</div>
            <div>Year</div>
            <div>Fair Market Value</div>
          </div>
          <ul className="space-y-2">
            {recentSearches.length > 0 ? (
              recentSearches.map((search, index) => (
                <li
                  key={index}
                  className="grid grid-cols-2 gap-4 rounded-2xl bg-[#DFDEDE] p-2 md:grid-cols-4"
                >
                  <div>{search.search_term.manufacturer}</div>
                  <div>{search.search_term.sub_model}</div>
                  <div>{search.search_term.year}</div>
                  <div>{search.fair_market_value}</div>
                </li>
              ))
            ) : (
              <p className="text-center text-gray-500">
                No recent searches available.
              </p>
            )}
          </ul>
        </Card>

        {/* Billing Summary */}
        <Card
          className="flex flex-col items-center justify-center space-y-4 rounded-2xl p-6"
          placeholder={undefined}
          onPointerEnterCapture={undefined}
          onPointerLeaveCapture={undefined}
        >
          <h2 className="text-center text-xl font-bold">Billing Summary</h2>
          <div className="text-center text-5xl font-bold text-gray-800">
            {billingSummary.currency}{" "}
            {billingSummary.total_due.toLocaleString()}
          </div>
          <div className="text-center text-lg text-gray-600">
            Pending Invoices: {billingSummary.pending_invoices}
          </div>
          <Button
            className="mt-4 w-auto self-center bg-[#DFDEDE] text-[#2A2B2D] hover:bg-[#CDD6DF]"
            placeholder={undefined}
            onPointerEnterCapture={undefined}
            onPointerLeaveCapture={undefined}
            onClick={() => router.push("/billing")}
          >
            Show More
          </Button>
        </Card>
      </div>

      {/* Second Row */}
      <div>
        {/* Latest Insights */}
        <Card
          className="rounded-2xl p-4 pb-4"
          placeholder={undefined}
          onPointerEnterCapture={undefined}
          onPointerLeaveCapture={undefined}
        >
          <h2 className="mb-4 text-xl font-bold">
            Latest Insights and Additions
          </h2>
          <div className="mb-2 grid grid-cols-3 gap-4 font-bold">
            <div>Manufacturer</div>
            <div>Submodel</div>
            <div>Transmission</div>
          </div>
          {/* <ul className="space-y-2"> */}
          {/* {insights.length > 0 ? ( TO - DO Insights workflow needs to be built
              insights.map((insight, index) => (
                <li
                  key={index}
                  className="grid grid-cols-3 gap-4 rounded-2xl bg-[#DFDEDE] p-2"
                >
                </li>
              ))
            ) : ( */}
          <p className="text-center text-gray-500">No insights available.</p>
          {/* </ul> */}
          <Button
            className="mt-4 w-auto self-center bg-[#DFDEDE] text-[#2A2B2D] hover:bg-[#CDD6DF]"
            placeholder={undefined}
            onPointerEnterCapture={undefined}
            onPointerLeaveCapture={undefined}
          >
            Show More
          </Button>
        </Card>
      </div>
    </div>
  );
};

export default Whitebook;
