branches:
  main:
    label: ""
    increment: Patch
    prevent-increment:
      of-merged-branch: true
    track-merge-target: false
    regex: ^master$|^main$
    source-branches: []
    tracks-release-branches: false
    is-release-branch: false
    is-main-branch: true
  develop:
    mode: ContinuousDelivery
    label: alpha
    increment: Minor
    prevent-increment:
      when-current-commit-tagged: false
    track-merge-target: true
    regex: ^dev(elop)?(ment)?$
    source-branches:
      - main
    tracks-release-branches: true
    is-release-branch: false
    is-main-branch: false
    pre-release-weight: 0
  staging:
    mode: ContinuousDelivery
    label: beta
    increment: Minor
    prevent-increment:
      of-merged-branch: true
      when-current-commit-tagged: false
    track-merge-target: false
    regex: ^stag(e|ing)$
    source-branches:
      - main
      - support
    tracks-release-branches: false
    is-release-branch: true
    is-main-branch: false
    pre-release-weight: 30000
  release:
    mode: ManualDeployment
    label: beta
    increment: Minor
    prevent-increment:
      of-merged-branch: true
      when-current-commit-tagged: false
    track-merge-target: false
    regex: ^releases?[/-]
    source-branches:
      - main
      - support
    tracks-release-branches: false
    is-release-branch: true
    is-main-branch: false
    pre-release-weight: 30000
  feature:
    mode: ManualDeployment
    label: "{BranchName}"
    increment: Inherit
    prevent-increment:
      when-current-commit-tagged: false
    track-merge-message: true
    regex: ^features?[/-](?<BranchName>.+)
    source-branches:
      - develop
      - staging
      - main
      - release
      - support
      - hotfix
    is-source-branch-for: []
    is-main-branch: false
    pre-release-weight: 30000
  pull-request:
    mode: ContinuousDelivery
    label: PullRequest
    increment: Inherit
    prevent-increment:
      of-merged-branch: true
      when-current-commit-tagged: false
    label-number-pattern: '[/-](?<number>\d+)'
    track-merge-message: true
    regex: ^(pull|pull\-requests|pr)[/-]
    source-branches:
      - develop
      - staging
      - main
      - release
      - feature
      - support
      - hotfix
    is-source-branch-for: []
    pre-release-weight: 30000
  hotfix:
    mode: ManualDeployment
    label: beta
    increment: Inherit
    prevent-increment:
      when-current-commit-tagged: false
    regex: ^hotfix(es)?[/-](?<BranchName>.+)
    source-branches:
      - main
      - support
    is-source-branch-for: []
    is-release-branch: true
    is-main-branch: false
    pre-release-weight: 30000
  support:
    label: ""
    increment: Patch
    prevent-increment:
      of-merged-branch: true
    track-merge-target: false
    regex: ^support[/-](?<BranchName>.+)
    source-branches:
      - main
    is-source-branch-for: []
    tracks-release-branches: false
    is-release-branch: false
    is-main-branch: true
    pre-release-weight: 55000
  unknown:
    mode: ManualDeployment
    label: "{BranchName}"
    increment: Inherit
    prevent-increment:
      when-current-commit-tagged: true
    regex: (?<BranchName>.+)
    source-branches:
      - main
      - staging
      - develop
      - release
      - feature
      - pull-request
      - hotfix
      - support
    is-source-branch-for: []
    is-main-branch: false
ignore:
  sha: []
mode: ContinuousDelivery
label: "{BranchName}"
increment: Inherit
prevent-increment:
  of-merged-branch: false
  when-branch-merged: false
  when-current-commit-tagged: true
track-merge-target: false
track-merge-message: true
commit-message-incrementing: Enabled
regex: ""
source-branches: []
is-source-branch-for: []
tracks-release-branches: false
is-release-branch: false
is-main-branch: false