"use client";
import React from "react"; // useEffect
// import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
// import {
//     faDice,
//     faDiceThree,
//     faTruckPickup,
//     faVectorSquare,
// } from "@fortawesome/free-solid-svg-icons";
// import axios from "axios";
// import VehiclesDataStats from "../Cards/VehiclesDataStats";
// import "@/css/icons.css";

const Insurance: React.FC = () => {
  // const [variants, setVariants] = React.useState([]);
  // const [models, setModels] = React.useState([]);
  // const [makes, setMakes] = React.useState([]);
  // const [loading, setLoading] = React.useState(true);
  // const [error, setError] = React.useState<any>(null);
  // useEffect(() => {
  //     // Function to fetch variants data
  //     const config = {
  //         headers: {
  //             "X-Insurance-API-Key":
  //                 "UaaqpSBbauT5z+RsMrVdiX4SxZkny1ta9K9hw0/bV3FOsj9NAobMdRK2KTUzskc2jBpG+Q",
  //             // 'Access-Control-Allow-Origin': '*', // CORS
  //         },
  //     };
  //     // console.log("config here", config);
  //     const fetchData = async () => {
  //         try {
  //             const response = await axios.get(
  //                 "http://localhost:8870/api/v2/variants",
  //                 config,
  //             );
  //             const result = await response.data;
  //             setVariants(result.data); // Update state with the fetched data
  //             console.log("result", result);
  //         } catch (error) {
  //             setError(error);
  //             console.error("Error fetching variants:", error);
  //         } finally {
  //             setLoading(false); // Update loading state
  //         }
  //     };

  //     fetchData();
  // }, []); // Empty dependency array means this effect runs once after the initial render
  // useEffect(() => {
  //     // Function to fetch variants data
  //     const config = {
  //         headers: {
  //             "X-Insurance-API-Key":
  //                 "UaaqpSBbauT5z+RsMrVdiX4SxZkny1ta9K9hw0/bV3FOsj9NAobMdRK2KTUzskc2jBpG+Q",
  //             // 'Access-Control-Allow-Origin': '*', // CORS
  //         },
  //     };
  //     // console.log("config here", config);
  //     const fetchData = async () => {
  //         try {
  //             const response = await axios.get(
  //                 "http://localhost:8870/api/v2/models",
  //                 config,
  //             );
  //             const result = await response.data;
  //             setModels(result.data); // Update state with the fetched data
  //             console.log("result", result);
  //         } catch (error) {
  //             setError(error);
  //             console.error("Error fetching variants:", error);
  //         } finally {
  //             setLoading(false); // Update loading state
  //         }
  //     };

  //     fetchData();
  // }, []); // Empty dependency array means this effect runs once after the initial render
  // useEffect(() => {
  //     // Function to fetch variants data
  //     const config = {
  //         headers: {
  //             "X-Insurance-API-Key":
  //                 "UaaqpSBbauT5z+RsMrVdiX4SxZkny1ta9K9hw0/bV3FOsj9NAobMdRK2KTUzskc2jBpG+Q",
  //             // 'Access-Control-Allow-Origin': '*', // CORS
  //         },
  //     };
  //     // console.log("config here", config);
  //     const fetchData = async () => {
  //         try {
  //             const response = await axios.get(
  //                 "http://localhost:8870/api/v2/makes",
  //                 config,
  //             );
  //             const result = await response.data;
  //             setMakes(result.data); // Update state with the fetched data
  //             console.log("result", result);
  //         } catch (error) {
  //             setError(error);
  //             console.error("Error fetching variants:", error);
  //         } finally {
  //             setLoading(false); // Update loading state
  //         }
  //     };

  //     fetchData();
  // }, []); // Empty dependency array means this effect runs once after the initial render
  // if (loading) return <div>Loading...</div>;
  // if (error) return <div>Error: {error.message}</div>;
  return (
    <>
      Under Construction
      {/* <div className="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 xl:grid-cols-4 2xl:gap-7.5">
                <VehiclesDataStats
                    title="Total Vehicle Variants"
                    total={`${variants.length}`}
                >
                    <FontAwesomeIcon icon={faDice} className="vehicle-icons" />
                </VehiclesDataStats>
                <VehiclesDataStats title="Latest Variants" total="0">
                    <FontAwesomeIcon icon={faDiceThree} className="vehicle-icons" />
                </VehiclesDataStats>
                <VehiclesDataStats
                    title="Total Vehicle Models"
                    total={`${models.length}`}
                >
                    <FontAwesomeIcon
                        icon={faVectorSquare}
                        className="vehicle-icons"
                    />
                </VehiclesDataStats>
                <VehiclesDataStats
                    title="Total Vehicle Makes"
                    total={`${makes.length}`}
                >
                    <FontAwesomeIcon
                        icon={faTruckPickup}
                        className="vehicle-icons"
                    />
                </VehiclesDataStats>
            </div> */}
    </>
  );
};

export default Insurance;
