import React, { useCallback, useEffect, useState } from "react";
import { useDropzone } from "react-dropzone";
import { Listbox, ListboxButton, ListboxOption, ListboxOptions, Transition } from "@headlessui/react";
import { SelectorIcon, CheckIcon } from "@heroicons/react/solid";
import { State } from 'country-state-city';

interface BusinessType {
    id: number;
    name: string;
    description: string;
}

interface OrganizationDetailsFormProps {
    companyName: string;
    tpin: string;
    phoneNumber: string;
    email: string;
    address: {
        street: string;
        city: string;
        state: string;
        zip_code: string;
        country: string;
    };
    incorporationDocuments: File[];
    setCompanyName: (value: string) => void;
    setTpin: (value: string) => void;
    setPhoneNumber: (value: string) => void;
    setEmail: (value: string) => void;
    setAddress: (value: any) => void;
    handleAddDocument: (file: File) => void;
    handleRemoveDocument: (index: number) => void;
    errors: { [key: string]: string };
    documentsAddedError: string;
    businessTypes: BusinessType[];
    businessTypeId: number;
    setBusinessTypeId: (value: number) => void;
}

const OrganizationDetailsForm: React.FC<OrganizationDetailsFormProps> = ({
    companyName,
    tpin,
    phoneNumber,
    email,
    address,
    incorporationDocuments,
    setCompanyName,
    setTpin,
    setPhoneNumber,
    setEmail,
    setAddress,
    businessTypes,
    handleAddDocument,
    handleRemoveDocument,
    errors,
    documentsAddedError,
    businessTypeId,
    setBusinessTypeId,
}) => {
    const [filteredBusinessTypes, setFilteredBusinessTypes] = useState<BusinessType[]>([]);
    const [states, setStates] = useState<{ value: string; label: string }[]>([]);

    useEffect(() => {
        // Filter out the business type with the name 'Placeholder'
        const filteredTypes = businessTypes.filter(type => type.name !== 'Placeholder');
        setFilteredBusinessTypes(filteredTypes);
    }, [businessTypes]);

    useEffect(() => {
        // Set default country to Zambia
        setAddress((prevAddress: any) => ({ ...prevAddress, country: 'Zambia' }));
    }, [setAddress]);

    useEffect(() => {
        // Get the list of states for Zambia
        const stateList = State.getStatesOfCountry('ZM').map(state => ({
            value: state.isoCode,
            label: state.name
        }));
        setStates(stateList);
    }, []);

    const onDrop = useCallback((acceptedFiles: File[]) => {
        acceptedFiles.forEach(file => {
            handleAddDocument(file);
        });
    }, [handleAddDocument]);

    const { getRootProps, getInputProps } = useDropzone({ onDrop, multiple: true });

    console.log('businessTypeId', businessTypeId)

    return (
        <form className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label
                        htmlFor="company_name"
                        className="block text-md font-medium text-gray-700"
                    >
                        Company Name <span className="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        id="company_name"
                        name="company_name"
                        value={companyName}
                        onChange={(e) => setCompanyName(e.target.value)}
                        className={`mt-1 w-full rounded-md border border-gray-300 px-3 py-2 shadow-md focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-11 ${companyName ? "bg-[#e8f0fe]" : ""}`}
                        style={{ fontSize: "14px" }}
                        required
                    />
                    {errors.companyName && <p className="text-red-500 text-sm">{errors.companyName}</p>}
                </div>
                <div>
                    <label
                        htmlFor="email"
                        className="block text-md font-medium text-gray-700"
                    >
                        Email <span className="text-red-500">*</span>
                    </label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className={`mt-1 w-full rounded-md border border-gray-300 px-3 py-2 shadow-md focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-11 ${email ? "bg-[#e8f0fe]" : ""}`}
                        style={{ fontSize: "14px" }}
                        required
                    />
                    {errors.organizationEmail && <p className="text-red-500 text-sm">{errors.organizationEmail}</p>}
                </div>
                <div>
                    <label
                        htmlFor="tpin"
                        className="block text-md font-medium text-gray-700"
                    >
                        TPIN <span className="text-red-500">*</span>
                    </label>
                    <input
                        type="number"
                        id="tpin"
                        name="tpin"
                        value={tpin}
                        onChange={(e) => setTpin(e.target.value)}
                        className={`mt-1 w-full rounded-md border border-gray-300 px-3 py-2 shadow-md focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-11 ${tpin ? "bg-[#e8f0fe]" : ""}`}
                        style={{ fontSize: "14px" }}
                        required
                    />
                    {errors.tpin && <p className="text-red-500 text-sm">{errors.tpin}</p>}
                </div>
                <div>
                    <label
                        htmlFor="phone_number"
                        className="block text-md font-medium mb-1 text-gray-700"
                    >
                        Phone Number <span className="text-red-500">*</span>
                    </label>
                    <div className="flex mt-1">
                        <div className="flex items-center justify-center bg-gray-200 border border-gray-300 rounded-l-md px-3 py-2 shadow-md" style={{ fontSize: "14px" }}>
                            +260
                        </div>
                        <input
                            type="text"
                            id="phone_number"
                            name="phone_number"
                            value={phoneNumber}
                            onChange={(e) => setPhoneNumber(e.target.value)}
                            className={`w-full rounded-r-md border border-gray-300 px-3 py-2 shadow-md focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-11  ${phoneNumber ? "bg-[#e8f0fe]" : ""}`}
                            style={{ fontSize: "14px" }}
                            placeholder="Enter phone number"
                            required
                        />
                    </div>
                    {errors.phoneNumber && <p className="text-red-500 text-sm">{errors.phoneNumber}</p>}
                </div>
                <div>
                    <label
                        htmlFor="country"
                        className="block text-md font-medium text-gray-700"
                    >
                        Country <span className="text-red-500">*</span>
                    </label>
                    <Listbox value={address.country} onChange={(value) => setAddress({ ...address, country: 'Zambia' })}>
                        <div className="relative mt-1">
                            <ListboxButton className={`relative w-full cursor-default rounded-md border border-gray-300 py-2 pl-3 pr-10 text-left shadow-md focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-11 ${address.country ? "bg-[#e8f0fe]" : ""}`} style={{ fontSize: "14px" }}>
                                <span className="block truncate">
                                    Zambia
                                </span>
                                <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                    <SelectorIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                                </span>
                            </ListboxButton>
                            <Transition
                                as={React.Fragment}
                                leave="transition ease-in duration-100"
                                leaveFrom="opacity-100"
                                leaveTo="opacity-0"
                            >
                                <ListboxOptions className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none md:text-md" style={{ fontSize: "14px" }}>
                                    <ListboxOption
                                        key="ZM"
                                        className={({ active }) =>
                                            `relative cursor-default select-none py-2 pl-10 pr-4 ${active ? "bg-[#f1f1f1] text-gray-700" : "text-gray-900"
                                            }`}
                                        value="ZM"
                                    >
                                        {({ selected }) => (
                                            <>
                                                <span
                                                    className={`block truncate ${selected ? "font-medium" : "font-normal"
                                                        }`}
                                                >
                                                    Zambia
                                                </span>
                                                {selected ? (
                                                    <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-white">
                                                        <CheckIcon className="h-5 w-5" aria-hidden="true" />
                                                    </span>
                                                ) : null}
                                            </>
                                        )}
                                    </ListboxOption>
                                </ListboxOptions>
                            </Transition>
                        </div>
                    </Listbox>
                    {errors.country && <p className="text-red-500 text-sm">{errors.country}</p>}
                </div>
                <div>
                    <label
                        htmlFor="state"
                        className="block text-md font-medium text-gray-700"
                    >
                        State <span className="text-red-500">*</span>
                    </label>
                    <Listbox value={address.state} onChange={(value) => setAddress({ ...address, state: states.find(state => state.value === value)?.label || '' })}>
                        <div className="relative mt-1">
                            <ListboxButton className={`relative w-full cursor-default rounded-md border border-gray-300 py-2 pl-3 pr-10 text-left shadow-md focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-11 ${address.state && address.state !== "" ? "bg-[#e8f0fe]" : ""}`} style={{ fontSize: "14px" }}>
                                <span className="block truncate">
                                    {states.find(state => state.label === address.state)?.label || "Select State"}
                                </span>
                                <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                    <SelectorIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                                </span>
                            </ListboxButton>
                            <Transition
                                as={React.Fragment}
                                leave="transition ease-in duration-100"
                                leaveFrom="opacity-100"
                                leaveTo="opacity-0"
                            >
                                <ListboxOptions className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none md:text-md" style={{ fontSize: "14px" }}>
                                    {states.map((state) => (
                                        <ListboxOption
                                            key={state.value}
                                            className={({ active }) =>
                                                `relative cursor-default select-none py-2 pl-10 pr-4 ${active ? "bg-[#f1f1f1] text-gray-700" : "text-gray-900"
                                                }`}
                                            value={state.value}
                                        >
                                            {({ selected }) => (
                                                <>
                                                    <span
                                                        className={`block truncate ${selected ? "font-medium" : "font-normal"
                                                            }`}
                                                    >
                                                        {state.label}
                                                    </span>
                                                    {selected ? (
                                                        <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-white">
                                                            <CheckIcon className="h-5 w-5" aria-hidden="true" />
                                                        </span>
                                                    ) : null}
                                                </>
                                            )}
                                        </ListboxOption>
                                    ))}
                                </ListboxOptions>
                            </Transition>
                        </div>
                    </Listbox>
                    {errors.state && <p className="text-red-500 text-sm">{errors.state}</p>}
                </div>
                <div>
                    <label
                        htmlFor="city"
                        className="block text-md font-medium text-gray-700"
                    >
                        City <span className="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        id="city"
                        name="city"
                        value={address.city}
                        onChange={(e) => setAddress({ ...address, city: e.target.value })}
                        className={`mt-1 w-full rounded-md border border-gray-300 px-3 py-2 shadow-md focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-11 ${address.city ? "bg-[#e8f0fe]" : ""}`}
                        style={{ fontSize: "14px" }}
                        required
                    />
                    {errors.city && <p className="text-red-500 text-sm">{errors.city}</p>}
                </div>
                <div>
                    <label
                        htmlFor="street"
                        className="block text-md font-medium text-gray-700"
                    >
                        Street <span className="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        id="street"
                        name="street"
                        value={address.street}
                        onChange={(e) => setAddress({ ...address, street: e.target.value })}
                        className={`mt-1 w-full rounded-md border border-gray-300 px-3 py-2 shadow-md focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-11 ${address.street ? "bg-[#e8f0fe]" : ""}`}
                        style={{ fontSize: "14px" }}
                        required
                    />
                    {errors.street && <p className="text-red-500 text-sm">{errors.street}</p>}
                </div>
                <div>
                    <label
                        htmlFor="zip_code"
                        className="block text-md font-medium text-gray-700"
                    >
                        Zip Code <span className="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        id="zip_code"
                        name="zip_code"
                        value={address.zip_code}
                        onChange={(e) => setAddress({ ...address, zip_code: e.target.value })}
                        className={`mt-1 w-full rounded-md border border-gray-300 px-3 py-2 shadow-md focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-11 ${address.zip_code ? "bg-[#e8f0fe]" : ""}`}
                        style={{ fontSize: "14px" }}
                        required
                    />
                    {errors.zip_code && <p className="text-red-500 text-sm">{errors.zip_code}</p>}
                </div>
                <div>
                    <label
                        htmlFor="business_type"
                        className="block text-md font-medium text-gray-700"
                    >
                        Business Type <span className="text-red-500">*</span>
                    </label>
                    <Listbox value={businessTypeId} onChange={setBusinessTypeId}>
                        <div className="relative mt-1">
                            <ListboxButton className={`relative w-full cursor-default rounded-md border border-gray-300 py-2 pl-3 pr-10 text-left shadow-md focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-11 ${businessTypeId && businessTypeId !== 0 ? "bg-[#e8f0fe]" : ""}`} style={{ fontSize: "14px" }}>
                                <span className="block truncate">
                                    {filteredBusinessTypes.find(type => type.id === businessTypeId)?.name || "Select Business Type"}
                                </span>
                                <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                    <SelectorIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                                </span>
                            </ListboxButton>
                            <Transition
                                as={React.Fragment}
                                leave="transition ease-in duration-100"
                                leaveFrom="opacity-100"
                                leaveTo="opacity-0"
                            >
                                <ListboxOptions className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none md:text-md" style={{ fontSize: "14px" }}>
                                    {filteredBusinessTypes.map((type) => (
                                        <ListboxOption
                                            key={type.id}
                                            className={({ active }) =>
                                                `relative cursor-default select-none py-2 pl-10 pr-4 ${active ? "bg-[#f1f1f1] text-gray-700" : "text-gray-900"
                                                }`}
                                            value={type.id}
                                        >
                                            {({ selected }) => (
                                                <>
                                                    <span
                                                        className={`block truncate ${selected ? "font-medium" : "font-normal"
                                                            }`}
                                                    >
                                                        {type.name}
                                                    </span>
                                                    {selected ? (
                                                        <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-white">
                                                            <CheckIcon className="h-5 w-5" aria-hidden="true" />
                                                        </span>
                                                    ) : null}
                                                </>
                                            )}
                                        </ListboxOption>
                                    ))}
                                </ListboxOptions>
                            </Transition>
                        </div>
                    </Listbox>
                    {errors.businessType && <p className="text-red-500 text-sm">{errors.businessType}</p>}
                </div>
            </div>
            <div>
                <div className="mt-4">
                    <label
                        htmlFor="incorporation_documents"
                        className="block text-md font-medium text-gray-700"
                    >
                        Upload Incorporation Documents <span className="text-red-500">*</span>
                    </label>
                    <div
                        {...getRootProps()}
                        className={`mt-1 w-full rounded-md border border-gray-300 px-3 py-2 shadow-md focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-11 ${incorporationDocuments.length > 0 ? "bg-[#e8f0fe]" : ""}`}
                        style={{ fontSize: "14px" }}
                    >
                        <input {...getInputProps()} className="dropzone-input" />
                        <p>Drop file here, or click to select file</p>
                    </div>
                </div>
                {errors.incorporationDocuments && <p className="text-red-500 text-sm">{errors.incorporationDocuments}</p>}
            </div>
            {documentsAddedError && incorporationDocuments.length === 0 && <p className="text-red-500 text-sm">{documentsAddedError}</p>}
            {incorporationDocuments.length > 0 && (
                <div className="mt-4">
                    <h4 className="text-md font-medium text-gray-700">Uploaded Documents</h4>
                    <table className="min-w-full bg-white">
                        <thead>
                            <tr>
                                <th className="py-2 px-4 border-b text-left">Document</th>
                                <th className="py-2 px-4 border-b text-left"></th>
                            </tr>
                        </thead>
                        <tbody>
                            {incorporationDocuments.map((doc, index) => (
                                <tr key={index}>
                                    <td className="py-2 px-4 border-b">{doc.name}</td>
                                    <td className="py-2 px-4 border-b">
                                        <button
                                            type="button"
                                            onClick={() => handleRemoveDocument(index)}
                                            className="text-red-500 hover:text-red-700"
                                        >
                                            Remove
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}
        </form>
    );
};

export default OrganizationDetailsForm;