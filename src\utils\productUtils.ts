import { ProductCustomDetail, ImageUploadStatus, UploadedImageData } from "@/types/product/index";
import path from 'path';

/**
 * Transforms product details from API format to frontend format
 */
export const transformProductDetails = (details: any): ProductCustomDetail[] => {
    if (!details || typeof details !== 'object') return [];

    return Object.entries(details).map(([key, value]) => {
        // Check if the value is an object with type information
        if (value && typeof value === 'object' && 'type' in value && 'value' in value) {
            // This is a structured field with type information
            return {
                customFieldName: key,
                fieldType: String(value.type), // Ensure it's a string
                value: String(value.value)
            };
        }

        // Handle legacy format or simple values
        // Try to infer the type, with special handling for decimal values
        let fieldType = 'text';

        if (typeof value === 'boolean') {
            fieldType = 'boolean';
        } else if (typeof value === 'number') {
            // Check if it's likely a decimal by looking for decimal places
            const stringValue = String(value);
            fieldType = stringValue.includes('.') && !stringValue.endsWith('.0') ? 'decimal' : 'number';
        }

        return {
            customFieldName: key,
            fieldType: fieldType,
            value: String(value)
        };
    });
};

/**
 * Transforms product details from frontend format to API format
 */
export const transformDetailsForApi = (details: ProductCustomDetail[] | undefined): { [key: string]: any } => {
    if (!details) return {};

    return details.reduce((acc, detail) => {
        if (detail.customFieldName) {
            try {
                // Create a structured object that preserves both value and type
                let processedValue: any;

                switch (detail.fieldType) {
                    case 'number':
                        // Parse as integer
                        processedValue = parseInt(detail.value, 10) || 0;
                        break;

                    case 'decimal':
                        // Parse as floating point with decimal precision
                        processedValue = parseFloat(detail.value) || 0;
                        break;

                    case 'boolean':
                        // Convert string to boolean
                        processedValue = detail.value.toLowerCase() === 'true' || detail.value === '1';
                        break;

                    case 'text':
                    default:
                        // Keep as string
                        processedValue = detail.value;
                        break;
                }

                // Store both the value and its type
                acc[detail.customFieldName] = {
                    type: detail.fieldType,
                    value: processedValue
                };

            } catch (error) {
                // Fallback to original value if parsing fails
                acc[detail.customFieldName] = {
                    type: 'text',
                    value: detail.value
                };
            }
        }
        return acc;
    }, {} as { [key: string]: any });
};

/**
 * Gets the URL for an image
 */
export const getImageUrl = (path: string): string => {
    if (!path) return '/images/placeholder.png';

    // Remove any leading/trailing slashes and clean the path
    const cleanPath = path.trim().replace(/^\/+|\/+$/g, '');

    // If the path is empty after cleaning, return placeholder
    if (!cleanPath) return '/images/placeholder.png';

    return `/api/products/view/${cleanPath}`;
};

/**
 * Processes images from existing product data
 */
export const processExistingImages = (images: any[] | undefined): ImageUploadStatus[] => {
    if (!images || !Array.isArray(images) || images.length === 0) {
        return [];
    }

    return images
        .filter(img => img !== null && img !== undefined)
        .map((img, index) => {
            // Handle string paths (common when fetching from API)
            if (typeof img === 'string') {
                return {
                    _localId: `existing-${index}-${Date.now()}`,
                    id: undefined,
                    name: img.split('/').pop() || `Image ${index + 1}`,
                    type: path.extname(img) || '.jpg',
                    is_default: index === 0,
                    file: img.split('/').pop() || `image-${index}.jpg`,
                    path: img,
                    status: 'success' as const,
                    isExisting: true // Mark as existing image
                };
            }

            // Handle object based image data
            if (typeof img === 'object' && img !== null) {
                // Extract path from various possible structures with proper type checking
                let imgPath = '';
                let imgId: number | undefined = undefined;

                if ('path' in img && typeof img.path === 'string') {
                    imgPath = img.path;
                } else if ('url' in img && typeof img.url === 'string') {
                    imgPath = img.url;
                } else if ('src' in img && typeof img.src === 'string') {
                    imgPath = img.src;
                }

                // Try to get the image ID from the object
                if ('id' in img && typeof img.id === 'number') {
                    imgId = img.id;
                } else if ('image_id' in img && typeof img.image_id === 'number') {
                    imgId = img.image_id;
                } else if ('imageId' in img && typeof img.imageId === 'number') {
                    imgId = img.imageId;
                }

                return {
                    _localId: `existing-${index}-${Date.now()}`,
                    id: imgId,
                    name: 'name' in img && typeof img.name === 'string'
                        ? img.name
                        : imgPath.split('/').pop() || `Image ${index + 1}`,
                    type: 'type' in img && typeof img.type === 'string'
                        ? img.type
                        : path.extname(imgPath) || '.jpg',
                    is_default: 'is_default' in img && typeof img.is_default === 'boolean'
                        ? img.is_default
                        : index === 0,
                    file: 'file' in img && typeof img.file === 'string'
                        ? img.file
                        : imgPath.split('/').pop() || `image-${index}.jpg`,
                    path: imgPath,
                    status: 'success' as const,
                    isExisting: true // Mark as existing image
                };
            }

            return null;
        })
        .filter(Boolean) as ImageUploadStatus[];
};

/**
 * Creates a new image upload status object from a file
 */
export const createImageUploadStatus = (file: File): ImageUploadStatus => {
    const localId = `${Date.now()}-${Math.random()}`;
    // Generate a unique filename similar to what the server will create
    const timestamp = Date.now();
    const uniqueFilename = `${timestamp}-${file.name.replace(/[^a-zA-Z0-9.]/g, '_')}`;
    const fileExtension = path.extname(file.name);
    const uniqueNameWithoutExt = uniqueFilename.substring(0, uniqueFilename.lastIndexOf('.'));

    return {
        _localId: localId,
        name: uniqueNameWithoutExt, // Unique name without extension
        type: fileExtension,
        is_default: false,
        file: uniqueFilename, // Unique name with extension
        path: '',
        status: 'success', // Mark as success immediately since we're not uploading yet
        originalFile: file,
    };
};

/**
 * Uploads images to MinIO
 */
export const uploadImagesToMinIO = async (
    imagesToUpload: ImageUploadStatus[],
    productId?: number
): Promise<UploadedImageData[]> => {
    const uploadResults: UploadedImageData[] = [];

    for (const upload of imagesToUpload) {
        // Skip images that are already uploaded or don't have an original file
        if (upload.isExisting || !upload.originalFile) continue;

        try {
            // Create FormData for upload
            const formData = new FormData();
            formData.append("image", upload.originalFile);
            if (productId) {
                formData.append("productId", String(productId));
            }

            const response = await fetch('/api/products/upload-image', {
                method: 'POST',
                body: formData,
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || `Upload failed with status ${response.status}`);
            }

            // Add to results
            const { _localId, status, errorMessage, originalFile, isExisting, ...imageData } = {
                ...upload,
                ...result
            };
            uploadResults.push(imageData);
        } catch (error) {
            // Just continue to the next image if there's an error
            console.error('Error uploading image:', error);
        }
    }

    return uploadResults;
};