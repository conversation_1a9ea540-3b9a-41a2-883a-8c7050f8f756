import { ProductCustomDetail, ImageUploadStatus, UploadedImageData } from "@/types/product/index";
import path from 'path';

// Type definitions for API data structures
interface ApiCustomDetailValue {
    type: string;
    value: string | number | boolean;
}

interface ApiCustomDetails {
    [key: string]: ApiCustomDetailValue | string | number | boolean;
}

interface ApiImageObject {
    id?: number;
    image_id?: number;
    imageId?: number;
    path?: string;
    url?: string;
    src?: string;
    name?: string;
    type?: string;
    is_default?: boolean;
    file?: string;
}

interface ApiUploadResponse {
    error?: string;
    [key: string]: unknown;
}

interface ProcessedApiValue {
    type: string;
    value: string | number | boolean;
}

/**
 * Transforms product details from API format to frontend format
 */
export const transformProductDetails = (details: ApiCustomDetails | null | undefined): ProductCustomDetail[] => {
    if (!details || typeof details !== 'object') return [];

    return Object.entries(details).map(([key, value]) => {
        // Check if the value is an object with type information
        if (value && typeof value === 'object' && 'type' in value && 'value' in value) {
            const structuredValue = value as ApiCustomDetailValue;
            // This is a structured field with type information
            return {
                customFieldName: key,
                fieldType: String(structuredValue.type), // Ensure it's a string
                value: String(structuredValue.value)
            };
        }

        // Handle legacy format or simple values
        // Try to infer the type, with special handling for decimal values
        let fieldType = 'text';

        if (typeof value === 'boolean') {
            fieldType = 'boolean';
        } else if (typeof value === 'number') {
            // Check if it's likely a decimal by looking for decimal places
            const stringValue = String(value);
            fieldType = stringValue.includes('.') && !stringValue.endsWith('.0') ? 'decimal' : 'number';
        }

        return {
            customFieldName: key,
            fieldType: fieldType,
            value: String(value)
        };
    });
};

/**
 * Transforms product details from frontend format to API format
 */
export const transformDetailsForApi = (details: ProductCustomDetail[] | undefined): { [key: string]: ProcessedApiValue } => {
    if (!details) return {};

    return details.reduce((acc, detail) => {
        if (detail.customFieldName) {
            try {
                // Create a structured object that preserves both value and type
                let processedValue: string | number | boolean;

                switch (detail.fieldType) {
                    case 'number':
                        // Parse as integer
                        processedValue = parseInt(detail.value, 10) || 0;
                        break;

                    case 'decimal':
                        // Parse as floating point with decimal precision
                        processedValue = parseFloat(detail.value) || 0;
                        break;

                    case 'boolean':
                        // Convert string to boolean
                        processedValue = detail.value.toLowerCase() === 'true' || detail.value === '1';
                        break;

                    case 'text':
                    default:
                        // Keep as string
                        processedValue = detail.value;
                        break;
                }

                // Store both the value and its type
                acc[detail.customFieldName] = {
                    type: detail.fieldType,
                    value: processedValue
                };

            } catch (_error) {
                // Fallback to original value if parsing fails
                acc[detail.customFieldName] = {
                    type: 'text',
                    value: detail.value
                };
            }
        }
        return acc;
    }, {} as { [key: string]: ProcessedApiValue });
};

/**
 * Gets the URL for an image
 */
export const getImageUrl = (path: string): string => {
    if (!path) return '/images/placeholder.png';

    // Remove any leading/trailing slashes and clean the path
    const cleanPath = path.trim().replace(/^\/+|\/+$/g, '');

    // If the path is empty after cleaning, return placeholder
    if (!cleanPath) return '/images/placeholder.png';

    return `/api/products/view/${cleanPath}`;
};

/**
 * Processes images from existing product data
 */
export const processExistingImages = (images: (string | ApiImageObject)[] | undefined): ImageUploadStatus[] => {
    if (!images || !Array.isArray(images) || images.length === 0) {
        return [];
    }

    const processedImages: ImageUploadStatus[] = [];

    images
        .filter((img): img is string | ApiImageObject => img !== null && img !== undefined)
        .forEach((img, index) => {
            // Handle string paths (common when fetching from API)
            if (typeof img === 'string') {
                processedImages.push({
                    _localId: `existing-${index}-${Date.now()}`,
                    id: undefined,
                    name: img.split('/').pop() || `Image ${index + 1}`,
                    type: path.extname(img) || '.jpg',
                    is_default: index === 0,
                    file: img.split('/').pop() || `image-${index}.jpg`,
                    path: img,
                    status: 'success' as const,
                    isExisting: true // Mark as existing image
                });
                return;
            }

            // Handle object based image data
            if (typeof img === 'object' && img !== null) {
                // Extract path from various possible structures with proper type checking
                let imgPath = '';
                let imgId: number | undefined = undefined;

                if (img.path && typeof img.path === 'string') {
                    imgPath = img.path;
                } else if (img.url && typeof img.url === 'string') {
                    imgPath = img.url;
                } else if (img.src && typeof img.src === 'string') {
                    imgPath = img.src;
                }

                // Try to get the image ID from the object
                if (img.id && typeof img.id === 'number') {
                    imgId = img.id;
                } else if (img.image_id && typeof img.image_id === 'number') {
                    imgId = img.image_id;
                } else if (img.imageId && typeof img.imageId === 'number') {
                    imgId = img.imageId;
                }

                processedImages.push({
                    _localId: `existing-${index}-${Date.now()}`,
                    id: imgId,
                    name: img.name && typeof img.name === 'string'
                        ? img.name
                        : imgPath.split('/').pop() || `Image ${index + 1}`,
                    type: img.type && typeof img.type === 'string'
                        ? img.type
                        : path.extname(imgPath) || '.jpg',
                    is_default: img.is_default && typeof img.is_default === 'boolean'
                        ? img.is_default
                        : index === 0,
                    file: img.file && typeof img.file === 'string'
                        ? img.file
                        : imgPath.split('/').pop() || `image-${index}.jpg`,
                    path: imgPath,
                    status: 'success' as const,
                    isExisting: true // Mark as existing image
                });
            }
        });

    return processedImages;
};

/**
 * Creates a new image upload status object from a file
 */
export const createImageUploadStatus = (file: File): ImageUploadStatus => {
    const localId = `${Date.now()}-${Math.random()}`;
    // Generate a unique filename similar to what the server will create
    const timestamp = Date.now();
    const uniqueFilename = `${timestamp}-${file.name.replace(/[^a-zA-Z0-9.]/g, '_')}`;
    const fileExtension = path.extname(file.name);
    const uniqueNameWithoutExt = uniqueFilename.substring(0, uniqueFilename.lastIndexOf('.'));

    return {
        _localId: localId,
        name: uniqueNameWithoutExt, // Unique name without extension
        type: fileExtension,
        is_default: false,
        file: uniqueFilename, // Unique name with extension
        path: '',
        status: 'success', // Mark as success immediately since we're not uploading yet
        originalFile: file,
    };
};

/**
 * Uploads images to MinIO
 */
export const uploadImagesToMinIO = async (
    imagesToUpload: ImageUploadStatus[],
    productId?: number
): Promise<UploadedImageData[]> => {
    const uploadResults: UploadedImageData[] = [];

    for (const upload of imagesToUpload) {
        // Skip images that are already uploaded or don't have an original file
        if (upload.isExisting || !upload.originalFile) continue;

        try {
            // Create FormData for upload
            const formData = new FormData();
            formData.append("image", upload.originalFile);
            if (productId) {
                formData.append("productId", String(productId));
            }

            const response = await fetch('/api/products/upload-image', {
                method: 'POST',
                body: formData,
            });

            const result = await response.json() as ApiUploadResponse;

            if (!response.ok) {
                throw new Error(result.error || `Upload failed with status ${response.status}`);
            }

            // Add to results - extract only the properties needed for UploadedImageData
            const imageData: UploadedImageData = {
                id: upload.id,
                name: upload.name,
                type: upload.type,
                is_default: upload.is_default,
                file: upload.file,
                path: upload.path,
                // Override with any data from the API response
                ...(result as Partial<UploadedImageData>)
            };
            uploadResults.push(imageData);
        } catch (error) {
            // Just continue to the next image if there's an error
            console.error('Error uploading image:', error);
        }
    }

    return uploadResults;
};