import React from 'react';

interface ProductDetailsCardProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

const ProductDetailsCard: React.FC<ProductDetailsCardProps> = ({
  title,
  children,
  className = ''
}) => {
  return (
    <div className={`rounded bg-white p-4 shadow-md ${className}`}>
      <h2 className="mb-2 text-xl font-semibold">{title}</h2>
      {children}
    </div>
  );
};

export default ProductDetailsCard;