"""
This script generates an Excel report summarizing vehicle search history data.
The script performs the following steps:
1. Reads search data from a JSON file.
2. Filters the data to include searches from Friday to Thursday.
3. Summarizes the searches per day, month, and quarter.
4. Writes the filtered and summarized data to an Excel file with multiple sheets.
Functions:
- get_searches_df: Reads search data from a JSON file and returns a DataFrame.
- get_last_week_number: Returns the number of the previous week.
- get_writable_df: Prepares a DataFrame for writing to an Excel file.
- get_searches_per_day_df: Summarizes searches per day.
- get_current_week_number: Returns the number of the current week.
- get_friday_to_thursday_df: Filters searches to include only those from Friday to Thursday.
- summarize_searches_per_interval: Summarizes searches per specified intervals (e.g., month, quarter).
Output:
- An Excel file named "WBV_Search_History_Report_W<current_week_number>_<current_year>.xlsx" with the following sheets:
    - "FriToThu - All (W<last_week_number>ToW<current_week_number>)": Filtered searches from Friday to Thursday.
    - "FriToThu - Daily (W<last_week_number>ToW<current_week_number>)": Daily summary of searches from Friday to Thursday.
    - "Monthly": Monthly summary of searches.
    - "Quarterly": Quarterly summary of searches.
    - "AllSearches": All search data.
"""

from pandas import ExcelWriter
from datetime import datetime
from utils.functions import (
    get_searches_df,
    get_last_week_number,
    get_writable_df,
    get_searches_per_day_df,
    get_current_week_number,
    get_friday_to_thursday_df,
    summarize_searches_per_interval,
)

searches_df = get_searches_df("searches.json")

friday_to_thursday_df = get_friday_to_thursday_df(searches_df)

searches_per_day_df = get_searches_per_day_df(friday_to_thursday_df)

searches_per_month_df = summarize_searches_per_interval(
    searches_df,
    [
        "month",
        "year",
        "quarter",
    ],
    "month",
)

searches_per_quarter_df = summarize_searches_per_interval(
    searches_df,
    [
        "quarter",
        "year",
    ],
    "quarter",
)

# Output Excel file path
excel_file_path = (
    f"WBV_Search_History_Report_W{get_current_week_number()}_{datetime.now().year}.xlsx"
)

# create a new Excel file with the filtered searches
with ExcelWriter(excel_file_path, engine="openpyxl") as writer:
    get_writable_df(friday_to_thursday_df).to_excel(
        writer,
        sheet_name=f"FriToThu - All (W{get_last_week_number()}ToW{get_current_week_number()})",
        index=False,
    )

# Append the searches per day summary to the existing file
with ExcelWriter(excel_file_path, engine="openpyxl", mode="a") as writer:
    searches_per_day_df.to_excel(
        writer,
        sheet_name=f"FriToThu - Daily (W{get_last_week_number()}ToW{get_current_week_number()})",
        index=False,
    )

# Append the searches per month summary to the existing file
with ExcelWriter(excel_file_path, engine="openpyxl", mode="a") as writer:
    searches_per_month_df.to_excel(writer, sheet_name="Monthly", index=False)

# Append the searches per quarter summary to the existing file
with ExcelWriter(excel_file_path, engine="openpyxl", mode="a") as writer:
    searches_per_quarter_df.to_excel(writer, sheet_name="Quarterly", index=False)

# Append all searches to the existing file
with ExcelWriter(excel_file_path, engine="openpyxl", mode="a") as writer:
    get_writable_df(searches_df).to_excel(writer, sheet_name="AllSearches", index=False)
