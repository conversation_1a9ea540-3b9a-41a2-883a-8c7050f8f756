"use client";

import React, { useState } from "react";
import { useSearchParams } from "next/navigation";
import Image from "next/image";
import axios from "axios";
import { z } from "zod";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEnvelope, faLock } from "@fortawesome/free-solid-svg-icons";
import { jwtDecode } from "jwt-decode";

interface JwtDecodedToken {
  user_id: number;
  email: string;
  role: string | null;
  key: string;
  exp: number;
}
const resetPasswordSchema = z
  .object({
    password: z.string().min(6, "Password must be at least 6 characters long"),
    confirmPassword: z
      .string()
      .min(6, "Password must be at least 6 characters long"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

type ResetPasswordData = z.infer<typeof resetPasswordSchema>;

const initialFormData = {
  password: "",
  confirmPassword: "",
};

const ResetPasswordPage: React.FC = () => {
  const [formData, setFormData] = useState<ResetPasswordData>(initialFormData);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [message, setMessage] = useState<string>("");

  const searchParams = useSearchParams();

  const code = searchParams.get("code");

  if (!code) {
    throw new Error("Invalid or missing reset code.");
  }

  const decodedToken: JwtDecodedToken = jwtDecode(code);
  const user_id = decodedToken.user_id;
  const email = decodedToken.email;
  const key = decodedToken.key;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    setMessage("");
    e.preventDefault();

    try {
      // Validate form data using zod
      resetPasswordSchema.parse(formData);
      const dataToSendToApi = {
        ...formData,
        user_id,
        email,
        key,
      };

      // Send form data to backend
      const response = await axios.post(
        "/api/reset-password",
        dataToSendToApi,
        {
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      if (response.status === 200) {
        setMessage("Password has been reset. Please log in.");
        setFormData(initialFormData);
        setErrors({});
      } else {
        setErrors({ general: "Failed to reset password. Please try again" });
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const zodErrors = error.errors.reduce((acc: any, curr: any) => {
          acc[curr.path[0]] = curr.message;
          return acc;
        }, {});
        setErrors(zodErrors);
      } else {
        console.error("Error during password reset:", error);
        if (
          axios.isAxiosError(error) &&
          error?.response &&
          error?.response?.data
        ) {
          setErrors({ general: "Failed to reset password." });
        } else {
          setErrors({
            general: "An unexpected error occurred. Please try again.",
          });
        }
      }
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-100">
      <div className="my-2 w-full max-w-md space-y-6 rounded-lg bg-white px-8 pb-8 pt-0 shadow-md">
        <div className="mb-2 text-center">
          <Image
            src="/images/logos/Whitebook ALT 8.png"
            priority
            width={200} // This will be overridden by CSS
            height={800} // This will be overridden by CSS
            alt="Logo"
            className="mx-auto h-[800px] max-h-45 w-auto"
          />
          <h2 className="mt-2 text-2xl font-bold text-gray-900">
            Forgot Password
          </h2>
          <hr
            className="mx-auto mt-4 w-full border-0"
            style={{
              height: "1px",
              background:
                "linear-gradient(to right, transparent, #06b6d4, transparent)",
            }}
          />
        </div>
        <form onSubmit={handleSubmit} className="space-y-4">
          {errors.general && (
            <div className="rounded bg-red-100 p-4 text-red-700">
              <p className="text-center text-sm">{errors.general}</p>
            </div>
          )}
          {message && (
            <div className="rounded bg-green-100 p-4 text-green-700">
              <p className="text-center text-sm">{message}</p>
            </div>
          )}
          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium text-gray-700"
            >
              Password
            </label>
            <div className="relative">
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 text-sm leading-5">
                <FontAwesomeIcon icon={faLock} />
              </div>
            </div>
            {errors.password && (
              <p className="mt-1 text-xs text-red-500">{errors.password}</p>
            )}
          </div>
          <div>
            <label
              htmlFor="confirmPassword"
              className="block text-sm font-medium text-gray-700"
            >
              Confirm Password
            </label>
            <div className="relative">
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 text-sm leading-5">
                <FontAwesomeIcon icon={faLock} />
              </div>
            </div>
            {errors.confirmPassword && (
              <p className="mt-1 text-xs text-red-500">
                {errors.confirmPassword}
              </p>
            )}
          </div>
          <button
            type="submit"
            className="w-full rounded-md bg-wbv-theme px-4 py-2 text-white transition duration-300 hover:bg-opacity-90"
          >
            Reset Password
          </button>
        </form>
        <div className="border-t pt-2 text-center">
          <p className="text-sm text-gray-600">
            Remember your password?&nbsp;
            <a href="/auth/sign-in" className="text-blue-500 hover:underline">
              Sign In
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default ResetPasswordPage;
