import { v2EngineAxiosInstance } from "../../../utils/axios";

// Types for manufacturer, model, and variant
type Manufacturer = { name: string; vehicle_id: number };
type ManufacturerOption = { label: string; value: number };
type Model = {
  model: string;
  vehicle_model_id: number;
  year_range: string;
  vehicle_variants: Variant[];
};
type ModelOption = {
  label: string;
  value: number;
  years: string;
  vehicle_variants: Variant[];
};
type Variant = {
  variant_id: number;
  name: string;
  manufacture_year: number;
  vehicle_model_id: number;
};

// Get all manufacturers
export const getManufacturers = async (): Promise<ManufacturerOption[]> => {
  try {
    const response = await v2EngineAxiosInstance.get<{ data: Manufacturer[] }>("/makes");
    const data = response.data;

    if (!data || !Array.isArray(data.data)) return [];

    return data.data
      .map((item) => ({
        label: item.name,
        value: item.vehicle_id,
      }))
      .sort((a, b) => a.label.localeCompare(b.label));
  } catch (error) {
    console.error("Error fetching manufacturers", error);
    return [];
  }
};

// Get models by manufacturer
export const getModelsByManufacturer = async (
  selectedManufacturer: number | string
): Promise<ModelOption[]> => {
  try {
    if (!selectedManufacturer) {
      throw new Error("Manufacturer is required.");
    }

    const response = await v2EngineAxiosInstance.get<{ data: Model[] }>(`/models/${selectedManufacturer}`);
    const data = response.data;

    if (!data || !Array.isArray(data.data)) return [];

    const sortedModels = data.data
      .map((item) => ({
        label: item.model,
        value: item.vehicle_model_id,
        years: item.year_range,
        vehicle_variants: item.vehicle_variants,
      }))
      .sort((a, b) => a.label.localeCompare(b.label));
    return sortedModels;
  } catch (error) {
    console.error("Error fetching models:", error);
    return [];
  }
};

// Get submodels (variants) by model, manufacturer, and year
export const getSubModelByModel = async (
  selectedModel: number | string,
  selectedManufacturer: number | string,
  selectedYear: number | string
): Promise<
  { label: string; value: number; manufacture_year: number }[]
> => {
  try {
    if (!selectedModel || !selectedManufacturer || !selectedYear) {
      throw new Error("Model, Manufacturer, and Year are required");
    }

    const response = await v2EngineAxiosInstance.get<{ data: Model[] }>(`/models/${selectedManufacturer}`);
    const data = response.data;

    if (!data || !Array.isArray(data.data)) {
      throw new Error("Invalid API response: Missing or incorrect 'data' structure");
    }

    const modelData = data.data.find(
      (m) => m.vehicle_model_id === Number(selectedModel)
    );

    if (!modelData) {
      throw new Error(`No matching model found for model ID: ${selectedModel}`);
    }

    if (!Array.isArray(modelData.vehicle_variants) || modelData.vehicle_variants.length === 0) {
      return [];
    }

    // Filter variants by selectedYear
    const uniqueVariants: Record<number, { label: string; value: number; manufacture_year: number }> = {};
    modelData.vehicle_variants
      .filter(
        (variant) =>
          typeof variant.manufacture_year !== "undefined" &&
          Number(variant.manufacture_year) === Number(selectedYear)
      )
      .forEach((variant) => {
        if (variant.variant_id && variant.vehicle_model_id) {
          uniqueVariants[variant.variant_id] = {
            label: variant.name,
            value: variant.variant_id,
            manufacture_year: variant.manufacture_year,
          };
        }
      });

    return Object.values(uniqueVariants).sort((a, b) => a.label.localeCompare(b.label));
  } catch (error) {
    console.error("Error fetching sub-models and variants:", error);
    return [];
  }
};