import NextAuth, {
  AuthOptions,
  Session,
  User as NextA<PERSON>User,
} from "next-auth";
import type { Account, Profile } from "next-auth";
import type { AdapterUser } from "next-auth/adapters";
import Credential<PERSON>Provider from "next-auth/providers/credentials";
import { JWT } from "next-auth/jwt";
import axios from "axios";

// Extend NextAuth types using module augmentation if you want type safety everywhere
declare module "next-auth" {
  interface Session {
    user: {
      id?: string;
      token?: string;
      role?: string;
      organizationId?: string;
      // ...other user fields
    };
  }
  interface User {
    user_id?: string;
    access_token?: string;
    role?: string;
    organization_id?: string;
    // ...other user fields
  }
}
declare module "next-auth/jwt" {
  interface JWT {
    id?: string;
    token?: string;
    role?: string;
    organizationId?: string;
  }
}

const authOptions: AuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        try {
          const res = await axios.post(
            `${process.env.WHITEBOOK_API_URL_V2}/login`,
            {
              email: credentials?.email,
              password: credentials?.password,
            },
          );
          if (res.status === 200) {
            return res.data.data;
          } else {
            return null;
          }
        } catch (error) {
          console.error("Error during signin:", error);
          throw new Error("Invalid username or password");
        }
      },
    }),
  ],
  callbacks: {
    async jwt({
      token,
      user,
    }: {
      token: JWT;
      user?: NextAuthUser | AdapterUser;
      account?: Account | null;
      profile?: Profile;
      trigger?: "signIn" | "signUp" | "update";
      isNewUser?: boolean;
      session?: unknown;
    }) {
      if (user && "user_id" in user) {
        token.id = (user as NextAuthUser).user_id;
        token.organizationId = (user as NextAuthUser).organization_id;
        token.token = (user as NextAuthUser).access_token;
        token.role = (user as NextAuthUser).role;
      }
      return token;
    },
    async session({ session, token }: { session: Session; token: JWT }) {
      session.user.id = token.id;
      session.user.token = token.token;
      session.user.role = token.role;
      session.user.organizationId = token.organizationId;
      return session;
    },
    async redirect({ url, baseUrl }) {
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
  },
  pages: {
    signIn: "/auth/sign-in",
  },
  secret: `${process.env.NEXTAUTH_SECRET}`,
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
