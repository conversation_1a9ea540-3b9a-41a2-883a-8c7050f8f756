"use client";

import React, { useEffect, useState } from "react";
import OrganizationDetailsForm from "@/components/Forms/Onboarding/Organization/OrganizationDetailsForm";
import OnboardIndividualForm from "@/components/Forms/Onboarding/Individual/OnboardIndividualForm";
import AddAdminUserForm from "@/components/Forms/Onboarding/Organization/AddAdminUserForm";
import AddRegisteredUserForm from "@/components/Forms/Onboarding/Organization/AddRegisteredUserForm";
import OnboardingSummary from "@/components/Onboarding/summary/OnboardingSummary";
import {
  adminUserSchema,
  organizationDetailsSchema,
  registeredUserSchema,
} from "@/components/Forms/Onboarding/schemas/schemas";
import StepIndicator from "@/components/FormElements/StepIndicator";
import AdminUsersTable from "@/components/Tables/Onboarding/AdminUsersTable";
import RegisteredUsersTable from "@/components/Tables/Onboarding/RegisteredUsersTable";
import OnboardingTabs from "@/components/FormElements/OnboardingTabs";
import WBV<PERSON>ormHeader from "@/components/FormElements/WBVFormHeader";
import { toast, ToastContainer } from "react-toastify";

interface Role {
  id: number;
  name: string;
  label: string;
  description: string;
  permissions: Array<{
    id: number;
    name: string;
    description: string;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
  }>;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

interface BusinessType {
  id: number;
  name: string
  description: string
}

interface OrganizationAddress {
  street: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
}


const OnboardingPage: React.FC = () => {
  // UI control elements state
  const [activeTab, setActiveTab] = useState<"individual" | "organization">(
    "individual",
  );
  const [currentStep, setCurrentStep] = useState<number>(1);

  // individual user state
  const [individualUserFirstName, setIndividualUserFirstName] =
    useState<string>("");
  const [individualUserLastName, setIndividualUserLastName] =
    useState<string>("");
  const [individualUserEmail, setIndividualUserEmail] = useState<string>("");

  // organization details state
  const [companyName, setCompanyName] = useState<string>("");
  const [organizationEmail, setOrganizationEmail] = useState<string>("");
  const [tpin, setTpin] = useState<string>("");
  const [phoneNumber, setPhoneNumber] = useState<string>("");
  const [address, setAddress] = useState<OrganizationAddress>({
    street: "",
    city: "",
    state: "",
    zip_code: "",
    country: "",
  });
  const [businessTypeName, setBusinessTypeName] = useState<string>("");
  const [businessTypeId, setBusinessTypeId] = useState<number>(0);
  const [incorporationDocuments, setIncorporationDocuments] = useState<File[]>(
    [],
  );

  // error state
  const [organizationDetailsFormErrors, setOrganizationDetailsFormErrors] =
    useState<{ [key: string]: string }>({});
  const [adminUserFormErrors, setAdminUserFormErrors] = useState<{
    [key: string]: string;
  }>({});
  const [registeredUserFormErrors, setRegisteredUserFormErrors] = useState<{
    [key: string]: string;
  }>({});
  const [adminUsersAddedError, setAdminUsersAddedError] = useState<string>("");
  const [registeredUsersAddedError, setRegisteredUsersAddedError] =
    useState<string>("");
  const [documentsAddedError, setDocumentsAddedError] = useState<string>("");

  // admin user state
  const [adminUsers, setAdminUsers] = useState<
    {
      adminUserTitle: string;
      adminUserFirstName: string;
      adminUserLastName: string;
      adminUserEmail: string;
    }[]
  >([]);

  const [adminUserTitle, setAdminUserTitle] = useState<string>("");
  const [adminUserFirstName, setAdminUserFirstName] = useState<string>("");
  const [adminUserLastName, setAdminUserLastName] = useState<string>("");
  const [adminUserEmail, setAdminUserEmail] = useState<string>("");

  // registered user state
  const [registeredUsers, setRegisteredUsers] = useState<
    {
      registeredUserTitle: string;
      registeredUserFirstName: string;
      registeredUserLastName: string;
      registeredUserEmail: string;
      registeredUserRoleId: number;
    }[]
  >([]);
  const [registeredUserTitle, setRegisteredUserTitle] = useState<string>("");
  const [registeredUserFirstName, setRegisteredUserFirstName] =
    useState<string>("");
  const [registeredUserLastName, setRegisteredUserLastName] =
    useState<string>("");
  const [registeredUserEmail, setRegisteredUserEmail] = useState<string>("");
  const [registeredUserRoleId, setRegisteredUserRoleId] = useState<number>(0);
  const [roles, setRoles] = useState<Role[]>([]);
  const [error, setError] = useState<any>(null);

  const [businessTypes, setBusinessTypes] = useState<BusinessType[]>([]);

  const [toastMessage, setToastMessage] = useState<string | null>(null);
  const [toastError, setToastError] = useState<string | null>(null);

  const [submitted, setSubmitted] = useState<boolean>(false);

  useEffect(() => {
    if (toastMessage) {
      toast.success(toastMessage, {
        position: "bottom-right",
      });
      setToastMessage(null);
    }
    if (toastError) {
      toast.error(toastError, {
        position: "bottom-right",
      });
      setToastError(null);
    }
  }, [toastMessage, toastError]);


  useEffect(() => {
    const fetchBusinessTypes = async () => {
      try {
        const response = await fetch("/api/organizationTypes", {
          method: "GET",
        });
        const data = await response.json();
        setBusinessTypes(data.data)
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
      } catch (error) {
        console.error("Error fetching business types:", error);
      }
    };

    fetchBusinessTypes();
  }, []);


  useEffect(() => {
    const fetchRolesData = async () => {
      try {
        const response = await fetch("/api/roles", { method: "GET" });
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const result = await response.json();
        const filteredRoles = result.data.filter(
          (role: Role) => role.name.toLowerCase() !== "super admin",
        );
        setRoles(filteredRoles);
      } catch (error) {
        console.error("Error fetching roles:", error);
        setError(error);
        console.error("Error fetching roles:", error);
      }
    };

    fetchRolesData();
  }, []);

  useEffect(() => {
    const selectedBusinessType = businessTypes.find(
      (type) => type.id === businessTypeId
    );
    if (selectedBusinessType) {
      setBusinessTypeName(selectedBusinessType.name);
    }
  }, [businessTypeId, businessTypes]);

  const handleTabChange = (tab: "individual" | "organization") => {
    setActiveTab(tab);
    setCurrentStep(1);
  };

  const handleNextStep = () => {
    if (activeTab === "organization" && currentStep === 1) {
      const validationResult = organizationDetailsSchema.safeParse({
        companyName,
        organizationEmail,
        tpin,
        phoneNumber,
        address: {
          street: address.street,
          city: address.city,
          state: address.state,
          zip_code: address.zip_code,
          country: address.country,
        },
        businessTypeId,
        incorporationDocuments,
      });

      if (!validationResult.success) {
        const newErrors: { [key: string]: string } = {};
        validationResult.error.errors.forEach((error) => {
          console.log("error in handleNextStep", error);
          const path = error.path.join(".");
          newErrors[path] = error.message;
        });
        if (
          Object.keys(newErrors).length === 1 &&
          newErrors.hasOwnProperty("incorporationDocuments") &&
          incorporationDocuments.length > 0
        ) {
          setDocumentsAddedError("");
          setCurrentStep((prevStep) => prevStep + 1);
          return;
        }
        console.log("newErrors", newErrors);
        setOrganizationDetailsFormErrors(newErrors);
        return;
      } else {
        setOrganizationDetailsFormErrors({});
      }

      if (incorporationDocuments.length === 0) {
        setDocumentsAddedError(
          "At least one incorporation document is required",
        );
        return;
      } else {
        setDocumentsAddedError("");
      }
    } else if (activeTab === "organization" && currentStep === 2) {
      if (adminUsers.length === 0) {
        setAdminUsersAddedError("At least one admin user is required");
        return;
      }
      if (registeredUsers.length === 0) {
        setRegisteredUsersAddedError(
          "At least one registered user is required",
        );
        return;
      }
    }
    setCurrentStep((prevStep) => prevStep + 1);
  };

  const handlePrevStep = () => {
    setCurrentStep((prevStep) => prevStep - 1);
  };

  const handleStepChange = (step: number) => {
    setCurrentStep(step);
  };

  const handleAddAdminUser = (
    adminUserTitle: string,
    adminUserFirstName: string,
    adminUserLastName: string,
    adminUserEmail: string,
  ) => {
    const validationResult = adminUserSchema.safeParse({
      adminUserTitle,
      adminUserFirstName,
      adminUserLastName,
      adminUserEmail,
    });
    if (!validationResult.success) {
      const newErrors: { [key: string]: string } = {};
      validationResult.error.errors.forEach((error) => {
        newErrors[error.path[0]] = error.message;
      });
      setAdminUserFormErrors(newErrors);
      return;
    } else {
      setAdminUserFormErrors({});
      setAdminUsers([
        ...adminUsers,
        { adminUserTitle, adminUserFirstName, adminUserLastName, adminUserEmail },
      ]);
    }
  };

  const handleAddRegisteredUser = (
    registeredUserTitle: string,
    registeredUserFirstName: string,
    registeredUserLastName: string,
    registeredUserEmail: string,
    registeredUserRoleId: number,
  ) => {
    const validationResult = registeredUserSchema.safeParse({
      registeredUserTitle,
      registeredUserFirstName,
      registeredUserLastName,
      registeredUserEmail,
      registeredUserRoleId,
    });
    if (!validationResult.success) {
      const newErrors: { [key: string]: string } = {};
      validationResult.error.errors.forEach((error) => {
        newErrors[error.path[0]] = error.message;
      });
      setRegisteredUserFormErrors(newErrors);
      return;
    } else {
      setRegisteredUserFormErrors({});
      const role = roles.find(role => role.id === registeredUserRoleId)?.name || "";
      setRegisteredUsers([
        ...registeredUsers,
        {
          registeredUserTitle,
          registeredUserFirstName,
          registeredUserLastName,
          registeredUserEmail,
          registeredUserRoleId: registeredUserRoleId,
        },
      ]);
    }
  };

  const handleEditAdminUser = (
    index: number,
    updatedUser: {
      adminUserTitle: string;
      adminUserFirstName: string;
      adminUserLastName: string;
      adminUserEmail: string;
    },
  ) => {
    const updatedAdminUsers = [...adminUsers];
    updatedAdminUsers[index] = updatedUser;
    setAdminUsers(updatedAdminUsers);
  };

  const handleRemoveAdminUser = (index: number) => {
    const updatedAdminUsers = adminUsers.filter((_, i) => i !== index);
    setAdminUsers(updatedAdminUsers);
  };

  const handleEditRegisteredUser = (
    index: number,
    updatedUser: {
      registeredUserTitle: string;
      registeredUserFirstName: string;
      registeredUserLastName: string;
      registeredUserEmail: string;
      registeredUserRoleId: number;
    },
  ) => {
    const updatedRegisteredUsers = [...registeredUsers];
    updatedRegisteredUsers[index] = updatedUser;
    setRegisteredUsers(updatedRegisteredUsers);
  };

  const handleRemoveRegisteredUser = (index: number) => {
    const updatedRegisteredUsers = registeredUsers.filter(
      (_, i) => i !== index,
    );
    setRegisteredUsers(updatedRegisteredUsers);
  };

  const handleAddDocument = (file: File) => {
    setIncorporationDocuments([...incorporationDocuments, file]);
  };

  const handleRemoveDocument = (index: number) => {
    const updatedDocuments = incorporationDocuments.filter(
      (_, i) => i !== index,
    );
    setIncorporationDocuments(updatedDocuments);
  };

  const handleOrganizationSubmit = async () => {
    const formData = new FormData();
    formData.append("companyName", companyName);
    formData.append("tpin", tpin);
    formData.append("phoneNumber", phoneNumber);
    formData.append("organizationEmail", organizationEmail);
    formData.append("address", JSON.stringify(address));
    formData.append("businessTypeId", businessTypeId.toString());

    incorporationDocuments.forEach((file) => {
      formData.append("incorporationDocuments", file);
    });
    formData.append("adminUsers", JSON.stringify(adminUsers));
    formData.append("registeredUsers", JSON.stringify(registeredUsers));
    try {
      const response = await fetch("/api/onboard/organization", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        setSubmitted(false);
        setToastError("An error occurred. Please try again.");
      } else {
        setSubmitted(true);
        setToastMessage("Onboarding request submitted successfully.");

        // Reset all form fields, tables, and documents
        setCompanyName("");
        setOrganizationEmail("");
        setTpin("");
        setPhoneNumber("");
        setAddress({
          street: "",
          city: "",
          state: "",
          zip_code: "",
          country: "",
        });
        setBusinessTypeName("");
        setBusinessTypeId(0);
        setIncorporationDocuments([]);
        setAdminUsers([]);
        setRegisteredUsers([]);
        setAdminUserTitle("");
        setAdminUserFirstName("");
        setAdminUserLastName("");
        setAdminUserEmail("");
        setRegisteredUserTitle("");
        setRegisteredUserFirstName("");
        setRegisteredUserLastName("");
        setRegisteredUserEmail("");
        setRegisteredUserRoleId(0);
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  const isStep2Disabled =
    !companyName ||
    !organizationEmail ||
    !tpin ||
    !phoneNumber ||
    !address ||
    !businessTypeId ||
    incorporationDocuments.length === 0;

  const isStep3Disabled =
    isStep2Disabled || adminUsers.length === 0 || registeredUsers.length === 0;

  return (
    <>
      <div className="flex min-h-screen items-center justify-center bg-gray-100">
        <div
          className={`my-10 w-full ${activeTab === "organization" ? "max-w-3xl" : "max-w-md"} space-y-6 rounded-lg bg-white px-8 pb-8 pt-0 shadow-md`}
        >
          <WBVFormHeader title="Onboard" />
          <OnboardingTabs
            activeTab={activeTab}
            handleTabChange={handleTabChange}
            tab1="Individual"
            tab2="Organization"
          />
          {activeTab === "individual" ? (
            <OnboardIndividualForm
              formData={{
                first_name: individualUserFirstName,
                last_name: individualUserLastName,
                email: individualUserEmail,
                phone_number: phoneNumber,
                password: "", // Assuming you have a password field
              }}
              errors={{
                // Assuming you have error states for these fields
                first_name: "",
                last_name: "",
                email: "",
                phone_number: "",
                password: "",
              }}
              roles={roles} // Pass the roles state here
              handleChange={(e) => {
                const { name, value } = e.target;
                if (name === "first_name") setIndividualUserFirstName(value);
                if (name === "last_name") setIndividualUserLastName(value);
                if (name === "email") setIndividualUserEmail(value);
              }}
              handlePhoneChange={setPhoneNumber}
              handleSubmit={(e) => {
                e.preventDefault();
                // Handle form submission
              }}
              showPassword={false} // Assuming you have a state for this
              setShowPassword={() => { }} // Assuming you have a function for this
            />
          ) : (
            <>
              {currentStep === 1 && (
                <OrganizationDetailsForm
                  companyName={companyName}
                  tpin={tpin}
                  phoneNumber={phoneNumber}
                  email={organizationEmail}
                  address={address}
                  incorporationDocuments={incorporationDocuments}
                  setCompanyName={setCompanyName}
                  setTpin={setTpin}
                  setPhoneNumber={setPhoneNumber}
                  setEmail={setOrganizationEmail}
                  setAddress={setAddress}
                  handleAddDocument={handleAddDocument}
                  handleRemoveDocument={handleRemoveDocument}
                  errors={organizationDetailsFormErrors}
                  documentsAddedError={documentsAddedError}
                  businessTypes={businessTypes}
                  businessTypeId={businessTypeId}
                  setBusinessTypeId={setBusinessTypeId}
                />
              )}
              {currentStep === 2 && (
                <div>
                  <div className="flex flex-col space-y-4 md:flex-row md:space-x-4 md:space-y-0">
                    <div className="w-full rounded-md bg-white p-4 shadow-md md:w-1/2">
                      <h3 className="text-lg font-semibold">Add Admin User</h3>
                      {adminUsers.length === 0 ? (
                        <AddAdminUserForm
                          handleAddAdminUser={handleAddAdminUser}
                          adminUserTitle={adminUserTitle}
                          setAdminUserTitle={setAdminUserTitle}
                          adminUserFirstName={adminUserFirstName}
                          setAdminUserFirstName={setAdminUserFirstName}
                          adminUserLastName={adminUserLastName}
                          setAdminUserLastName={setAdminUserLastName}
                          adminUserEmail={adminUserEmail}
                          setAdminUserEmail={setAdminUserEmail}
                          errors={adminUserFormErrors}
                        />
                      ) : (
                        <p className="text-sm text-gray-500">
                          An admin user has been added.
                        </p>
                      )}
                    </div>
                    <div className="w-full rounded-md bg-white p-4 shadow-md md:w-1/2">
                      <h3 className="text-lg font-semibold">
                        Add Registered Users
                      </h3>
                      <AddRegisteredUserForm
                        handleAddRegisteredUser={handleAddRegisteredUser}
                        registeredUserTitle={registeredUserTitle}
                        setRegisteredUserTitle={setRegisteredUserTitle}
                        registeredUserFirstName={registeredUserFirstName}
                        setRegisteredUserFirstName={setRegisteredUserFirstName}
                        registeredUserLastName={registeredUserLastName}
                        setRegisteredUserLastName={setRegisteredUserLastName}
                        registeredUserEmail={registeredUserEmail}
                        setRegisteredUserEmail={setRegisteredUserEmail}
                        registeredUserRoleId={registeredUserRoleId}
                        setRegisteredUserRoleId={setRegisteredUserRoleId}
                        errors={registeredUserFormErrors}
                        roles={roles}
                      />
                    </div>
                  </div>
                  {adminUsers.length > 0 ? (
                    <div className="mt-6">
                      <h3 className="text-lg font-semibold">Admin User</h3>
                      <AdminUsersTable
                        users={adminUsers}
                        handleEditUser={handleEditAdminUser}
                        handleRemoveUser={handleRemoveAdminUser}
                      />
                    </div>
                  ) : (
                    <div className="mt-6">
                      <p className="text-sm text-red-500">
                        {adminUsersAddedError}
                      </p>
                    </div>
                  )}
                  {registeredUsers.length > 0 ? (
                    <div className="mt-6">
                      <h3 className="text-lg font-semibold">Registered Users</h3>
                      <RegisteredUsersTable
                        roles={roles}
                        users={registeredUsers}
                        handleEditUser={handleEditRegisteredUser}
                        handleRemoveUser={handleRemoveRegisteredUser}
                      />
                    </div>
                  ) : (
                    <div className="mt-6">
                      <p className="text-sm text-red-500">
                        {registeredUsersAddedError}
                      </p>
                    </div>
                  )}
                </div>
              )}
              {currentStep === 3 && !submitted ? (
                <OnboardingSummary
                  companyName={companyName}
                  tpin={tpin}
                  phoneNumber={phoneNumber}
                  organizationEmail={organizationEmail}
                  address={address}
                  businessTypeName={businessTypeName}
                  adminUsers={adminUsers}
                  registeredUsers={registeredUsers}
                  incorporationDocuments={incorporationDocuments}
                  handlePrevStep={handlePrevStep}
                  handleSubmit={handleOrganizationSubmit}
                  roles={roles}
                />
              ) : currentStep === 3 && submitted ? (
                <div className="text-center">
                  <div className="flex justify-center mb-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="w-8 h-8 text-green-500"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 00-1.414 0L9 11.586 5.707 8.293a1 1 0 10-1.414 1.414l4 4a1 1 0 001.414 0l7-7a1 1 0 000-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <p className="text-lg font-semibold text-wbv-theme">
                    Onboarding request submitted Successfully.
                  </p>
                </div>
              ) : null}
              <div className="mt-4 flex justify-between">
                {currentStep > 1 && currentStep < 3 && (
                  <button
                    className="rounded-md bg-gray-200 px-4 py-2 font-semibold text-gray-700 transition duration-300 hover:bg-gray-300"
                    onClick={handlePrevStep}
                  >
                    Previous
                  </button>
                )}
                {currentStep < 3 && (
                  <button
                    className={`rounded-md bg-wbv-theme px-4 py-2 font-semibold text-white transition duration-300 hover:bg-opacity-90 ${currentStep === 1 ? "ml-auto" : ""}`}
                    onClick={handleNextStep}
                  >
                    Next
                  </button>
                )}
              </div>
            </>
          )}
        </div>
      </div>
      <ToastContainer />
    </>
  );
};

export default OnboardingPage;
