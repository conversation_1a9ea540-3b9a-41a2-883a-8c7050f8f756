import React from 'react';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlus, faTrash } from "@fortawesome/free-solid-svg-icons";
import { ProductCustomDetail } from '@/types/product/index';
import { ProductDetailsSectionProps } from '@/types/product/modal';

const ProductDetailsSection: React.FC<ProductDetailsSectionProps> = ({
  productDetails,
  onAddDetail,
  onChangeDetail,
  onRemoveDetail,
  isLoading = false
}) => {
  return (
    <div className="col-span-2">
      <div className="mb-2 flex items-center justify-between">
        <label className="text-sm font-medium">Product Details</label>
        <button
          type="button"
          onClick={onAddDetail}
          disabled={isLoading}
          className={`flex items-center rounded bg-wbv-theme px-2 py-1 text-xs text-white hover:bg-wbv-theme ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <FontAwesomeIcon icon={faPlus} className="mr-1" />
          Add Field
        </button>
      </div>

      {productDetails.map((detail, index) => (
        <div key={index} className="mb-2 grid grid-cols-[1fr_1fr_1fr_auto] items-center gap-2">
          <input
            type="text"
            value={detail.customFieldName}
            onChange={(e) => onChangeDetail(index, 'customFieldName', e.target.value)}
            placeholder="Custom Field Name"
            className={`rounded border border-gray-300 p-2 text-sm focus:border-wbv-theme focus:outline-none focus:ring-2 focus:ring-wbv-theme ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={isLoading}
            required
          />
          <select
            value={detail.fieldType}
            onChange={(e) => onChangeDetail(index, 'fieldType', e.target.value)}
            className={`rounded border border-gray-300 p-2 text-sm focus:border-wbv-theme focus:outline-none focus:ring-2 focus:ring-wbv-theme ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={isLoading}
            required
          >
            <option value="">Select Type</option>
            <option value="text">Text</option>
            <option value="number">Number</option>
            <option value="decimal">Decimal</option>
            <option value="boolean">Boolean</option>
          </select>
          {detail.fieldType === 'boolean' ? (
            <select
              value={detail.value}
              onChange={(e) => onChangeDetail(index, 'value', e.target.value)}
              className={`rounded border border-gray-300 p-2 text-sm focus:border-wbv-theme focus:outline-none focus:ring-2 focus:ring-wbv-theme ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
              disabled={isLoading}
              required
            >
              <option value="false">false</option>
              <option value="true">true</option>
            </select>
          ) : (
            <input
              type={detail.fieldType === 'number' ? 'number' : detail.fieldType === 'decimal' ? 'number' : 'text'}
              value={detail.value}
              onChange={(e) => onChangeDetail(index, 'value', e.target.value)}
              placeholder="Value"
              className={`rounded border border-gray-300 p-2 text-sm focus:border-wbv-theme focus:outline-none focus:ring-2 focus:ring-wbv-theme ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
              disabled={isLoading}
              step={detail.fieldType === 'decimal' ? '0.01' : detail.fieldType === 'number' ? '1' : undefined}
            />
          )}
          <button
            type="button"
            onClick={() => onRemoveDetail(index)}
            className={`ml-2 text-red-500 hover:text-red-700 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={isLoading}
            title="Remove Field"
          >
            <FontAwesomeIcon icon={faTrash} />
          </button>
        </div>
      ))}
      {productDetails.length === 0 && (
        <p className="mt-2 text-sm text-gray-500 italic">No custom product details added yet.</p>
      )}
    </div>
  );
};

export default ProductDetailsSection;
