"use client";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import React from "react";
import {
  getMakeResponse,
  getMakesResponse,
  getModelResponse,
  getModelsResponse,
  getVariantResponse,
  getVariantsResponse,
} from "./exampleResponses";

const DeveloperSandbox: React.FC = () => {
  return (
    <DefaultLayout>
      <Breadcrumb pageName="Developer Sandbox" />
      <div className="container mx-auto p-4">
        {/* Introduction */}
        <section className="mb-8">
          <h2 className="mb-4 text-2xl font-semibold">Introduction</h2>
          <p>
            Welcome to the Developer Sandbox. This page provides comprehensive
            instructions on how to interact with our API endpoints. Below
            you&apos;ll find details on available endpoints, including their
            methods, URLs, required parameters, and example requests/responses.
          </p>
        </section>

        {/* Endpoints Documentation */}
        <section>
          <h2 className="mb-4 text-2xl font-semibold">API Endpoints</h2>

          {/* Vehicles Endpoints */}
          <div className="mb-6">
            <h3 className="mb-2 text-xl font-semibold">Vehicles</h3>

            {/* Get All Makes */}
            <div className="mb-4">
              <h4 className="text-lg font-medium">GET /api/v2/makes</h4>
              <p className="mb-2">Retrieve a list of all vehicle makes.</p>
              <h5 className="font-medium">Request:</h5>
              <pre className="overflow-x-auto rounded-lg bg-gray-800 p-4 text-white">
                <code className="font-mono">{`GET /api/v2/makes`}</code>
              </pre>
              <h5 className="mt-2 font-medium">Response:</h5>
              <pre className="overflow-x-auto rounded-lg bg-gray-800 p-4 text-white">
                <code className="font-mono">{getMakesResponse}</code>
              </pre>
            </div>

            {/* Get Make by ID */}
            <div className="mb-4">
              <h4 className="text-lg font-medium">
                GET /api/v2/makes/{"{id}"}
              </h4>
              <p className="mb-2">Retrieve a make by their ID.</p>
              <h5 className="font-medium">Request:</h5>
              <pre className="overflow-x-auto rounded-lg bg-gray-800 p-4 text-white">
                <code className="font-mono">{`GET /api/v2/makes/1`}</code>
              </pre>
              <h5 className="mt-2 font-medium">Response:</h5>
              <pre className="overflow-x-auto rounded-lg bg-gray-800 p-4 text-white">
                <code className="font-mono">{getMakeResponse}</code>
              </pre>
            </div>

            {/* Get All Models */}
            <div className="mb-4">
              <h4 className="text-lg font-medium">GET /api/v2/models</h4>
              <p className="mb-2">Retrieve a list of all vehicle models.</p>
              <h5 className="font-medium">Request:</h5>
              <pre className="overflow-x-auto rounded-lg bg-gray-800 p-4 text-white">
                <code className="font-mono">{`GET /api/v2/models`}</code>
              </pre>
              <h5 className="mt-2 font-medium">Response:</h5>
              <pre className="overflow-x-auto rounded-lg bg-gray-800 p-4 text-white">
                <code className="font-mono">{getModelsResponse}</code>
              </pre>
            </div>

            {/* Get Model by ID */}
            <div className="mb-4">
              <h4 className="text-lg font-medium">
                GET /api/v2/models/{"{id}"}
              </h4>
              <p className="mb-2">Retrieve a model by their ID.</p>
              <h5 className="font-medium">Request:</h5>
              <pre className="overflow-x-auto rounded-lg bg-gray-800 p-4 text-white">
                <code className="font-mono">{`GET /api/v2/models/1`}</code>
              </pre>
              <h5 className="mt-2 font-medium">Response:</h5>
              <pre className="overflow-x-auto rounded-lg bg-gray-800 p-4 text-white">
                <code className="font-mono">{getModelResponse}</code>
              </pre>
            </div>

            {/* Get All Variants */}
            <div className="mb-4">
              <h4 className="text-lg font-medium">GET /api/v2/variants</h4>
              <p className="mb-2">Retrieve a list of all vehicle variants.</p>
              <h5 className="font-medium">Request:</h5>
              <pre className="overflow-x-auto rounded-lg bg-gray-800 p-4 text-white">
                <code className="font-mono">{`GET /api/v2/variants`}</code>
              </pre>
              <h5 className="mt-2 font-medium">Response:</h5>
              <pre className="overflow-x-auto rounded-lg bg-gray-800 p-4 text-white">
                <code className="font-mono">{getVariantsResponse}</code>
              </pre>
            </div>

            {/* Get Variant by ID */}
            <div className="mb-4">
              <h4 className="text-lg font-medium">
                GET /api/v2/variants/{"{id}"}
              </h4>
              <p className="mb-2">Retrieve a variant by their ID.</p>
              <h5 className="font-medium">Request:</h5>
              <pre className="overflow-x-auto rounded-lg bg-gray-800 p-4 text-white">
                <code className="font-mono">{`GET /api/v2/variants/1`}</code>
              </pre>
              <h5 className="mt-2 font-medium">Response:</h5>
              <pre className="overflow-x-auto rounded-lg bg-gray-800 p-4 text-white">
                <code className="font-mono">{getVariantResponse}</code>
              </pre>
            </div>
          </div>
        </section>
      </div>
    </DefaultLayout>
  );
};

export default DeveloperSandbox;
