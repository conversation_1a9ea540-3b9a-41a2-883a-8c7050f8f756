import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSpinner } from "@fortawesome/free-solid-svg-icons";
import { getManufacturers, getModelsByManufacturer, getSubModelByModel } from '../../app/api/vehicle_endpoints/vehicleApi';

interface VehicleDetails {
  submodelId: number;
  manufacturerName: string;
  modelName: string;
  year: number;
  submodelName: string;
}

interface VehicleDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (details: VehicleDetails) => void;
}

const VehicleDetailsModal: React.FC<VehicleDetailsModalProps> = ({
  isOpen,
  onClose,
  onSelect
}) => {
  const [manufacturers, setManufacturers] = useState<{ id: number, name: string }[]>([]);
  const [models, setModels] = useState<{ id: number, name: string, years: number[] }[]>([]);
  const [availableYears, setAvailableYears] = useState<number[]>([]);
  const [submodels, setSubmodels] = useState<{ id: number, name: string }[]>([]);

  const [selectedManufacturer, setSelectedManufacturer] = useState<number | null>(null);
  const [selectedModel, setSelectedModel] = useState<number | null>(null);
  const [selectedYear, setSelectedYear] = useState<number | null>(null);
  const [selectedSubmodel, setSelectedSubmodel] = useState<number | null>(null);

  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch manufacturers when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchManufacturers();
      setSelectedManufacturer(null);
      setSelectedModel(null);
      setSelectedYear(null);
      setSelectedSubmodel(null);
      setModels([]);
      setAvailableYears([]);
      setSubmodels([]);
    }
  }, [isOpen]);

  // Fetch models when manufacturer changes
  useEffect(() => {
    if (selectedManufacturer) {
      fetchModels(selectedManufacturer);
      setSelectedModel(null);
      setSelectedYear(null);
      setSelectedSubmodel(null);
      setAvailableYears([]);
      setSubmodels([]);
    }
  }, [selectedManufacturer]);

  // When a model is selected, update available years
  useEffect(() => {
    if (selectedModel && models.length > 0) {
      const foundModel = models.find((m) => m.id === selectedModel);
      if (foundModel && Array.isArray(foundModel.years)) {

        // Sort years in descending order (newest to oldest)
        const sortedYears = [...foundModel.years].sort((a, b) => b - a);

        setAvailableYears(sortedYears);
      } else {
        setAvailableYears([]);
      }
      setSelectedYear(null);
      setSelectedSubmodel(null);
      setSubmodels([]);
    }
  }, [selectedModel, models]);

  // Fetch submodels when year changes
  useEffect(() => {
    if (selectedModel && selectedManufacturer && selectedYear) {
      fetchSubmodels(selectedModel, selectedManufacturer, selectedYear);
      setSelectedSubmodel(null);
    }
  }, [selectedYear, selectedModel, selectedManufacturer]);

  const fetchSubmodels = async (modelId: number, manufacturerId: number, year: number) => {
    setLoading(true);
    setError(null);
    try {
      const data = await getSubModelByModel(modelId, manufacturerId, year);
      setSubmodels(data.map((item: any) => ({
        id: item.value,
        name: item.label,
      })));
    } catch (error) {
      setError(error instanceof Error ? error.message : "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  // --- API-based fetch functions ---
  const fetchManufacturers = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await getManufacturers();
      setManufacturers(data.map((item: any) => ({
        id: item.value,
        name: item.label,
      })));
    } catch (error) {
      setError(error instanceof Error ? error.message : "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  const fetchModels = async (manufacturerId: number) => {
    setLoading(true);
    setError(null);
    try {
      const data = await getModelsByManufacturer(manufacturerId);
      setModels(data.map((item: any) => ({
        id: item.value,
        name: item.label,
        years: item.years,
      })));
    } catch (error) {
      setError(error instanceof Error ? error.message : "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  const handleSelect = () => {
    if (selectedSubmodel && selectedManufacturer && selectedModel && selectedYear) {
      // Find the selected manufacturer, model, and submodel names
      const manufacturerName = manufacturers.find(m => m.id === selectedManufacturer)?.name || '';
      const modelName = models.find(m => m.id === selectedModel)?.name || '';
      const submodelName = submodels.find(s => s.id === selectedSubmodel)?.name || '';

      // Create a complete vehicle details object
      const vehicleDetails: VehicleDetails = {
        submodelId: selectedSubmodel,
        manufacturerName,
        modelName,
        year: selectedYear,
        submodelName
      };

      onSelect(vehicleDetails);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[10000] flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className="w-full max-w-md rounded-lg bg-white shadow-lg">
        <div className="border-b p-4">
          <h2 className="text-lg font-bold">Select Vehicle Details</h2>
        </div>
        <div className="p-4">
          {error && (
            <div className="mb-4 rounded bg-red-100 p-2 text-red-700">
              Error: {error}
            </div>
          )}
          <div className="space-y-4">
            {/* Manufacturer Dropdown */}
            <div>
              <label className="mb-1 block text-sm font-medium">Manufacturer</label>
              <select
                value={selectedManufacturer || ''}
                onChange={(e) => setSelectedManufacturer(e.target.value ? parseInt(e.target.value, 10) : null)}
                className="w-full rounded border border-gray-300 p-2 focus:border-wbv-theme focus:outline-none focus:ring-2 focus:ring-wbv-theme"
                disabled={loading || manufacturers.length === 0}
              >
                <option value="">Select Manufacturer</option>
                {manufacturers.map((manufacturer) => (
                  <option key={manufacturer.id} value={manufacturer.id}>
                    {manufacturer.name}
                  </option>
                ))}
              </select>
            </div>
            {/* Model Dropdown */}
            <div>
              <label className="mb-1 block text-sm font-medium">Model</label>
              <select
                value={selectedModel || ''}
                onChange={(e) => setSelectedModel(e.target.value ? parseInt(e.target.value, 10) : null)}
                className="w-full rounded border border-gray-300 p-2 focus:border-wbv-theme focus:outline-none focus:ring-2 focus:ring-wbv-theme"
                disabled={loading || !selectedManufacturer || models.length === 0}
              >
                <option value="">Select Model</option>
                {models.map((model) => (
                  <option key={model.id} value={model.id}>
                    {model.name}
                  </option>
                ))}
              </select>
            </div>
            {/* Year Dropdown */}
            <div>
              <label className="mb-1 block text-sm font-medium">Year</label>
              <select
                value={selectedYear || ''}
                onChange={(e) => setSelectedYear(e.target.value ? parseInt(e.target.value, 10) : null)}
                className="w-full rounded border border-gray-300 p-2 focus:border-wbv-theme focus:outline-none focus:ring-2 focus:ring-wbv-theme"
                disabled={loading || !selectedModel || availableYears.length === 0}
              >
                <option value="">Select Year</option>
                {availableYears.map((year) => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
            </div>
            {/* Submodel Dropdown */}
            <div>
              <label className="mb-1 block text-sm font-medium">Submodel</label>
              <select
                value={selectedSubmodel || ''}
                onChange={(e) => setSelectedSubmodel(e.target.value ? parseInt(e.target.value, 10) : null)}
                className="w-full rounded border border-gray-300 p-2 focus:border-wbv-theme focus:outline-none focus:ring-2 focus:ring-wbv-theme"
                disabled={loading || !selectedYear || submodels.length === 0}
              >
                <option value="">Select Submodel</option>
                {submodels.map((submodel) => (
                  <option key={submodel.id} value={submodel.id}>
                    {submodel.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
        <div className="flex justify-end space-x-2 border-t p-4">
          <button
            type="button"
            onClick={onClose}
            className="rounded bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleSelect}
            className="rounded bg-wbv-theme px-4 py-2 text-white hover:bg-opacity-90"
            disabled={loading || !selectedSubmodel}
          >
            {loading ? (
              <span className="flex items-center">
                <FontAwesomeIcon icon={faSpinner} spin className="mr-2" />
                Loading...
              </span>
            ) : (
              "Select"
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default VehicleDetailsModal;