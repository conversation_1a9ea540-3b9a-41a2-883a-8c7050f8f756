export interface Role {
  id: number;
  name: string;
  label: string;
  description: string;
  permissions: Array<{
    id: number;
    name: string;
    description: string;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
  }>;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface SearchTerm {
  manufacturer: string;
  model: string;
  year: number;
  engine_size: number;
  transmission: string;
  sub_model: string;
  variant_id: number;
  car_type: string;
  regnum: string;
  minimum_price: string;
  maximum_price: string;
}

export interface TopSearch {
  search_term: SearchTerm;
  count: number;
  fair_market_value: number;
}

export interface BillingSummary {
  pending_invoices: number;
  total_due: number;
  currency: string;
}

export interface DashboardResponse {
  top_searches: TopSearch[];
  billing_summary: BillingSummary;
}

// Wrapper type for the API response with a top-level "data" key
export interface ApiResponse<T> {
  data: T;
}
