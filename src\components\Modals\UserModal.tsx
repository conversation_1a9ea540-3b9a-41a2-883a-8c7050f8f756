import React, { useEffect, useState } from "react";
import PhoneInput from "react-phone-input-2";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUserEdit, faUserPlus } from "@fortawesome/free-solid-svg-icons";

interface User {
  id: number;
  email: string;
  phone_number: string;
  title: string;
  first_name: string;
  last_name: string;
  role_id: number;
}

interface Role {
  id: number;
  name: string;
  label: string;
  description: string;
  permissions: Array<{
    id: number;
    name: string;
    description: string;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
  }>;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

interface UserModalProps {
  selectedUser?: Partial<User> | null;
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => void;
  newUser?: Partial<User> | null;
  handlePhoneChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const UserModal: React.FC<UserModalProps> = ({
  selectedUser,
  onSubmit,
  newUser,
  handleChange,
  handlePhoneChange,
  setIsModalOpen,
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<any>(null);
  const [roles, setRoles] = useState<Role[]>([]);

  useEffect(() => {
    const fetchRolesData = async () => {
      try {
        const response = await fetch("/api/roles", { method: "GET" });
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const result = await response.json();
        const filteredRoles = result.data.filter(
          (role: Role) => role.name.toLowerCase() !== "super admin",
        );
        setRoles(filteredRoles);
      } catch (error) {
        setError(error);
        setIsModalOpen(false);
        console.error("Error fetching roles:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchRolesData();
  }, [setIsModalOpen]);

  if (loading)
    return (
      <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50">
        Loading...
      </div>
    );
  if (error) return <div className="text-red-500">Error: {error}</div>;

  return (
    <>
      <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50 p-4">
        <div className="w-full max-w-2xl rounded-lg bg-white p-6 shadow-lg">
          <div className="flex flex-col items-center">
            <FontAwesomeIcon
              icon={selectedUser ? faUserEdit : faUserPlus}
              className="mb-2 text-3xl text-wbv-theme"
            />
            <h2 className="mb-2 text-center text-xl font-bold">
              {selectedUser ? "Update" : "Add"} User
            </h2>
            <div className="mb-4 h-1 w-full bg-gradient-to-r from-transparent via-wbv-theme to-transparent"></div>
          </div>
          <form onSubmit={onSubmit}>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="flex flex-col">
                <label className="mb-1">Title</label>
                <select
                  name="title"
                  value={newUser?.title}
                  onChange={handleChange}
                  className="rounded border border-gray-300 p-2 placeholder-gray-400"
                  style={{ height: "2.75rem" }}
                  required
                >
                  <option value="" disabled className="text-gray-400">
                    Select Title
                  </option>
                  <option value="Sir">Sir</option>
                  <option value="Dr">Dr</option>
                  <option value="Mr">Mr</option>
                  <option value="Mrs">Mrs</option>
                  <option value="Ms">Ms</option>
                </select>
              </div>

              <div className="flex flex-col">
                <label className="mb-1">First Name</label>
                <input
                  type="text"
                  name="first_name"
                  value={newUser?.first_name}
                  onChange={handleChange}
                  placeholder="John"
                  className="rounded border border-gray-300 p-2 placeholder-gray-400"
                  style={{ height: "2.75rem" }}
                  required
                />
              </div>

              <div className="flex flex-col">
                <label className="mb-1">Last Name</label>
                <input
                  type="text"
                  name="last_name"
                  value={newUser?.last_name}
                  onChange={handleChange}
                  placeholder="Doe"
                  className="rounded border border-gray-300 p-2 placeholder-gray-400"
                  style={{ height: "2.75rem" }}
                  required
                />
              </div>

              <div className="flex flex-col">
                <label className="mb-1">Email</label>
                <input
                  type="email"
                  name="email"
                  value={newUser?.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  className="rounded border border-gray-300 p-2 placeholder-gray-400"
                  style={{ height: "2.75rem" }}
                  required
                />
              </div>

              <div className="flex flex-col">
                <label className="mb-1">Phone Number</label>
                <PhoneInput
                  country={"zm"}
                  value={newUser?.phone_number}
                  onChange={handlePhoneChange}
                  inputProps={{
                    name: "phone_number",
                    required: true,
                    autoFocus: false,
                  }}
                  inputStyle={{
                    width: "100%",
                    height: "2.75rem",
                    borderRadius: "0.375rem",
                  }}
                  enableSearch
                />
              </div>

              <div className="flex flex-col">
                <label className="mb-1">Role</label>
                <select
                  name="role_id"
                  value={newUser?.role_id}
                  onChange={handleChange}
                  className="rounded border border-gray-300 p-2 placeholder-gray-400"
                  style={{ height: "2.75rem" }}
                  required
                >
                  <option value={0} disabled className="text-gray-400">
                    Select Role
                  </option>
                  {roles.map((role) => (
                    <option key={role.id} value={Number(role.id)}>
                      {role.name.charAt(0).toUpperCase() + role.name.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="mt-4 flex justify-center">
              <button
                type="button"
                onClick={() => setIsModalOpen(false)}
                className="mr-2 rounded bg-gray-500 p-2 text-white hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="rounded bg-wbv-theme p-2 text-white hover:bg-opacity-90"
              >
                {selectedUser ? "Submit" : "Add User"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default UserModal;
