import { faDownload } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React from "react";
import * as XLSX from "xlsx/xlsx.mjs";

interface DownloadBtnProps {
  data: any[];
  fileName: string;
}

const DownloadBtn: React.FC<DownloadBtnProps> = ({ data = [], fileName }) => {
  const handleDownload = () => {
    const downloadData = data?.length ? data : [];
    // Extract Data (create a workbook object from the table)
    const worksheet = XLSX.utils.json_to_sheet(downloadData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
    XLSX.writeFile(workbook, fileName ? `${fileName}.xlsx` : "data.xlsx");
  };

  return (
    <button
      className="ml-2 inline-flex items-center rounded bg-wbv-theme px-4 py-2 text-xs font-semibold text-white hover:bg-opacity-90"
      onClick={handleDownload}
    >
      <FontAwesomeIcon
        className="mr-2 h-4 w-4 fill-current"
        icon={faDownload}
      />
      <span>Download</span>
    </button>
  );
};

export default DownloadBtn;
