import { NextRequest, NextResponse } from "next/server";
import { Client as MinioClient } from 'minio';
import axios from "axios";

// Load environment variables
const {
    S3_ENDPOINT,
    S3_BUCKET,
    S3_USE_SSL,
    S3_ACCESS_KEY,
    S3_SECRET_KEY,
    S3_ONBOARDING_DOCUMENTS_PATH,
} = process.env;

export const runtime = 'nodejs'; // Update the page config

const minioClient = new MinioClient({
    endPoint: S3_ENDPOINT!,
    port: S3_USE_SSL === 'true' ? 443 : 80,
    useSSL: S3_USE_SSL === 'true',
    accessKey: S3_ACCESS_KEY!,
    secretKey: S3_SECRET_KEY!,
});

export async function POST(req: NextRequest) {
    if (req.method !== 'POST') {
        return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
    }

    try {
        const formData = await req.formData();
        console.log("Received form data:", formData);

        // Directly parse JSON fields from the form data
        let adminUsers, registeredUsers;

        try {
            adminUsers = JSON.parse(formData.get("adminUsers") as string);
            registeredUsers = JSON.parse(formData.get("registeredUsers") as string);
        } catch (error) {
            return NextResponse.json({ error: "Invalid JSON in user fields" }, { status: 400 });
        }

        // Extract other form fields
        const companyName = formData.get("companyName")?.toString();
        const tpin = formData.get("tpin")?.toString();
        const phoneNumber = formData.get("phoneNumber")?.toString();
        const organizationEmail = formData.get("organizationEmail")?.toString();
        const address = JSON.parse(formData.get("address")?.toString() || "{}");
        const businessTypeId = parseInt(formData.get("businessTypeId")?.toString() || "0", 10);
        const files = formData.getAll("incorporationDocuments") as File[];

        if (!companyName || !tpin || !phoneNumber || !organizationEmail || !address || !businessTypeId || !files.length || !adminUsers || !registeredUsers) {
            return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
        }

        // Upload files to Minio
        const uploadedFiles = [];
        for (const file of files) {
            const fileName = `${S3_ONBOARDING_DOCUMENTS_PATH}/${Date.now()}-${file.name}`;
            const buffer = Buffer.from(await file.arrayBuffer());

            try {
                const minioResponse = await minioClient.putObject(S3_BUCKET!, fileName, buffer, buffer.length, {
                    'Content-Type': file.type || undefined,
                });
                console.log('Minio response:', minioResponse);
                uploadedFiles.push(fileName);
            } catch (uploadError) {
                console.error('Upload error:', uploadError);
                return NextResponse.json({ error: 'Upload failed' }, { status: 500 });
            }
        }

        // Prepare data for backend
        const backendData = {
            organization_name: companyName,
            tpin,
            phone_number: phoneNumber,
            organization_email: organizationEmail,
            address: {
                name: companyName,
                street: address.street,
                city: address.city,
                state: address.state,
                zip_code: address.zip_code,
                country: address.country,
            },
            organization_type_id: businessTypeId,
            documents: uploadedFiles,
            admin_users: adminUsers.map((user: any) => ({
                title: user.adminUserTitle,
                first_name: user.adminUserFirstName,
                last_name: user.adminUserLastName,
                email: user.adminUserEmail,
            })),
            staff_users: registeredUsers.map((user: any) => ({
                title: user.registeredUserTitle,
                first_name: user.registeredUserFirstName,
                last_name: user.registeredUserLastName,
                email: user.registeredUserEmail,
                role_id: user.registeredUserRoleId,
            })),
        };

        // Send request to backend
        const response = await axios.post(
            `${process.env.WHITEBOOK_API_URL_V2}/onboarding`,
            backendData,
            {
                headers: {
                    "X-WHITEBOOK-API-Key": process.env.WHITEBOOK_ADMIN_API_KEY,
                    "Content-Type": "application/json",
                },
            },
        );

        if (response.status !== 201) {
            return NextResponse.json({ error: "Failed to upload to backend" }, { status: 500 });
        }

        return NextResponse.json(response.data, { status: 201 });

    } catch (error) {
        console.error("API error:", error);
        if (axios.isAxiosError(error) && error.response && error.response.data.errors && error.response.data.errors.length) {
            return NextResponse.json({ error: error.response.data.errors[0].message }, { status: error.status });
        }
        return NextResponse.json({ error: "Internal server error" }, { status: 500 });
    }
}