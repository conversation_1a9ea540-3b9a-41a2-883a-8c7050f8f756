import { NextRequest, NextResponse } from "next/server";
import { v2EngineAxiosInstance, APIErrorResponse } from "@/utils/axios"; 
import { Currency } from "@/types/product/index";

// GET /api/currencies - Fetch all currencies
export async function GET(req: NextRequest) {
  try {
    // Fetch data from your V2 engine's /currencies endpoint
    const response = await v2EngineAxiosInstance.get<{ data: Currency[] }>("/currencies");

    // Check if the response format is as expected (e.g., has a data property)
    if (response.data && Array.isArray(response.data.data)) {
      return NextResponse.json(response.data.data); // Return the array of currencies
    } else {
      // Handle unexpected response structure from V2 engine
      console.error("Unexpected currency data structure from V2 engine:", response.data);
      return NextResponse.json(
        { error: "Failed to fetch currencies due to unexpected format." },
        { status: 500 }
      );
    }
  } catch (error: unknown) {
    const err = error as APIErrorResponse;
    // Log detailed error from V2 engine interaction
    console.error(
      "Error fetching currencies via V2 engine:",
      err.response?.status,
      err.response?.data || err.response?.message || err
    );

    // Prepare error response for the frontend
    const status = err.response?.status || 500;
    const message =
      (err.response?.data?.message as string) ||
      (err.response?.data?.error as string) ||
      (err.response?.message as string) ||
      "Error fetching currencies";

    return NextResponse.json(
      { error: message },
      { status }
    );
  }
}