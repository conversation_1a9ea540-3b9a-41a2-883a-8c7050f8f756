"use client";

import React, { useEffect, useState } from "react";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import VehicleMakes from "@/components/Vehicles/Makes/VehicleMakes";

const VehicleMakesPage = () => {
  const [makes, setMakes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<any>(null);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch("/api/vehicles/makes", { method: "GET" });
        const data = await response.json();
        setMakes(data.data);
      } catch (error) {
        setError(error);
        console.error("Error fetching variants:", error);
      } finally {
        setLoading(false); // Update loading state
      }
    };

    fetchData();
  }, []); // Empty dependency array means this effect runs once after the initial render
  return (
    <DefaultLayout>
      <Breadcrumb pageName="Vehicle Makes" />
      <VehicleMakes makes={makes} loading={loading} error={error} />
    </DefaultLayout>
  );
};

export default VehicleMakesPage;
