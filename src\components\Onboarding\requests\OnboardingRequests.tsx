import React, { useState } from "react";
import { createColumnHelper, SortingState } from "@tanstack/react-table";
import "@/css/table.css";
import DebouncedSearchInput from "@/components/Inputs/DebouncedSearchInput";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    faBan,
    faEdit,
    faEye,
    faMagnifyingGlass,
    faTrash,
} from "@fortawesome/free-solid-svg-icons";
import TanStackTable from "@/components/Tables/TanStackTable";
import Link from "next/link";
import OrganizationModal from "@/components/Modals/OrganizationModal";
import ConfirmModal from "@/components/Modals/ConfirmModal";
import { getPageSizeOptions } from "@/components/Tables/utils";

interface Organization {
    id: number;
    name: string;
    street_address: string;
    postal_address: string;
    email: string;
    phone_number: string;
    registration_number: string;
}

interface User {
    role_name: string;
    first_name: string;
    last_name: string;
}

interface OrganizationsProps {
    organizations: Organization[];
    loading: boolean;
    error: string | null;
    isModalOpen: boolean;
    setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
    handleSubmit: (organization: Partial<Organization>) => void;
    handleUpdate: (updatedOrganization: Partial<Organization>) => void;
    handleDelete: (id: number) => void;
    handleSuspend: (id: number) => void;
    user: User | null;
}

const Organizations: React.FC<OrganizationsProps> = ({
    organizations,
    loading,
    error,
    isModalOpen,
    setIsModalOpen,
    handleSubmit,
    handleUpdate,
    handleDelete,
    handleSuspend,
    user,
}) => {
    const [newOrganization, setNewOrganization] = useState<Partial<Organization>>(
        {
            name: "",
            street_address: "",
            postal_address: "",
            email: "",
            phone_number: "",
        },
    );
    const [selectedOrganization, setSelectedOrganization] =
        useState<Partial<Organization> | null>(null);
    const [isConfirmDeleteModalOpen, setIsConfirmDeleteModalOpen] =
        useState<boolean>(false);
    const [isConfirmSuspendModalOpen, setIsConfirmSuspendModalOpen] =
        useState<boolean>(false);
    const [organizationToDelete, setOrganizationToDelete] = useState<
        number | null
    >(null);
    const [organizationToSuspend, setOrganizationToSuspend] = useState<
        number | null
    >(null);
    const [pageSize, setPageSize] = useState<number>(10); // Default page size
    const openUpdateModal = (organization: Organization) => {
        setSelectedOrganization(organization);
        setNewOrganization(organization);
        setIsModalOpen(true);
    };
    const openSuspendModal = (id: number) => {
        setOrganizationToSuspend(id);
        setIsConfirmSuspendModalOpen(true);
    };

    const openDeleteModal = (id: number) => {
        setOrganizationToDelete(id);
        setIsConfirmDeleteModalOpen(true);
    };

    const confirmDelete = () => {
        if (organizationToDelete !== null) {
            handleDelete(organizationToDelete);
            setOrganizationToDelete(null);
            setIsConfirmDeleteModalOpen(false);
        }
    };

    const confirmSuspend = () => {
        if (organizationToDelete !== null) {
            handleSuspend(organizationToDelete);
            setOrganizationToSuspend(null);
            setIsConfirmSuspendModalOpen(false);
        }
    };

    const getOrganizationNameById = (id: number): string => {
        const organization = organizations?.find((org) => org.id === id);
        return organization ? organization.name : "Unknown";
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setNewOrganization((prev) => ({
            ...prev,
            [name]: value,
        }));
    };

    const handlePhoneChange = (value: string) => {
        setNewOrganization((prevData) => ({
            ...prevData,
            phone_number: value,
        }));
    };

    const onSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (selectedOrganization) {
            handleUpdate(newOrganization);
        } else {
            handleSubmit(newOrganization);
        }
        setNewOrganization({
            name: "",
            street_address: "",
            postal_address: "",
            email: "",
            phone_number: "",
        });
        setSelectedOrganization(null);
        setIsModalOpen(false);
    };
    const [globalFilter, setGlobalFilter] = React.useState("");
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const columnHelper = createColumnHelper<Organization>();

    {/* Table Columns */ }
    const columns = [
        columnHelper.accessor("name", {
            cell: (info) => (
                <div className="max-width-cell truncated-cell">{info?.getValue()}</div>
            ),
            header: "Name",
        }),
        columnHelper.accessor("email", {
            cell: (info) => (
                <div className="max-width-cell truncated-cell">{info?.getValue()}</div>
            ),
            header: "Email",
        }),
        columnHelper.accessor("street_address", {
            cell: (info) => <div className="max-width-cell">{info?.getValue()}</div>,
            header: "Address",
        }),
        columnHelper.accessor("phone_number", {
            cell: (info) => <div className="max-width-cell">{info?.getValue()}</div>,
            header: "Phone",
        }),
        columnHelper.display({
            id: "actions",
            cell: (info) => (
                <div className="flex space-x-4">
                    <Link
                        href={`organizations/${info.row.original.id}`}
                        className="text-blue-500 hover:text-opacity-90"
                        title="View"
                    >
                        <FontAwesomeIcon icon={faEye} />
                    </Link>
                    <button
                        onClick={() => openUpdateModal(info.row.original)}
                        className="text-wbv-theme hover:text-opacity-90"
                        title="Edit"
                    >
                        <FontAwesomeIcon icon={faEdit} />
                    </button>
                    {/* TODO:  user?.role_name === "super admin" && (*/}
                    {user?.first_name === "Kalenga" && (
                        <>
                            <button
                                onClick={() => openSuspendModal(info.row.original.id)}
                                className="text-yellow-500 hover:text-opacity-90"
                                title="Suspend"
                            >
                                <FontAwesomeIcon icon={faBan} />
                            </button>
                            <button
                                onClick={() => openDeleteModal(info.row.original.id)}
                                className="text-red-500 hover:text-red-700"
                                title="Delete"
                            >
                                <FontAwesomeIcon icon={faTrash} />
                            </button>
                        </>
                    )}
                </div>
            ),
            header: "Actions",
        }),
    ];

    if (loading) return <div>Loading...</div>;
    if (error) return <div className="text-red-500">Error: {error}</div>;

    return (
        <div className="flex flex-col pt-4">
            <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                    {/* {Search and Add} */}
                    <div className="mb-2 flex justify-between">
                        <div className="flex w-full items-center gap-1">
                            <FontAwesomeIcon icon={faMagnifyingGlass} />
                            <DebouncedSearchInput
                                value={globalFilter ?? ""}
                                onChange={(value) => setGlobalFilter(String(value))}
                                debounced={500}
                                placeholder="Search organizations..."
                            />
                        </div>
                        <div className="flex flex-col items-start gap-2">
                            <div className="flex items-center gap-2">
                                <button
                                    className="ml-2 inline-flex items-center whitespace-nowrap rounded bg-wbv-theme px-4 py-2 text-xs font-semibold text-white hover:bg-opacity-90"
                                    onClick={() => {
                                        setNewOrganization({
                                            name: "",
                                            street_address: "",
                                            postal_address: "",
                                            email: "",
                                            phone_number: "",
                                        });
                                        setSelectedOrganization(null);
                                        setIsModalOpen(true);
                                    }}
                                >
                                    <span>Add Organization</span>
                                </button>
                            </div>
                        </div>
                        {/* Modal */}
                        {isModalOpen && (
                            <OrganizationModal
                                selectedOrganization={selectedOrganization}
                                onSubmit={onSubmit}
                                newOrganization={newOrganization}
                                handleChange={handleChange}
                                handlePhoneChange={handlePhoneChange}
                                setIsModalOpen={setIsModalOpen}
                            />
                        )}
                    </div>
                    {/* Confirm Delete Modal */}
                    <ConfirmModal
                        isOpen={isConfirmDeleteModalOpen}
                        onClose={() => setIsConfirmDeleteModalOpen(false)}
                        onConfirm={confirmDelete}
                        title="Confirm Deletion"
                        message={`Are you sure you want to delete the organization <strong>${getOrganizationNameById(organizationToDelete!)}?</strong>`}
                        cta="Delete"
                        ctaBtnColor="danger"
                    />
                    {/* Confirm Suspend Modal */}
                    <ConfirmModal
                        isOpen={isConfirmSuspendModalOpen}
                        onClose={() => setIsConfirmSuspendModalOpen(false)}
                        onConfirm={confirmSuspend}
                        title="Confirm Suspension"
                        message={`Are you sure you want to suspend the organization <strong>${getOrganizationNameById(organizationToSuspend!)}?</strong>`}
                        cta="Suspend"
                        ctaBtnColor="warning"
                    />

                    {/* Organizations Table */}
                    {loading ? (
                        <p>Loading...</p>
                    ) : error ? (
                        <p className="text-red-500">{error}</p>
                    ) : (
                        <div className="my-5 overflow-hidden border-b border-gray-200 shadow">
                            <TanStackTable
                                data={organizations}
                                columns={columns}
                                globalFilter={globalFilter}
                                sorting={sorting}
                                setGlobalFilter={setGlobalFilter}
                                setSorting={setSorting}
                                pageSizeOptions={getPageSizeOptions(organizations)}
                            />
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Organizations;

