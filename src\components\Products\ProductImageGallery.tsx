import React from 'react';

interface ProductImageGalleryProps {
  images: string[];
  productName: string;
  onImageClick?: (imagePath: string, index: number) => void;
}

// Helper function to properly construct image URLs
const getImageUrl = (path: string): string => {
  if (!path) return '/images/placeholder.png';
  
  // Remove any leading/trailing slashes and clean the path
  const cleanPath = path.trim().replace(/^\/+|\/+$/g, '');
  
  // If the path is empty after cleaning, return placeholder
  if (!cleanPath) return '/images/placeholder.png';
  
  return `/api/products/view/${cleanPath}`;
};

const ProductImageGallery: React.FC<ProductImageGalleryProps> = ({
  images,
  productName,
  onImageClick
}) => {
  if (!images || images.length === 0) {
    return (
      <div className="mt-6 border-t pt-4">
        <h2 className="mb-4 text-xl font-semibold">Product Images</h2>
        <p className="text-sm text-gray-500">No images available for this product.</p>
      </div>
    );
  }

  return (
    <div className="mt-6 border-t pt-4">
      <h2 className="mb-4 text-xl font-semibold">Product Images</h2>
      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
        {images
          .filter((imgPath): imgPath is string => typeof imgPath === 'string' && imgPath.trim() !== '')
          .map((imgPath, index) => (
            <div 
              key={index} 
              className="aspect-square overflow-hidden rounded border border-stroke dark:border-strokedark cursor-pointer"
              onClick={() => onImageClick && onImageClick(imgPath, index)}
            >
              <img
                src={getImageUrl(imgPath)}
                alt={`${productName} - Image ${index + 1}`}
                className="h-full w-full object-cover transition-transform duration-300 hover:scale-110"
                loading="lazy"
                onError={(e) => {
                  console.error(`Failed to load image: ${imgPath}, URL: ${getImageUrl(imgPath)}`);
                  (e.target as HTMLImageElement).src = '/images/placeholder.png';
                  (e.target as HTMLImageElement).alt = 'Image failed to load';
                }}
              />
            </div>
          ))}
      </div>
    </div>
  );
};

export default ProductImageGallery;