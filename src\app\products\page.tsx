"use client";

import DefaultLayout from "@/components/Layouts/DefaultLayout";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import React, { useEffect, useState } from "react";
import { Product, ProductApiResponse, ProductCustomDetail } from "@/types/product/index";
import Products from "@/components/Products/Products";
import CreateShopModal from "@/components/Modals/createShopModal";
import { useSession } from "next-auth/react";
import { toast } from "react-toastify";
import { mapCurrencyIdToStringSync } from "@/utils/currencyUtils";

// User interface - expecting organization_id directly or nested
interface User {
  first_name: string;
  last_name: string;
  role_name: string;
  organization_id?: number;
}

// Interface for the fetched shop data
interface Shop {
  id: number;
  name: string;
  organization_id: number | null;
  user_id?: number  | null;
}

// Interface for product data from API
interface ApiProductData {
  id: number;
  name: string;
  type: string;
  currency_id: number;
  price: number;
  variant?: number;
  description: string;
  product_details?: string | number | boolean | null;
  shop_id: number;
}

interface ApiResponse {
  data: ApiProductData[];
}
interface UploadedImageData {
  id?: number;
  name: string;
  type: string;
  is_default: boolean;
  file: string;
  path: string;
}

// Helper function to transform product_details object (from API) to array (for component/modal)
const transformProductDetails = (details: unknown): ProductCustomDetail[] => {
  if (!details || typeof details !== "object") return [];

  return Object.entries(details as Record<string, unknown>).map(([key, value]) => {
    // Check if the value is an object with type information
    if (value && typeof value === "object" && "type" in value && "value" in value) {
      // This is a structured field with type information
      return {
        customFieldName: key,
        fieldType: String((value as { type: unknown }).type), // Ensure it's a string
        value: String((value as { value: unknown }).value),
      };
    }

    // Handle legacy format or simple values
    // Try to infer the type, with special handling for decimal values
    let fieldType = "text";

    if (typeof value === "boolean") {
      fieldType = "boolean";
    } else if (typeof value === "number") {
      // Check if it's likely a decimal by looking for decimal places
      const stringValue = String(value);
      fieldType = stringValue.includes(".") && !stringValue.endsWith(".0") ? "decimal" : "number";
    }

    return {
      customFieldName: key,
      fieldType: fieldType,
      value: String(value),
    };
  });
};

// Helper function to transform productDetails array back to object for API
const transformDetailsForApi = (
  details: ProductCustomDetail[] | undefined
): { [key: string]: unknown } => {
  if (!details) return {};
  console.log("Transforming details:", details); // Log input details
  const result = details.reduce((acc, detail) => {
    if (detail.customFieldName) {
      try {
        // Create a structured object that preserves both value and type
        let processedValue: string | number | boolean;

        switch (detail.fieldType) {
          case "number":
            // Parse as integer
            processedValue = parseInt(detail.value, 10) || 0;
            console.log(` -> Parsed as number: ${processedValue}`);
            break;

          case "decimal":
            // Parse as floating point with decimal precision
            processedValue = parseFloat(detail.value) || 0;
            console.log(` -> Parsed as decimal: ${processedValue}`);
            break;

          case "boolean":
            // Convert string to boolean
            processedValue = detail.value.toLowerCase() === "true" || detail.value === "1";
            console.log(` -> Parsed as boolean: ${processedValue}`);
            break;

          case "text":
          default:
            // Keep as string
            processedValue = detail.value;
            console.log(` -> Kept as string: "${processedValue}"`);
            break;
        }

        // Store both the value and its type
        acc[detail.customFieldName] = {
          type: detail.fieldType,
          value: processedValue,
        };
      } catch (error) {
        console.error(
          `Error processing field "${detail.customFieldName}" with type "${detail.fieldType}":`,
          error
        );
        // Fallback to original value if parsing fails
        acc[detail.customFieldName] = {
          type: "text",
          value: detail.value,
        };
      }
    }
    return acc;
  }, {} as { [key: string]: unknown });
  console.log("Transformed details result:", result); // Log final object
  return result;
};

const ProductsPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [user, setUser] = useState<User | null>(null);
  const [userOrgShopId, setUserOrgShopId] = useState<number | null>(
    () => Number(sessionStorage.getItem("userOrgShopId")) || null // Initialize from sessionStorage
  );
  const [showCreateShopModal, setShowCreateShopModal] = useState(false); // State to control modal visibility
  const { data: session } = useSession();

  // Fetch user info AND shops in parallel, then determine shop ID
  useEffect(() => {
    const fetchInitialData = async (): Promise<void> => {
      if (!session?.user?.id) {
        console.log("No session user ID found for initial data fetch.");
        setLoading(false);
        return;
      }

      setError(null); // Reset errors

      try {
        console.log(`Fetching user data for user ID: ${session.user.id}`);
        // Fetch user information
        const userResponse = await fetch(`/api/users/${session.user.id}`);
        if (!userResponse.ok) {
          const errorText = await userResponse.text();
          console.error(
            `Failed to fetch user info: ${userResponse.status}`,
            errorText
          );
          throw new Error(`Failed to fetch user info: ${userResponse.status}`);
        }
        const userData = (await userResponse.json()) as User;
        console.log("Fetched user data:", userData);
        const organizationId = userData.organization_id || undefined;
        setUser({
          first_name: userData.first_name,
          last_name: userData.last_name,
          role_name: userData.role_name,
          organization_id: organizationId,
        });

        console.log("Checking shop association...");
        // Fetch shops data
        const shopsResponse = await fetch(`/api/shops`);
        if (!shopsResponse.ok) {
          const errorText = await shopsResponse.text();
          console.error(
            `Failed to fetch shops: ${shopsResponse.status}`,
            errorText
          );
          throw new Error(`Failed to fetch shops: ${shopsResponse.status}`);
        }
        const shopsData = (await shopsResponse.json()) as Shop[];
        console.log("Fetched shops data:", shopsData);

        // Check shop association by organization ID or user ID
        const orgShop = shopsData.find(
          (shop) => shop.organization_id === organizationId
        );
        const userShop = shopsData.find(
          (shop) => shop.user_id === session.user.id
        );

        if (orgShop || userShop) {
          const shopId = orgShop?.id || userShop?.id || null;
          console.log(`Found associated shop ID: ${shopId}`);
          setUserOrgShopId(shopId); // Set the specific shop ID state
          sessionStorage.setItem("userOrgShopId", String(shopId)); // Cache shop ID in session storage
        } else {
          console.warn(
            `No shop found for organization ID: ${organizationId} or user ID: ${session.user.id}`
          );
          setUserOrgShopId(null); // Reset shop ID
          sessionStorage.removeItem("userOrgShopId"); // Clear cached shop ID
        }
      } catch (err) {
        console.error("Error fetching initial user/shop data:", err);
        setError(
          err instanceof Error ? err.message : "Failed to load essential data"
        );
        setUserOrgShopId(null); // Ensure shop ID is null on error
      } finally {
        setLoading(false);
      }
    };

    fetchInitialData();
  }, [session]);

  const handleShopCreated = (newShopId: number) => {
    try {
      if (!newShopId) {
        throw new Error("Invalid shop ID received after shop creation.");
      }
      setUserOrgShopId(newShopId); // Set the new shop ID after creation
      sessionStorage.setItem("userOrgShopId", String(newShopId)); // Cache new shop ID in session storage
      setShowCreateShopModal(false); // Close the modal
      toast.success("Shop created successfully!", { position: "bottom-right" });
    } catch (error) {
      console.error("Error handling shop creation:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "An unexpected error occurred while creating the shop.",
        { position: "bottom-right" }
      );
    }
  };

  // Fetch products associated with the user's shop ID
  useEffect(() => {
    fetchProducts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userOrgShopId]);

  const fetchProducts = async (): Promise<void> => {
    if (!userOrgShopId) return;

    setLoading(true);
    try {
      const response = await fetch(`/api/products?shop_id=${userOrgShopId}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = (await response.json()) as ApiResponse;
      let transformedData: Product[] = [];
      if (result && Array.isArray(result.data)) {
        transformedData = result.data
          .filter((item: ApiProductData) => item.shop_id === userOrgShopId) // Filter products by shop ID
          .map(
            (item: ApiProductData): Product => ({
              id: item.id,
              name: item.name,
              productType: item.type,
              currency_id: item.currency_id,
              currency_code: mapCurrencyIdToStringSync(
                item.currency_id || 0
              ),
              price: item.price,
              variant: item.variant,
              description: item.description,
              productDetails: transformProductDetails(item.product_details),
              images: [],
              shop_id: item.shop_id,
            })
          );
      } else {
        console.warn(
          "API response for products did not contain an array in result.data:",
          result
        );
      }
      setProducts(transformedData);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch products";
      setError(errorMessage);
      console.error("Error fetching products:", err);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitProduct = async (
    productSubmitData: Partial<Product> & { imagesData?: UploadedImageData[] }
  ) => {
    if (!userOrgShopId) {
      setError(
        "Cannot add product: Shop ID for user's organization is missing or not loaded."
      );
      console.error(
        "handleSubmitProduct failed: userOrgShopId is null or undefined."
      );
      throw new Error(
        "Cannot add product: Shop ID for user's organization is missing or not loaded."
      );
    }

    try {
      const priceValue =
        parseFloat(String(productSubmitData.price).replace(/[^0-9.]/g, "")) || 0;

      // Debug log the incoming data
      console.log("Received product submit data:", productSubmitData);

      // For the initial product creation, don't include images
      const apiPayload: Record<string, unknown> = {
        name: productSubmitData.name,
        description: productSubmitData.description,
        price: Math.abs(priceValue),
        currency_id: productSubmitData.currency_id,
        shop_id: userOrgShopId,
        type: productSubmitData.productType,
        variant_id: productSubmitData.variant
          ? Number(productSubmitData.variant)
          : null, // Set to null if variant doesn't exist
        variant: undefined, // Remove the numeric variant field to avoid type mismatch
        product_details: transformDetailsForApi(
          productSubmitData.productDetails
        ),
        // Don't include images in the initial product creation
      };

      // Remove undefined values but keep null values for variant_id
      Object.keys(apiPayload).forEach((key) => {
        if (key !== "variant_id" && apiPayload[key] === undefined) {
          delete apiPayload[key];
        }
      });

      // Debug log the final payload
      console.log("Submitting product payload:", apiPayload);

      const response = await fetch("/api/products", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(apiPayload),
      });

      if (!response.ok) {
        const errorData = await response.json() as { message?: string };
        throw new Error(errorData.message || "Failed to create product");
      }

      const result = await response.json() as ProductApiResponse;

      // Show success toast notification
      toast.success("Product created successfully!", {
        position: "bottom-right",
        autoClose: 3000,
      });

      // Extract the created product data from the response
      const createdProduct: Product = {
        id: result.id || result.data?.id || 0, // Use result.id or fallback to data.id
        name: productSubmitData.name || "",
        productType: productSubmitData.productType || "",
        currency_id: productSubmitData.currency_id || 0,
        currency_code: mapCurrencyIdToStringSync(
          productSubmitData.currency_id || 0
        ),
        price: productSubmitData.price || 0,
        variant: productSubmitData.variant,
        description: productSubmitData.description || "",
        productDetails: (productSubmitData.productDetails ?? []) as ProductCustomDetail[],
        images: [],
        shop_id: userOrgShopId,
      };

      // Refresh the products list
      await fetchProducts();

      // Return the created product
      return createdProduct;
    } catch (error) {
      console.error("Error creating product:", error);
      setError(
        error instanceof Error ? error.message : "Failed to create product"
      );
      toast.error(
        error instanceof Error ? error.message : "Failed to create product",
        {
          position: "bottom-right",
        }
      );
      throw error;
    }
  };

  const handleUpdateProduct = async (updatedProductData: Partial<Product>) => {
    if (!userOrgShopId || !updatedProductData.id) {
      setError("Cannot update product: Missing required data");
      return;
    }

    setLoading(true);
    setError(null);
    // Create a single toast ID for this update operation
    const toastId = toast.info("Updating product...", { autoClose: false });
    try {
      const priceValue =
        parseFloat(String(updatedProductData.price).replace(/[^0-9.]/g, "")) ||
        0;

      const apiPayload: { [key: string]: unknown } = {
        name: updatedProductData.name,
        description: updatedProductData.description,
        price: Math.abs(priceValue),
        currency_id: updatedProductData.currency_id,
        shop_id: userOrgShopId,
        type: updatedProductData.productType,
        variant_id: updatedProductData.variant
          ? Number(updatedProductData.variant)
          : null, // Set to null if variant doesn't exist
        variant: undefined, // Remove the numeric variant field to avoid type mismatch
        product_details: transformDetailsForApi(
          updatedProductData.productDetails
        ),
      };

      // Remove undefined values but keep null values for variant_id
      Object.keys(apiPayload).forEach((key) => {
        if (key !== "variant_id" && apiPayload[key] === undefined) {
          delete apiPayload[key];
        }
      });

      console.log("Submitting update payload:", apiPayload);

      const response = await fetch(`/api/products/${updatedProductData.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(apiPayload),
      });

      if (!response.ok) {
        const errorData = (await response.json()) as { message?: string };
        throw new Error(errorData.message || "Failed to update product");
      }

      await fetchProducts();

      // Update the toast with success message
      toast.update(toastId, {
        type: "success",
        render: "Product updated successfully",
        autoClose: 3000,
      });
    } catch (error) {
      console.error("Error updating product:", error);
      setError(
        error instanceof Error ? error.message : "Failed to update product"
      );
      // Update the toast with error message
      toast.update(toastId, {
        type: "error",
        render:
          error instanceof Error ? error.message : "Failed to update product",
        autoClose: 3000,
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteProduct = async (id: number) => {
    setLoading(true);
    setError(null);
    try {
      // *** ADJUST API ENDPOINT AS NEEDED ***
      const response = await fetch(`/api/products/${id}`, {
        // Assuming API uses ID in URL for DELETE
        method: "DELETE",
      });
      if (!response.ok) {
        const errorData = await response
          .json()
          .catch(() => ({ message: "Failed to delete product" }))as { message?: string };
        throw new Error(errorData.message || "Failed to delete product");
      }
      setProducts((prev) => prev.filter((p) => p.id !== id));

      // Show success toast notification
      toast.success("Product deleted successfully!", {
        position: "bottom-right",
        autoClose: 3000,
      });
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to delete product";
      setError(errorMessage);
      toast.error(errorMessage, {
        position: "bottom-right",
      });
      console.error("Delete Product Error:", err);
    } finally {
      setLoading(false);
    }
  };

  if (!userOrgShopId) {
    return (
      <DefaultLayout>
        <Breadcrumb pageName="Products" />
        <div className="flex h-screen flex-col items-center justify-center">
          <p className="mb-4 text-lg font-semibold text-gray-700">
            You need to create a shop to be able to create products.
          </p>
          <button
            onClick={() => setShowCreateShopModal(true)}
            className="rounded bg-wbv-theme px-4 py-2 text-white hover:bg-opacity-90"
          >
            Create Shop
          </button>
        </div>
        {showCreateShopModal && (
          <CreateShopModal
            isOpen={showCreateShopModal}
            onClose={() => setShowCreateShopModal(false)}
            userId={Number(session?.user?.id)}
            organizationId={user?.organization_id || null}
            onShopCreated={handleShopCreated}
          />
        )}
      </DefaultLayout>
    );
  }

  return (
    <DefaultLayout>
      <Breadcrumb pageName="Products" />
      <Products
        products={products}
        loading={loading}
        error={error}
        handleSubmitProduct={handleSubmitProduct}
        handleUpdateProduct={handleUpdateProduct}
        handleDeleteProduct={handleDeleteProduct}
        user={user}
      />
    </DefaultLayout>
  );
};

export default ProductsPage;