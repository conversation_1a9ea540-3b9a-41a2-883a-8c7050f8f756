import React from "react";
import PhoneInput from "react-phone-input-2";

interface Organization {
  id: number;
  name: string;
  street_address: string;
  postal_address: string;
  email: string;
  phone_number: string;
  registration_number: string;
}

interface OrganizationModalProps {
  selectedOrganization?: Partial<Organization> | null;
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  newOrganization?: Partial<Organization> | null;
  handlePhoneChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const OrganizationModal: React.FC<OrganizationModalProps> = ({
  selectedOrganization,
  onSubmit,
  newOrganization,
  handleChange,
  handlePhoneChange,
  setIsModalOpen,
}) => (
  <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50">
    <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-lg">
      <h2 className="mb-4 text-xl font-bold">
        {selectedOrganization ? "Edit" : "Add"} Organization
      </h2>
      <form onSubmit={onSubmit}>
        <div className="grid grid-cols-1 gap-4">
          <input
            type="text"
            name="name"
            value={newOrganization?.name}
            onChange={handleChange}
            placeholder="Organisation Name"
            className="rounded border border-gray-300 p-2"
            required
          />
          <input
            type="text"
            name="street_address"
            value={newOrganization?.street_address}
            onChange={handleChange}
            placeholder="Street Address"
            className="rounded border border-gray-300 p-2"
            required
          />
          <input
            type="text"
            name="postal_address"
            value={newOrganization?.postal_address}
            onChange={handleChange}
            placeholder="Postal Address"
            className="rounded border border-gray-300 p-2"
            required
          />
          <input
            type="email"
            name="email"
            value={newOrganization?.email}
            onChange={handleChange}
            placeholder="Email"
            className="rounded border border-gray-300 p-2"
            required
          />
          <div>
            <PhoneInput
              country={"zm"}
              value={newOrganization?.phone_number}
              onChange={handlePhoneChange}
              inputProps={{
                name: "phone_number",
                required: true,
                autoFocus: false,
              }}
              inputStyle={{
                width: "100%",
                height: "2.75rem",
                borderRadius: "0.375rem",
              }}
            />
          </div>
        </div>
        <div className="mt-4 flex justify-end">
          <button
            type="button"
            onClick={() => setIsModalOpen(false)}
            className="mr-2 rounded bg-gray-500 p-2 text-white hover:bg-gray-600"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="rounded bg-wbv-theme p-2 text-white hover:bg-opacity-90"
          >
            {selectedOrganization ? "Submit" : "Add Organisation"}
          </button>
        </div>
      </form>
    </div>
  </div>
);

export default OrganizationModal;
