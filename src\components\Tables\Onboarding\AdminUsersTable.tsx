import React, { useState } from 'react';
import { adminUserSchema } from "@/components/Forms/Onboarding/schemas/schemas";
import { PencilIcon, TrashIcon } from '@heroicons/react/solid';
import { Listbox, ListboxButton, ListboxOption, ListboxOptions, Transition } from '@headlessui/react';
import { SelectorIcon, CheckIcon } from '@heroicons/react/solid';

interface AdminUser {
    adminUserTitle: string;
    adminUserFirstName: string;
    adminUserLastName: string;
    adminUserEmail: string;
}

interface AdminUsersTableProps {
    users: AdminUser[];
    handleEditUser: (index: number, updatedUser: AdminUser) => void;
    handleRemoveUser: (index: number) => void;
}

const titles = ["Mr", "Ms", "Mrs", "Dr", "Prof"];

const AdminUsersTable: React.FC<AdminUsersTableProps> = ({ users, handleEditUser, handleRemoveUser }) => {
    const [editingIndex, setEditingIndex] = useState<number | null>(null);
    const [editedUser, setEditedUser] = useState<AdminUser | null>(null);
    const [errors, setErrors] = useState<{ [key: string]: string }>({});

    const handleEditClick = (index: number, user: AdminUser) => {
        setEditingIndex(index);
        setEditedUser(user);
    };

    const handleSaveClick = (index: number) => {
        if (editedUser) {
            const validationResult = adminUserSchema.safeParse(editedUser);
            if (!validationResult.success) {
                const newErrors: { [key: string]: string } = {};
                validationResult.error.errors.forEach((error) => {
                    newErrors[error.path[0]] = error.message;
                });
                setErrors(newErrors);
                return;
            } else {
                setErrors({});
                handleEditUser(index, editedUser);
                setEditingIndex(null);
                setEditedUser(null);
            }
        }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>, field: string) => {
        if (editedUser) {
            setEditedUser({ ...editedUser, [field]: e.target.value });
        }
    };

    const handleTitleChange = (value: string) => {
        if (editedUser) {
            setEditedUser({ ...editedUser, adminUserTitle: value });
        }
    };

    return (
        <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
                <thead>
                    <tr>
                        <th className="py-2 px-4 border-b text-left">Title</th>
                        <th className="py-2 px-4 border-b text-left">First Name</th>
                        <th className="py-2 px-4 border-b text-left">Last Name</th>
                        <th className="py-2 px-4 border-b text-left">Email</th>
                        <th className="py-2 px-4 border-b text-left">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {users.map((user, index) => (
                        <tr key={index}>
                            {editingIndex === index ? (
                                <>
                                    <td className="py-2 px-4 border-b">
                                        <select
                                            value={editedUser?.adminUserTitle || ""}
                                            onChange={(e) => handleTitleChange(e.target.value)}
                                            className="w-full cursor-default rounded border border-gray-300 bg-white py-1 px-2 text-left focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-8"
                                        >
                                            {titles.map((title) => (
                                                <option
                                                    key={title}
                                                    value={title}
                                                    className="hover:bg-gray-200"
                                                >
                                                    {title}
                                                </option>
                                            ))}
                                        </select>
                                        {errors.adminUserTitle && <p className="text-red-500 text-sm">{errors.adminUserTitle}</p>}
                                    </td>
                                    <td className="py-2 px-4 border-b">
                                        <input
                                            type="text"
                                            value={editedUser?.adminUserFirstName}
                                            onChange={(e) => handleInputChange(e, 'adminUserFirstName')}
                                            className="w-full border rounded px-2 py-1"
                                        />
                                        {errors.adminUserFirstName && <p className="text-red-500 text-sm">{errors.adminUserFirstName}</p>}
                                    </td>
                                    <td className="py-2 px-4 border-b">
                                        <input
                                            type="text"
                                            value={editedUser?.adminUserLastName}
                                            onChange={(e) => handleInputChange(e, 'adminUserLastName')}
                                            className="w-full border rounded px-2 py-1"
                                        />
                                        {errors.adminUserLastName && <p className="text-red-500 text-sm">{errors.adminUserLastName}</p>}
                                    </td>
                                    <td className="py-2 px-4 border-b">
                                        <input
                                            type="email"
                                            value={editedUser?.adminUserEmail}
                                            onChange={(e) => handleInputChange(e, 'adminUserEmail')}
                                            className="w-full border rounded px-2 py-1"
                                        />
                                        {errors.adminUserEmail && <p className="text-red-500 text-sm">{errors.adminUserEmail}</p>}
                                    </td>
                                    <td className="py-2 px-4 border-b">
                                        <button onClick={() => handleSaveClick(index)} className="text-green-500 hover:text-green-700">
                                            Save
                                        </button>
                                    </td>
                                </>
                            ) : (
                                <>
                                    <td className="py-2 px-4 border-b">{user.adminUserTitle}</td>
                                    <td className="py-2 px-4 border-b">{user.adminUserFirstName}</td>
                                    <td className="py-2 px-4 border-b">{user.adminUserLastName}</td>
                                    <td className="py-2 px-4 border-b">{user.adminUserEmail}</td>
                                    <td className="py-2 px-4 border-b">
                                        <button onClick={() => handleEditClick(index, user)} className="text-blue-500 hover:text-blue-700 mr-2">
                                            <PencilIcon className="h-5 w-5 text-blue-500" aria-hidden="true" />
                                        </button>
                                        <button onClick={() => handleRemoveUser(index)} className="text-red-500 hover:text-red-700">
                                            <TrashIcon className="h-5 w-5 text-red-500" aria-hidden="true" />
                                        </button>
                                    </td>
                                </>
                            )}
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default AdminUsersTable;