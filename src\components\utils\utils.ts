export const formatDateTime = (dateStr: string) => {
  const date = new Date(dateStr);
  const formattedTs = date.toLocaleString("en-ZM", {
    day: "2-digit",
    month: "short",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  });

  // e.g. 09 Dec 2024, 09:56:19 am
  return formattedTs;
};

export const formatCurrency = (amount: number, currency: string) => {
  return new Intl.NumberFormat("en-ZM", {
    style: "currency",
    currency,
  }).format(amount);
};

export const capitalizeFirstLetter = (string: string) => {
  return String(string).charAt(0).toUpperCase() + String(string).slice(1);
};

export const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  //e.g. 2024-12-09
  return date.toISOString().split("T")[0];
};
