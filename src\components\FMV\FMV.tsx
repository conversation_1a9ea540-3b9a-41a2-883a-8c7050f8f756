import { useState, useEffect } from "react";
import axios from "axios";

const VehiclesSearchForm = () => {
  const [manufacturer, setManufacturer] = useState("");
  const [model, setModel] = useState("");
  const [subModel, setSubModel] = useState("");
  const [year, setYear] = useState("");
  const [engine, setEngine] = useState("");
  const [transmission, setTransmission] = useState("");
  const [makes, setMakes] = useState<{ id: string; name: string }[]>([]);
  const [models, setModels] = useState<{ id: string; name: string }[]>([]);
  const [subModels, setSubModels] = useState<{ id: string; name: string }[]>(
    [],
  );
  const [years, setYears] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch("/api/vehicles/makes", { method: "GET" });
        const data = await response.json();
        setMakes(data.data);
      } catch (err) {
        console.error("Error fetching makes:", err);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    if (manufacturer) {
      // Fetch models based on manufacturer
      axios
        .get(`/api/models/${manufacturer}`)
        .then((response) => setModels(response.data));
    }
  }, [manufacturer]);

  useEffect(() => {
    if (model) {
      // Fetch sub-models based on model
      axios
        .get(`/api/submodels/${model}`)
        .then((response) => setSubModels(response.data));
    }
  }, [model]);

  useEffect(() => {
    if (subModel) {
      // Fetch years based on sub-model
      axios
        .get(`/api/years/${subModel}`)
        .then((response) => setYears(response.data));
    }
  }, [subModel]);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // Handle form submission
    console.log({ manufacturer, model, subModel, year, engine, transmission });
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col items-center">
      <div className="mb-4">
        <label>Manufacturer</label>
        <select
          value={manufacturer}
          onChange={(e) => setManufacturer(e.target.value)}
        >
          <option value="">Select Manufacturer</option>
          {makes.map((make) => (
            <option key={make.id} value={make.id}>
              {make.name}
            </option>
          ))}
        </select>
      </div>
      <div className="mb-4">
        <label>Model</label>
        <select
          value={model}
          onChange={(e) => setModel(e.target.value)}
          disabled={!manufacturer}
        >
          <option value="">Select Model</option>
          {models.map((model) => (
            <option key={model.id} value={model.id}>
              {model.name}
            </option>
          ))}
        </select>
      </div>
      <div className="mb-4">
        <label>Sub Model</label>
        <select
          value={subModel}
          onChange={(e) => setSubModel(e.target.value)}
          disabled={!model}
        >
          <option value="">Select Sub Model</option>
          {subModels.map((subModel) => (
            <option key={subModel.id} value={subModel.id}>
              {subModel.name}
            </option>
          ))}
        </select>
      </div>
      <div className="mb-4">
        <label>Year</label>
        <select
          value={year}
          onChange={(e) => setYear(e.target.value)}
          disabled={!subModel}
        >
          <option value="">Select Year</option>
          {years.map((year) => (
            <option key={year} value={year}>
              {year}
            </option>
          ))}
        </select>
      </div>
      <div className="mb-4">
        <label>Engine Size</label>
        <input
          type="text"
          value={engine}
          onChange={(e) => setEngine(e.target.value)}
        />
      </div>
      <div className="mb-4">
        <label>Transmission</label>
        <input
          type="text"
          value={transmission}
          onChange={(e) => setTransmission(e.target.value)}
        />
      </div>
      <button type="submit" className="bg-wbv-theme px-4 py-2 text-white">
        Get Valuation
      </button>
    </form>
  );
};

export default VehiclesSearchForm;
