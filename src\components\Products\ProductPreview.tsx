import React, { useState, useEffect } from "react";
import Link from "next/link";
import { formatDateTime } from "../utils/utils"; // Assuming utils path
import { transformProductDetails } from '@/utils/productUtils';
import { mapCurrencyIdToStringSync, fetchCurrencies } from '@/utils/currencyUtils';
import ProductModal from "../Modals/ProductModal";
import ConfirmModal from "../Modals/ConfirmModal";
import ImagePreviewModal from "../Modals/ImagePreviewModal";
import { Product } from "@/types/product/index"; // Import from types folder
import ProductImageGallery from './ProductImageGallery';
import ProductDetailsCard from './ProductDetailsCard';
import InlineLoader from '../common/Loader/InlineLoader';

// Interface for product data from API response
interface ApiProductResponse {
    id: number;
    name?: string;
    product_name?: string;
    type?: string;
    product_type?: string;
    currency_id: number;
    price: number;
    variant?: number;
    description?: string;
    product_details?: string | number | boolean | null | object;
    images?: Array<{
        id?: number;
        path?: string;
        url?: string;
        src?: string;
        name?: string;
        type?: string;
        is_default?: boolean;
        file?: string;
    } | string>;
    shop_id?: number;
    created_at?: string;
    updated_at?: string;
}

// Helper functions for image URLs are now handled by the ProductImageGallery component

interface ProductPreviewProps {
    productId: number; // Receive ID instead of full product object
    handleUpdateProduct: (updatedProduct: Partial<Product>) => Promise<void>; // Make async if page needs to wait
    handleDeleteProduct: (id: number) => Promise<void>; // Make async if page needs to wait
}

const ProductPreview: React.FC<ProductPreviewProps> = ({
    productId,
    handleUpdateProduct,
    handleDeleteProduct,
}) => {
    // Internal state for product data, loading, and errors
    const [productData, setProductData] = useState<Product | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const [isProductModalOpen, setIsProductModalOpen] = useState<boolean>(false);
    const [isConfirmDeleteModalOpen, setIsConfirmDeleteModalOpen] = useState<boolean>(false);
    const [isImagePreviewModalOpen, setIsImagePreviewModalOpen] = useState<boolean>(false);
    const [currentImageIndex, setCurrentImageIndex] = useState<number>(0);
    const [currentImagePath, setCurrentImagePath] = useState<string>("");

    // Fetch currencies and product data
    useEffect(() => {
        const fetchProductInfo = async () => {
            if (!productId || isNaN(productId)) {
                setError("Invalid Product ID provided to preview component.");
                setLoading(false);
                return;
            }

            setLoading(true);
            setError(null);
            setProductData(null); // Reset previous data
            try {
                // Fetch currencies first to ensure they're cached
                await fetchCurrencies();

                const response = await fetch(`/api/products/${productId}`);
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ message: `HTTP error! status: ${response.status}` }));
                    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
                }

                const result = await response.json() as ApiProductResponse ;

                if (!result || typeof result !== 'object') {
                    throw new Error("Invalid product data received.");
                }

                // Transform data - Check for alternative field names
                const transformedProduct: Product = {
                    id: result.id,
                    name: result.name || result.product_name || "Unnamed Product",
                    productType: result.type || result.product_type || "unknown",
                    currency_id: result.currency_id,
                    currency_code: mapCurrencyIdToStringSync(result.currency_id || 0),
                    price: result.price || 0,
                    variant: result.variant || undefined,
                    description: result.description || "",
                    productDetails: transformProductDetails(result.product_details || null),
                    // Enhanced image transformation handling different potential structures
                    images: (() => {
                        // Check if images exists and is an array
                        if (!result.images || !Array.isArray(result.images)) {
                            return [];
                        }

                        // Map and filter the images properly
                        return result.images
                            .map((img) => {
                                // Handle different image object structures
                                if (typeof img === 'string') return img;
                                if (img && typeof img === 'object') {
                                    if ('path' in img && img.path) return img.path;
                                    if ('url' in img && img.url) return img.url;
                                    if ('src' in img && img.src) return img.src;
                                }

                                return null;
                            })
                            .filter((img): img is string => img !== null); // Type-safe filter to remove null entries
                    })(),
                    shop_id: result.shop_id || undefined,
                    created_at: result.created_at,
                    updated_at: result.updated_at,
                };


                setProductData(transformedProduct);
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : "Failed to fetch product information";
                setError(errorMessage);

            } finally {
                setLoading(false);
            }
        };

        fetchProductInfo();
    }, [productId]); // Re-run effect if productId changes

    // Modal Open/Close Handlers
    const openUpdateModal = () => {
        setIsProductModalOpen(true);
    };

    const openDeleteModal = () => {
        setIsConfirmDeleteModalOpen(true);
    };

    const confirmDelete = async () => {
        if (!productData) return;
        setLoading(true); // Indicate loading during delete action
        try {
            await handleDeleteProduct(productData.id);
            // Let the parent page handle redirection or state update
        } catch (deleteError) {
            setError(deleteError instanceof Error ? deleteError.message : "Failed to delete product");
            setLoading(false); // Stop loading only on error
        }
        // No finally setLoading(false) as parent handles redirect/state change
        setIsConfirmDeleteModalOpen(false);
    };

    // Handles submission from the modal
    const handleModalSubmit = async (updatedData: Partial<Product>) => {
        if (!productData) return;
        setLoading(true); // Indicate loading during update action
        try {
            // Call the handler passed from the page, which handles the actual API call
            // The parent component will handle toast notifications
            await handleUpdateProduct({ ...productData, ...updatedData });

            // Fetch the latest product data to show updated images
            const response = await fetch(`/api/products/${productId}`);
            if (!response.ok) {
                throw new Error(`Failed to refresh product data: ${response.status}`);
            }

            const refreshedData = await response.json() as ApiProductResponse;

            // Transform the refreshed data using the same logic as in useEffect
            const refreshedProduct: Product = {
                id: refreshedData.id,
                name: refreshedData.name || refreshedData.product_name || "Unnamed Product",
                productType: refreshedData.type || refreshedData.product_type || "unknown",
                currency_id: refreshedData.currency_id,
                currency_code: mapCurrencyIdToStringSync(refreshedData.currency_id || 0),
                price: refreshedData.price || 0,
                variant: refreshedData.variant || undefined,
                description: refreshedData.description || "",
                productDetails: transformProductDetails(refreshedData.product_details || null),
                images: (() => {
                    if (!refreshedData.images || !Array.isArray(refreshedData.images)) {
                        return [];
                    }

                    return refreshedData.images
                        .map((img) => {
                            if (typeof img === 'string') return img;
                            if (img && typeof img === 'object') {
                                if ('path' in img && img.path) return img.path;
                                if ('url' in img && img.url) return img.url;
                                if ('src' in img && img.src) return img.src;
                            }
                            return null;
                        })
                        .filter((img): img is string => img !== null);
                })(),
                shop_id: refreshedData.shop_id || undefined,
                created_at: refreshedData.created_at,
                updated_at: refreshedData.updated_at,
            };

            // Update the product data with the refreshed data
            setProductData(refreshedProduct);

        } catch (updateError) {
            const errorMessage = updateError instanceof Error ? updateError.message : "Failed to update product";
            setError(errorMessage);
        } finally {
            setLoading(false); // Stop loading after update attempt
        }
        setIsProductModalOpen(false);
    };

    // Render logic using internal state
    if (loading) {
        return (
            <div className="rounded-sm border border-stroke bg-white p-6 shadow-default dark:border-strokedark dark:bg-boxdark">
                <InlineLoader message="Loading product details..." size="large" />
            </div>
        );
    }
    if (error) return <div className="p-4 text-red-500 text-center rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">Error: {error}</div>;
    if (!productData) return <div className="p-4 text-center rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">Product not found or failed to load.</div>;

    // Main return JSX (updated to use new components)
    return (
        <div className="pb-6 pt-4">
            {/* Header and Back Link */}
            <div className="mb-4 flex items-center justify-between">
                <h1 className="whitespace-nowrap text-3xl font-bold">
                    {productData?.name}
                </h1>
                <Link href="/products" className="text-blue-500 hover:underline">
                    Back to Products
                </Link>
            </div>

            {/* Product Information Sections */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                {/* Basic Details Card */}
                <ProductDetailsCard title="Basic Details">
                    <p className="text-sm text-gray-700">
                        <strong>Type:</strong> {productData?.productType}
                    </p>
                    {productData?.variant && (
                        <p className="text-sm text-gray-700">
                            <strong>Model:</strong> {productData.variant}
                        </p>
                    )}
                    <p className="text-sm text-gray-700">
                        <strong>Price:</strong> {productData?.currency_code} {productData?.price?.toFixed(2)}
                    </p>
                </ProductDetailsCard>

                {/* Description Card */}
                <ProductDetailsCard title="Description" className="md:col-span-2 lg:col-span-1">
                    <p className="text-sm text-gray-700">
                        {productData?.description || "No description provided."}
                    </p>
                </ProductDetailsCard>

                {/* Custom Details Card */}
                {(productData?.productDetails && productData.productDetails.length > 0) && (
                    <ProductDetailsCard title="Custom Details">
                        {productData.productDetails.map((detail, index) => (
                            <p key={index} className="text-sm text-gray-700">
                                <strong>{detail.customFieldName.charAt(0).toUpperCase() + detail.customFieldName.slice(1)}:</strong> {detail.value.charAt(0).toUpperCase() + detail.value.slice(1)}
                            </p>
                        ))}
                    </ProductDetailsCard>
                )}

                {/* Meta Information Card */}
                {(productData?.created_at || productData?.updated_at) && (
                    <ProductDetailsCard title="Meta Information">
                        {productData.created_at && (
                            <p className="text-sm text-gray-700">
                                <strong>Created At:</strong> {formatDateTime(productData.created_at)}
                            </p>
                        )}
                        {productData.updated_at && productData.updated_at !== "0001-01-01T00:00:00Z" && (
                            <p className="text-sm text-gray-700">
                                <strong>Updated At:</strong> {formatDateTime(productData.updated_at)}
                            </p>
                        )}
                    </ProductDetailsCard>
                )}
            </div>

            {/* Product Images Section - Using the new component */}
            <ProductImageGallery
                images={productData.images as string[]}
                productName={productData.name}
                onImageClick={(imagePath, index) => {

                    setCurrentImagePath(imagePath);
                    setCurrentImageIndex(index);
                    setIsImagePreviewModalOpen(true);
                }}
            />

            {/* Action Buttons */}
            <div className="mt-6 flex space-x-4 border-t pt-4">
                <button
                    className="rounded bg-wbv-theme px-4 py-2 text-white hover:bg-opacity-90"
                    onClick={openUpdateModal}
                    disabled={loading}
                >
                    Edit Product
                </button>
                <button
                    className="rounded bg-red-600 px-4 py-2 text-white hover:bg-red-700"
                    onClick={openDeleteModal}
                    disabled={loading}
                >
                    Delete Product
                </button>
            </div>

            {/* Edit Product Modal */}
            {isProductModalOpen && (
                <ProductModal
                    isOpen={isProductModalOpen}
                    onClose={() => setIsProductModalOpen(false)}
                    onSubmit={handleModalSubmit} // Calls internal submit handler
                    currentProduct={{ ...productData, images: productData.images as string[] }} // Pass image paths (strings)
                />
            )}

            {/* Confirm Delete Modal */}
            <ConfirmModal
                isOpen={isConfirmDeleteModalOpen}
                onClose={() => setIsConfirmDeleteModalOpen(false)}
                onConfirm={confirmDelete}
                title="Confirm Product Deletion"
                message={`Are you sure you want to delete the product "${productData?.name}"? This action cannot be undone.`}
                cta="Delete"
                ctaBtnColor="danger"
            />

            {/* Image Preview Modal */}
            <ImagePreviewModal
                isOpen={isImagePreviewModalOpen}
                onClose={() => setIsImagePreviewModalOpen(false)}
                imagePath={currentImagePath}
                productName={productData.name}
                allImages={productData.images as string[]}
                currentIndex={currentImageIndex}
                onNavigate={(newIndex) => {
                    setCurrentImageIndex(newIndex);
                    if (productData?.images && productData.images[newIndex]) {
                        setCurrentImagePath(productData.images[newIndex] as string);
                    }
                }}
            />

        </div>
    );
};

export default ProductPreview;
