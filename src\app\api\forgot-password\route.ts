import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    const { email } = await request.json();

    if (!email || typeof email !== "string") {
      return NextResponse.json(
        { error: "Invalid email address provided." },
        { status: 400 },
      );
    }

    const encodedURIemail = encodeURIComponent(email);
    const forgotPasswordResponse = await fetch(
      `${process.env.WHITEBOOK_API_URL_V2}/auth/forgot-password?email=${encodedURIemail}`,
      {
        method: "POST",
      },
    );

    if (!forgotPasswordResponse.ok) {
      console.error(
        "Failed to send password reset link:",
        forgotPasswordResponse.status,
        forgotPasswordResponse.statusText,
      );

      return NextResponse.json(
        { error: "Failed to send password reset link." },
        { status: forgotPasswordResponse.status },
      );
    }

    return NextResponse.json(
      { message: "Password reset link has been sent to your email." },
      { status: 200 },
    );
  } catch (error) {
    console.error("Error handling forgot password request:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred. Please try again." },
      { status: 500 },
    );
  }
}
