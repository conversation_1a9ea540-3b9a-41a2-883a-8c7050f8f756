import { NextRequest, NextResponse } from "next/server";
import { v2EngineAxiosInstance, APIErrorResponse } from "@/utils/axios";

// GET /api/products/[id]/images - Get all images for a product
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  const { id } = params;

  if (isNaN(Number(id))) {
    return NextResponse.json({ error: "Invalid Product ID format" }, { status: 400 });
  }

  try {
    console.log(`Fetching images for product ${id}`);

    // Call the backend API to get all images for the product
    const response = await v2EngineAxiosInstance.get(`/products/${id}`);

    // Extract the images from the response safely
    let images: unknown = [];
    const data = response.data as { data?: { images?: unknown }, images?: unknown } | undefined;
    if (data?.data && Array.isArray((data.data as { images?: unknown }).images)) {
      images = (data.data as { images?: unknown }).images;
    } else if (Array.isArray(data?.images)) {
      images = data.images;
    }

    // Return the images
    return NextResponse.json({ images }, { status: 200 });
  } catch (error: unknown) {
    const err = error as APIErrorResponse;
    console.error("--- Error During V2 Engine Request ---");
    console.error(
      "V2 Engine Error Response:",
      err.response?.status,
      err.response?.data || err.response?.message || err
    );
    console.error("-----------------------------------");

    // Extract status and message for the frontend response
    const status = err.response?.status || 500;
    const message =
      (err.response?.data?.message as string) ||
      (err.response?.data?.error as string) ||
      (err.response?.message as string) ||
      (status === 400 ? "Bad Request to V2 engine" : "Error fetching product images");

    return NextResponse.json(
      { error: message },
      { status }
    );
  }
}

// POST /api/products/[id]/images - Add images to a product
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  const { id } = params;

  if (isNaN(Number(id))) {
    return NextResponse.json({ error: "Invalid Product ID format" }, { status: 400 });
  }

  try {
    // Use type assertion to avoid unsafe assignment
    const imageData = (await req.json()) as Record<string, unknown>;

    console.log("--- Received Product Images Payload ---");
    console.log(JSON.stringify(imageData, null, 2));
    console.log("-------------------------------------");

    // Send the image data to the backend
    const response = await v2EngineAxiosInstance.post(
      `/products/${id}/images`,
      imageData,
      {
        headers: { "Content-Type": "application/json" }
      }
    );

    return NextResponse.json(response.data, { status: 201 });
  } catch (error: unknown) {
    const err = error as APIErrorResponse;
    console.error("--- Error During V2 Engine Request ---");
    console.error(
      "V2 Engine Error Response:",
      err.response?.status,
      err.response?.data || err.response?.message || err
    );
    console.error("-----------------------------------");

    // Extract status and message for the frontend response
    const status = err.response?.status || 500;
    const message =
      (err.response?.data?.message as string) ||
      (err.response?.data?.error as string) ||
      (err.response?.message as string) ||
      (status === 400 ? "Bad Request to V2 engine" : "Error adding images to product");

    return NextResponse.json(
      { error: message },
      { status }
    );
  }
}
