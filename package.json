{"name": "whitebook-nextjs-admin-dashboard", "version": "1.3.5", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint \"src/**/*.+(ts|tsx)\"", "lint:fix": "eslint \"src/**/*.+(ts|tsx)\" --fix", "format": "prettier . --write", "format:check": "prettier . --check"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^2.2.0", "@heroicons/react": "^1.0.6", "@material-tailwind/react": "^2.1.10", "@react-pdf/renderer": "^4.0.0", "@tanstack/react-table": "^8.20.5", "@types/formidable": "^3.4.5", "apexcharts": "^3.45.2", "axios": "^1.7.7", "chart.js": "^4.4.6", "country-state-city": "^3.2.1", "dotenv": "^16.4.5", "dotenv-vault": "^1.26.2", "flatpickr": "^4.6.13", "formidable": "^3.5.2", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jsvectormap": "^1.6.0", "jwt-decode": "^4.0.0", "minio": "^8.0.4", "next": "^14.2.4", "next-auth": "^4.24.10", "papaparse": "^5.4.1", "react": "^18", "react-apexcharts": "^1.4.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18", "react-dropzone": "^14.3.5", "react-pdf-tailwind": "^2.3.0", "react-phone-input-2": "^2.15.1", "react-select": "^5.10.0", "react-toastify": "^11.0.2", "sharp": "^0.33.5", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@faker-js/faker": "^9.0.3", "@ianvs/prettier-plugin-sort-imports": "^4.4.0", "@testing-library/jest-dom": "^6.6.2", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20", "@types/papaparse": "^5.3.14", "@types/react": "18.3.12", "@types/react-dom": "^18", "@types/react-dropzone": "^4.2.2", "@types/react-fontawesome": "^1.6.8", "@types/react-toastify": "^4.0.2", "@types/xlsx": "^0.0.35", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "autoprefixer": "^10.0.1", "eslint": "^9.18.0", "eslint-config-next": "^15.1.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-diff": "^2.0.3", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.7.0", "postcss": "^8", "prettier": "^3.4.2", "prettier-plugin-sort-json": "^4.1.1", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.1", "typescript": "^5.7.2", "typescript-eslint": "^8.31.0"}}