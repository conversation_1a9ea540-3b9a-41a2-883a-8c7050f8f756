import React from "react";
import { createColumnHelper, SortingState } from "@tanstack/react-table";
import "@/css/table.css";
import DebouncedSearchInput from "@/components/Inputs/DebouncedSearchInput";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faMagnifyingGlass } from "@fortawesome/free-solid-svg-icons";
import TanStackTable from "@/components/Tables/TanStackTable";
import { getPageSizeOptions } from "@/components/Tables/utils";

interface Make {
  vehicle_id: number;
  name: string;
  total_vehicle_models: number;
  total_vehicle_variants: number;
}

interface VehicleMakesProps {
  makes: Make[];
  loading: boolean;
  error: any;
}

const VehicleMakes: React.FC<VehicleMakesProps> = ({
  makes,
  loading,
  error,
}) => {
  const [globalFilter, setGlobalFilter] = React.useState("");
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [pageSize, setPageSize] = React.useState<number>(10); // Default page size
  const columnHelper = createColumnHelper<Make>();

  const columns = [
    columnHelper.accessor("name", {
      cell: (info) => (
        <div className="max-width-cell truncated-cell">{info?.getValue()}</div>
      ),
      header: "Make",
    }),
    columnHelper.accessor("total_vehicle_models", {
      cell: (info) => <div className="max-width-cell">{info?.getValue()}</div>,
      header: "Number of Models",
    }),
    columnHelper.accessor("total_vehicle_variants", {
      cell: (info) => <div className="max-width-cell">{info?.getValue()}</div>,
      header: "Number of Variants",
    }),
  ];

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div className="flex flex-col pt-4">
      {/* {JSON.stringify(variants, null, 2)} */}
      <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
          {/* {Search and Upload} */}
          <div className="mb-2 flex justify-between">
            <div className="flex w-full items-center gap-1">
              <FontAwesomeIcon icon={faMagnifyingGlass} />
              <DebouncedSearchInput
                value={globalFilter ?? ""}
                onChange={(value) => setGlobalFilter(String(value))}
                debounced={500}
                placeholder="Search makes..."
              />
            </div>
            {/* <CsvFileInput onFileLoad={handleFileLoad} /> */}
            {/* <DownloadBtn data={makes} fileName="makes" /> */}
          </div>
          <div className="my-5 overflow-hidden border-b border-gray-200 shadow">
            <TanStackTable
              data={makes}
              columns={columns}
              globalFilter={globalFilter}
              sorting={sorting}
              setGlobalFilter={setGlobalFilter}
              setSorting={setSorting}
              pageSizeOptions={getPageSizeOptions(makes)}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default VehicleMakes;
