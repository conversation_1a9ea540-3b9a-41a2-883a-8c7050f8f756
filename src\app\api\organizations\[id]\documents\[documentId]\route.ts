import { NextRequest, NextResponse } from "next/server";
import { Client as MinioClient } from 'minio';
import axios from "axios";

const {
    S3_ENDPOINT,
    S3_BUCKET,
    S3_USE_SSL,
    S3_ACCESS_KEY,
    S3_SECRET_KEY,
    WHITEBOOK_API_URL_V2,
    WHITEBOOK_ADMIN_API_KEY,
    S3_ONBOARDING_DOCUMENTS_PATH,
} = process.env;

const minioClient = new MinioClient({
    endPoint: S3_ENDPOINT!,
    port: S3_USE_SSL === 'true' ? 443 : 80,
    useSSL: S3_USE_SSL === 'true',
    accessKey: S3_ACCESS_KEY!,
    secretKey: S3_SECRET_KEY!,
});

export async function PUT(
    req: NextRequest,
    { params }: { params: { id: string; documentId: string } }
) {
    try {


        const formData = await req.formData();
        const file = formData.get('file') as File;
        const { id, documentId } = params;

        if (!file) {
            return NextResponse.json(
                { error: 'File is required' },
                { status: 400 }
            );
        }

        // Generate timestamp for unique naming
        const timestamp = Date.now();
        const fileExtension = file.name.split('.').pop();
        const baseFileName = `${timestamp}-${file.name.split('.')[0]}`;
        const fileName = `${S3_ONBOARDING_DOCUMENTS_PATH}/${baseFileName}.${fileExtension}`;

        // Upload to Minio
        const buffer = Buffer.from(await file.arrayBuffer());

        try {
            await minioClient.putObject(S3_BUCKET!, fileName, buffer, buffer.length, {
                'Content-Type': file.type || undefined,
            });
        } catch (uploadError) {
            console.error('Upload error:', uploadError);
            return NextResponse.json({ error: 'Upload failed' }, { status: 500 });
        }

        // Prepare backend data
        const backendData = {
            name: baseFileName,
            type: `.${fileExtension}`,
            path: fileName,
            file: `${baseFileName}.${fileExtension}`
        };

        const response = await axios.put(
            `${WHITEBOOK_API_URL_V2}/organizations/${id}/documents/${documentId}`,
            backendData,
            {
                headers: {
                    "X-WHITEBOOK-API-Key": WHITEBOOK_ADMIN_API_KEY,
                    "Content-Type": "application/json",
                },
            }
        );

        return NextResponse.json({
            data: response.data,
            message: 'Document updated successfully'
        }, { status: 200 });

    } catch (error) {
        console.error('Error updating document:', error);
        return NextResponse.json(
            { error: 'Failed to update document' },
            { status: 500 }
        );
    }
}

export async function DELETE(
    req: NextRequest,
    { params }: { params: { id: string; documentId: string } }
) {
    try {
        const { id, documentId } = params;

        // Delete document from backend first
        const response = await axios.delete(
            `${WHITEBOOK_API_URL_V2}/organizations/${id}/documents/${documentId}`,
            {
                headers: {
                    "X-WHITEBOOK-API-Key": WHITEBOOK_ADMIN_API_KEY,
                    "Content-Type": "application/json",
                },
            }
        );

        if (response.status === 204 || response.status === 200) {
            return NextResponse.json({
                message: 'Document deleted successfully'
            }, { status: 200 });
        }

        throw new Error('Failed to delete document from backend');

    } catch (error) {
        console.error('Error deleting document:', error);
        return NextResponse.json(
            { error: 'Failed to delete document' },
            { status: 500 }
        );
    }
}