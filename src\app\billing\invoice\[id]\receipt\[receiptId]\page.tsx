"use client";

import Receipt from "@/components/Billing/receipt/Receipt";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { InvoiceStruct } from "@/schemas/billingSchema";

const ReceiptPage = () => {
  const params = useParams();
  const id = Array.isArray(params.id)
    ? parseInt(params.id[0] || "0", 10)
    : parseInt(params.id || "0", 10);
  const [invoice, setInvoice] = useState<InvoiceStruct>({
    id: 0,
    due_date: "",
    contact_email: "",
    address: {
      name: "",
      street: "",
      city: "",
      state: "",
      zip_code: 0,
      country: "",
      phone_number: "",
      created_at: "",
      created_by: 0,
      updated_at: "",
      updated_by: 0,
    },
    receipts: [],
    payments: [],
    summary: {
      total_api_calls: 0,
      cost_per_api_call: 0,
      total_billed: 0,
      pricing_type: "",
    },
    subtotal: 0,
    total: 0,
    tpin: "",
    currency_id: 0,
    status: "",
    notes: [],
    created_at: "",
    created_by: 0,
    updated_at: "",
    updated_by: 0,
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchInvoice = async () => {
      try {
        const fetchInvoiceResponse = await fetch(`/api/billing/invoices/${id}`, {
          method: "GET",
        });
        const invoiceData = await fetchInvoiceResponse.json();
        if (invoiceData) {
          setInvoice(invoiceData.data);
        } else {
          setError("Invoice not found");
        }
        setLoading(false);
      } catch (err) {
        setError("Error fetching invoice data");
        setLoading(false);
      }
    };

    fetchInvoice();
  }, [id]);

  const handleReceiptDownload = () => {
    console.log("Downloading receipt...");
    // TODO: Implement receipt download
  };

  return (
    <DefaultLayout>
      <Receipt
        invoice={invoice}
        loading={loading}
        error={error}
        handleReceiptDownload={handleReceiptDownload}
      />
    </DefaultLayout>
  );
};

export default ReceiptPage;
