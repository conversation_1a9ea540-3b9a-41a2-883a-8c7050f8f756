"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import axios from "axios";
import { z } from "zod";
import { LoginData, loginSchema } from "@/schemas/loginSchema";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faEnvelope,
  faEye,
  faEyeSlash,
} from "@fortawesome/free-solid-svg-icons";
import { signIn, useSession } from "next-auth/react";

const initialFormData = {
  email: "",
  password: "",
};

const SigninPage: React.FC = () => {
  const { data: session, status } = useSession();
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [formData, setFormData] = useState<LoginData>(initialFormData);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevData: any) => ({
      ...prevData,
      [name]: value,
    }));
  };

  useEffect(() => {
    if (session && session.user) {
      window.location.href = "/";
    }
  }, [session]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Validate form data using zod
      loginSchema.parse(formData);
      const formDataToSend = {
        ...formData,
      };

      // Send form data to backend with next-auth
      const response = await signIn("credentials", {
        redirect: false,
        email: formDataToSend.email,
        password: formDataToSend.password,
      });

      if (response?.error) {
        setErrors({ general: response.error });
      }
    } catch (error) {
      console.error("Error during signin on client sign in page:", error);
      if (error instanceof z.ZodError) {
        const zodErrors = error.errors.reduce((acc: any, curr: any) => {
          acc[curr.path[0]] = curr.message;
          return acc;
        }, {});
        setErrors(zodErrors);
      } else {
        if (
          axios.isAxiosError(error) &&
          error.response &&
          error.response.data
        ) {
          setErrors({ general: "Email or password is incorrect" });
        } else {
          setErrors({
            general: "An unexpected error occurred. Please try again.",
          });
        }
      }
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-100">
      <div className="my-10 w-full max-w-md space-y-6 rounded-lg bg-white px-8 pb-8 pt-0 shadow-md">
        <div className="mb-10 text-center">
          <Image
            src="/images/logos/Whitebook ALT 8.png"
            width={200}
            height={800}
            alt="Logo"
            className="mx-auto max-h-45"
          />
          <h2 className="mt-2 text-2xl font-bold text-gray-900">Sign In</h2>
          <hr
            className="mx-auto mt-4 w-full border-0"
            style={{
              height: "1px",
              background:
                "linear-gradient(to right, transparent, #06b6d4, transparent)",
            }}
          />
        </div>
        <form onSubmit={handleSubmit} className="space-y-4">
          {errors.general && (
            <div className="rounded bg-red-100 p-4 text-red-700">
              <p className="text-center text-sm">{errors.general}</p>
            </div>
          )}
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700"
            >
              Email
            </label>
            <div className="relative">
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 text-sm leading-5">
                <FontAwesomeIcon icon={faEnvelope} />
              </div>
            </div>
            {errors.email && (
              <p className="mt-1 text-xs text-red-500">{errors.email}</p>
            )}
          </div>
          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium text-gray-700"
            >
              Password
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="Password"
                className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 flex items-center pr-3 text-sm leading-5"
              >
                <FontAwesomeIcon icon={showPassword ? faEye : faEyeSlash} />
              </button>
            </div>
            {errors.password && (
              <p className="mt-1 text-xs text-red-500">{errors.password}</p>
            )}
          </div>
          <button
            type="submit"
            className="w-full rounded-md bg-wbv-theme px-4 py-2 text-white transition duration-300 hover:bg-opacity-90"
          >
            Sign In
          </button>
        </form>
        <div className="pb-0 pt-1 text-center">
          <a
            href="/auth/forgot-password"
            className="text-sm text-blue-500 hover:underline"
          >
            Forgot Password?
          </a>
        </div>
        <div className="border-t pt-2 text-center">
          <p className="text-sm text-gray-600">
            Don&apos;t have an account?&nbsp;
            <a href="/auth/sign-up" className="text-blue-500 hover:underline">
              Sign Up
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default SigninPage;
