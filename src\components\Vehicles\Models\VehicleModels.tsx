import React, { useEffect } from "react";
// import { users } from "./dataFaker";
import { createColumnHelper, SortingState } from "@tanstack/react-table";
import "@/css/table.css";
import DebouncedSearchInput from "@/components/Inputs/DebouncedSearchInput";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faMagnifyingGlass } from "@fortawesome/free-solid-svg-icons";
// import axios from "axios";
// import CsvFileInput from "./CsvFileInput";
import TanStackTable from "@/components/Tables/TanStackTable";
import { v2EngineAxiosInstance } from "@/utils/axios";
import { getPageSizeOptions } from "@/components/Tables/utils";

interface Model {
  model: string;
  total_vehicle_variants: number;
  total_year_range: number;
}

interface VehicleModelsProps {
  models: Model[];
  loading: boolean;
  error: any;
}

const VehicleModels: React.FC<VehicleModelsProps> = ({
  models,
  loading,
  error,
}) => {
  const [globalFilter, setGlobalFilter] = React.useState("");
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [pageSize, setPageSize] = React.useState<number>(10); // Default page size

  const columnHelper = createColumnHelper<Model>();

  const columns = [
    columnHelper.accessor("model", {
      cell: (info) => (
        <div className="max-width-cell truncated-cell">{info?.getValue()}</div>
      ),
      header: "Model",
    }),
    columnHelper.accessor("total_vehicle_variants", {
      cell: (info) => <div className="max-width-cell">{info?.getValue()}</div>,
      header: "Number of Variants",
    }),
    columnHelper.accessor("total_year_range", {
      cell: (info) => <div className="max-width-cell">{info?.getValue()}</div>,
      header: "Data Total Year Range",
    }),
  ];

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div className="flex flex-col pt-4">
      {/* {JSON.stringify(variants, null, 2)} */}
      <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
          {/* {Search and Upload} */}
          <div className="mb-2 flex justify-between">
            <div className="flex w-full items-center gap-1">
              <FontAwesomeIcon icon={faMagnifyingGlass} />
              <DebouncedSearchInput
                value={globalFilter ?? ""}
                onChange={(value) => setGlobalFilter(String(value))}
                debounced={500}
                placeholder="Search models..."
              />
            </div>
            {/* <CsvFileInput onFileLoad={handleFileLoad} /> */}
            {/* <DownloadBtn data={models} fileName="models" /> */}
          </div>
          <div className="my-5 overflow-hidden border-b border-gray-200 shadow">
            <TanStackTable
              data={models}
              columns={columns}
              globalFilter={globalFilter}
              sorting={sorting}
              setGlobalFilter={setGlobalFilter}
              setSorting={setSorting}
              pageSizeOptions={getPageSizeOptions(models)}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default VehicleModels;
