import React, { ChangeEvent, useState } from "react";
import { createColumnHelper, SortingState } from "@tanstack/react-table";
import "@/css/table.css";
import DebouncedSearchInput from "../../Inputs/DebouncedSearchInput";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faUpload,
  faMagnifyingGlass,
  faFileCsv,
} from "@fortawesome/free-solid-svg-icons";
import TanStackTable from "@/components/Tables/TanStackTable";
import { getPageSizeOptions } from "@/components/Tables/utils";

interface Variant {
  make: string;
  model: string;
  name: string;
  cubic_capacity: number;
  body_type: string;
  cif: number;
  market_value: number;
}

interface VehicleVariantsProps {
  variants: Variant[];
  loading: boolean;
  error: any;
}

const VehicleVariants: React.FC<VehicleVariantsProps> = ({
  variants,
  loading,
  error,
}) => {
  const [globalFilter, setGlobalFilter] = useState("");
  const [sorting, setSorting] = useState<SortingState>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [pageSize, setPageSize] = React.useState<number>(10); // Default page size

  const columnHelper = createColumnHelper<Variant>();

  const columns = [
    columnHelper.accessor("name", {
      cell: (info) => (
        <div className="max-width-cell truncated-cell">{info?.getValue()}</div>
      ),
      header: "Variant",
    }),
    columnHelper.accessor("model", {
      cell: (info) => (
        <div className="max-width-cell truncated-cell">{info?.getValue()}</div>
      ),
      header: "Model",
    }),
    columnHelper.accessor("make", {
      cell: (info) => (
        <div className="max-width-cell truncated-cell">{info?.getValue()}</div>
      ),
      header: "Make",
    }),
    columnHelper.accessor("cubic_capacity", {
      cell: (info) => <div className="max-width-cell">{info?.getValue()}</div>,
      header: "Cubic  Capacity",
    }),
    columnHelper.accessor("body_type", {
      cell: (info) => (
        <div className="max-width-cell truncated-cell">{info?.getValue()}</div>
      ),
      header: "Body Type",
    }),
    columnHelper.accessor("cif", {
      cell: (info) => <div className="max-width-cell">{info?.getValue()}</div>,
      header: "CIF (USD)",
    }),
    columnHelper.accessor("market_value", {
      cell: (info) => <div className="max-width-cell">{info?.getValue()}</div>,
      header: "Market Value (ZMW)",
    }),
  ];

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    console.log("File change event:", event);
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setIsUploading(true);
    }
  };

  const handleUploadClick = async () => {
    console.log("Upload file click, file:", selectedFile);
    if (!selectedFile) {
      return;
    }
    const formData = new FormData();
    formData.append("file", selectedFile);

    try {
      const response = await fetch("/api/vehicles/upload", {
        method: "POST",
        body: formData,
      });
      console.log("Upload response status:", response.status);
      if (response.status === 200) {
        setSelectedFile(null); // Clear the selected file after upload
        setIsUploading(false);
        alert("Variants uploaded successfully");
      }
    } catch (error) {
      console.error("Error uploading variants:", error);
      alert("Variants upload failed");
    }
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div className="flex flex-col pt-4">
      <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
          {/* {Search and Upload} */}
          <div className="mb-2 flex justify-between">
            <div className="flex w-full items-center gap-1">
              <FontAwesomeIcon icon={faMagnifyingGlass} />
              <DebouncedSearchInput
                value={globalFilter ?? ""}
                onChange={(value) => setGlobalFilter(String(value))}
                debounced={500}
                placeholder="Search variants..."
              />
            </div>
            <div className="flex flex-col items-start gap-2">
              <div className="flex items-center gap-2">
                <input
                  type="file"
                  onChange={handleFileChange}
                  className="hidden"
                  id="file-upload"
                />
                <label
                  htmlFor="file-upload"
                  className="ml-2 inline-flex items-center rounded bg-gray-700 px-4 py-2 text-xs font-semibold text-white hover:bg-gray-900"
                >
                  Choose
                  <FontAwesomeIcon
                    className="ml-2 h-4 w-4 fill-current"
                    icon={faFileCsv}
                  />
                </label>
                <button
                  disabled={!isUploading}
                  className={`ml-2 inline-flex items-center rounded bg-wbv-theme px-4 py-2 text-xs font-semibold text-white  ${isUploading
                      ? "bg-wbv-theme hover:bg-opacity-90"
                      : "cursor-not-allowed bg-wbv-theme opacity-50"
                    }`}
                  onClick={handleUploadClick}
                >
                  <FontAwesomeIcon
                    className="mr-2 h-4 w-4 fill-current"
                    icon={faUpload}
                  />
                  <span>Upload</span>
                </button>
              </div>
              {selectedFile && (
                <span className="text-sm text-gray-700">
                  Selected file: {selectedFile.name}
                </span>
              )}
            </div>
          </div>
          <div className="my-5 overflow-hidden border-b border-gray-200 shadow">
            <TanStackTable
              data={variants}
              columns={columns}
              globalFilter={globalFilter}
              sorting={sorting}
              setGlobalFilter={setGlobalFilter}
              setSorting={setSorting}
              pageSizeOptions={getPageSizeOptions(variants)}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default VehicleVariants;
