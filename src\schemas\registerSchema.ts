import { z } from "zod";

export const registerSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phone_number: z.string().regex(/^\+?[1-9]\d{1,14}$/, "Invalid phone_number"),
  password: z.string().min(8, "Password must be at least 8 characters long"),
});

export type RegisterData = z.infer<typeof registerSchema>;
