import React from 'react';
import { ProductBasicFormSectionProps } from '@/types/product/modal';

const ProductBasicFormSection: React.FC<ProductBasicFormSectionProps> = ({
  productData,
  currencies,
  loadingCurrencies,
  currencyError,
  handleChange,
  isLoading = false
}) => {
  return (
    <>
      <div className="flex flex-col">
        <label className="mb-1 text-sm font-medium">Name</label>
        <input
          type="text"
          name="name"
          value={productData.name}
          onChange={handleChange}
          disabled={isLoading}
          className={`rounded border border-gray-300 p-2 focus:border-wbv-theme focus:outline-none focus:ring-2 focus:ring-wbv-theme ${isLoading ? "cursor-not-allowed opacity-50" : ""}`}
          required
        />
      </div>

      <div className="flex flex-col">
        <label className="mb-1 text-sm font-medium">Product Type</label>
        <select
          name="productType"
          value={productData.productType}
          onChange={handleChange}
          disabled={isLoading}
          className={`rounded border border-gray-300 p-2 focus:border-wbv-theme focus:outline-none focus:ring-2 focus:ring-wbv-theme ${isLoading ? "cursor-not-allowed opacity-50" : ""}`}
          required
        >
          <option value="">Select Type</option>
          <option value="Vehicle">Vehicle</option>
          <option value="Other">Other</option>
        </select>
      </div>

      <div className="flex flex-col">
        <label className="mb-1 text-sm font-medium">Currency</label>
        {loadingCurrencies ? (
          <div className="rounded border border-gray-300 bg-gray-50 p-2 text-gray-500">
            Loading currencies...
          </div>
        ) : currencyError ? (
          <div className="rounded border border-red-300 bg-red-50 p-2 text-red-700">
            Error: {currencyError}
          </div>
        ) : currencies.length > 0 ? (
          <select
            name="currency_id"
            value={productData.currency_id || ''}
            onChange={handleChange}
            disabled={isLoading}
            className={`rounded border border-gray-300 p-2 focus:border-wbv-theme focus:outline-none focus:ring-2 focus:ring-wbv-theme ${isLoading ? "cursor-not-allowed opacity-50" : ""}`}
            required
          >
            <option value="">Select Currency</option>
            {currencies.map((currency) => (
              <option key={currency.id} value={currency.id}>
                {currency.code} {currency.name ? `(${currency.name})` : ''}
              </option>
            ))}
          </select>
        ) : (
          <div className="rounded border border-gray-300 bg-gray-50 p-2 text-gray-500">
            No currencies available
          </div>
        )}
      </div>

      <div className="flex flex-col">
        <label className="mb-1 text-sm font-medium">Price</label>
        <input
          type="number"
          name="price"
          value={productData.price}
          onChange={handleChange}
          disabled={isLoading}
          className={`rounded border border-gray-300 p-2 focus:border-wbv-theme focus:outline-none focus:ring-2 focus:ring-wbv-theme ${isLoading ? "cursor-not-allowed opacity-50" : ""}`}
          required
          min="0"
          step="0.5"
        />
      </div>

      <div className="col-span-2">
        <label className="mb-1 text-sm font-medium">Description</label>
        <textarea
          name="description"
          value={productData.description}
          onChange={handleChange}
          disabled={isLoading}
          className={`w-full rounded border border-gray-300 p-2 focus:border-wbv-theme focus:outline-none focus:ring-2 focus:ring-wbv-theme ${isLoading ? "cursor-not-allowed opacity-50" : ""}`}
          rows={4}
          required
        />
      </div>
    </>
  );
};

export default ProductBasicFormSection;
