This script generates an Excel report summarizing vehicle search history data.
The script performs the following steps:
1. Reads search data from a JSON file.
2. Filters the data to include searches from Friday to Thursday.
3. Summarizes the searches per day, month, and quarter.
4. Writes the filtered and summarized data to an Excel file with multiple sheets.
Functions:
- get_searches_df: Reads search data from a JSON file and returns a DataFrame.
- get_last_week_number: Returns the number of the previous week.
- get_writable_df: Prepares a DataFrame for writing to an Excel file.
- get_searches_per_day_df: Summarizes searches per day.
- get_current_week_number: Returns the number of the current week.
- get_friday_to_thursday_df: Filters searches to include only those from Friday to Thursday.
- summarize_searches_per_interval: Summarizes searches per specified intervals (e.g., month, quarter).

Input:
- Latest search data in searches.json file

Output:
- An Excel file named "WBV_Search_History_Report_W<current_week_number>_<current_year>.xlsx" with the following sheets:
    - "FriToThu - All (W<last_week_number>ToW<current_week_number>)": Filtered searches from Friday to Thursday.
    - "FriToThu - Daily (W<last_week_number>ToW<current_week_number>)": Daily summary of searches from Friday to Thursday.
    - "Monthly": Monthly summary of searches.
    - "Quarterly": Quarterly summary of searches.
    - "AllSearches": All search data.

Requirements:
- Ensure you have python3 and pip installed

Usage:
1. Install the necessary dependencies as listed in requirements.txt file
# pip install -r requirements.txt

2. Run app
# python main.py


Note: The report is generated in the root dir