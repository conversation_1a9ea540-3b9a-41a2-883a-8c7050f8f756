import React from "react";
import Link from "next/link";
import { createColumnHelper } from "@tanstack/react-table";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faFileInvoiceDollar,
  faFilePdf,
  faReceipt,
  faWallet,
  faXmark,
} from "@fortawesome/free-solid-svg-icons";
import { InvoiceStruct } from "@/schemas/billingSchema";
import {
  formatCurrency,
  formatDate,
  capitalizeFirstLetter,
} from "@/components/utils/utils";
import InvoicePDF from "@/components/Billing/invoice/InvoicePDF";
import ReceiptPDF from "@/components/Billing/receipt/ReceiptPDF";
import { PDFDownloadLink } from "@react-pdf/renderer";

const columnHelper = createColumnHelper<InvoiceStruct>();

export const getColumns = (
  openPaymentModal: (id: number) => void,
  openCancellationModal: (id: number) => void,
  user: { role_name: string } | null,
) => [
  columnHelper.accessor("id", {
    cell: (info) => (
      <div className="max-width-cell truncated-cell">{info?.getValue()}</div>
    ),
    header: "No.",
  }),
  columnHelper.accessor("subtotal", {
    cell: (info) => (
      <div className="max-width-cell">
        {info.row.original.summary.total_api_calls} FMV Queries
      </div>
    ),
    header: "Description",
  }),
  columnHelper.accessor("total", {
    cell: (info) => (
      <div className="max-width-cell">
        {formatCurrency(info?.getValue(), "ZMW")}
      </div>
    ),
    header: "Amount",
  }),
  columnHelper.accessor("due_date", {
    cell: (info) => (
      <div className="max-width-cell">{formatDate(info?.getValue())}</div>
    ),
    header: "Due Date",
  }),
  columnHelper.accessor("status", {
    cell: (info) => (
      <div className="max-width-cell">
        <span
          className={`rounded-full px-2 py-1 text-xs font-semibold ${
            info.getValue() === "paid"
              ? "bg-green-100 text-green-800"
              : info.getValue() === "pending"
                ? "bg-yellow-100 text-yellow-800"
                : "bg-red-100 text-red-800"
          }`}
        >
          {capitalizeFirstLetter(info?.getValue())}
        </span>
      </div>
    ),
    header: "Status",
  }),
  columnHelper.display({
    id: "actions",
    cell: (info) => {
      return (
        <div className="flex items-center space-x-3">
          <Link
            href={`billing/invoice/${info.row.original.id}`}
            title="View Invoice"
          >
            <FontAwesomeIcon
              icon={faFileInvoiceDollar}
              className="text-sm text-blue-500 hover:text-blue-700"
            />
          </Link>
          {info.row.original.receipts.length > 0 && (
            <Link
              href={`billing/invoice/${info.row.original.id}/receipt/${info.row.original.receipts[0].id}`}
              title="View Receipt"
            >
              <FontAwesomeIcon
                icon={faReceipt}
                className="text-sm text-blue-500 hover:text-blue-700"
              />
            </Link>
          )}
          <PDFDownloadLink
            document={<InvoicePDF invoiceId={info.row.original.id} />}
            fileName={`invoice-${info.row.original.id}.pdf`}
            className="text-sm text-black hover:opacity-90"
            title="Download Invoice"
          >
            <FontAwesomeIcon icon={faFilePdf} />
          </PDFDownloadLink>
          {info.row.original.receipts.length > 0 && (
            <PDFDownloadLink
              document={<ReceiptPDF invoiceId={info.row.original.id} />}
              fileName={`receipt-${info.row.original.receipts[0].id}.pdf`}
              className="text-sm text-wbv-theme hover:opacity-90"
              title="Download Receipt"
            >
              <FontAwesomeIcon icon={faFilePdf} />
            </PDFDownloadLink>
          )}
          {info.row.original.status !== "paid" && (
            <button
              onClick={() => openPaymentModal(info.row.original.id)}
              className="text-sm text-wbv-theme hover:opacity-90"
              title="Pay"
            >
              <FontAwesomeIcon icon={faWallet} />
            </button>
          )}
          {user?.role_name === "super admin" && (
            <button
              onClick={() => openCancellationModal(info.row.original.id)}
              className="text-sm text-red-500  hover:opacity-90"
              title="Cancel"
            >
              <FontAwesomeIcon icon={faXmark} />
            </button>
          )}
        </div>
      );
    },
    header: "Actions",
  }),
];
