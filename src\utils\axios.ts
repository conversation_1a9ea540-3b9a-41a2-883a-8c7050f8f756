import axios from "axios";

export const v2EngineAxiosInstance = axios.create({
  baseURL: process.env.WHITEBOOK_API_URL_V2,
  headers: {
    "X-WHITEBOOK-API-Key": process.env.WHITEBOOK_ADMIN_API_KEY,
  },
});

export const engineAxiosInstance = axios.create({
  baseURL: process.env.WHITEBOOK_API_URL,
  headers: {
    "X-WHITEBOOK-API-Key": process.env.WHITEBOOK_ADMIN_API_KEY,
    "Content-Type": "multipart/form-data",
  },
});

export interface APIErrorResponse {
  response?: {
    status?: number;
    message?: string;
    data?: {
      error?: string;
      errors?:string;
      [key: string]: unknown;
    };
  };
  [key: string]: unknown;
}