import React from 'react';

interface Role {
    id: number;
    name: string;
    label: string;
    description: string;
    permissions: Array<{
        id: number;
        name: string;
        description: string;
        created_at: string;
        updated_at: string;
        deleted_at: string | null;
    }>;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
}

interface OrganizationAddress {
    street: string;
    city: string;
    state: string;
    zip_code: string;
    country: string;
}

interface OnboardingSummaryProps {
    companyName: string;
    tpin: string;
    phoneNumber: string;
    organizationEmail: string;
    address: OrganizationAddress;
    businessTypeName: string;
    adminUsers: { adminUserFirstName: string; adminUserLastName: string; adminUserEmail: string }[];
    registeredUsers: { registeredUserFirstName: string; registeredUserLastName: string; registeredUserEmail: string; registeredUserRoleId: number }[];
    incorporationDocuments: File[];
    handlePrevStep: () => void;
    handleSubmit: () => void;
    roles: Role[];
}

const OnboardingSummary: React.FC<OnboardingSummaryProps> = ({
    companyName,
    tpin,
    phoneNumber,
    organizationEmail,
    address,
    businessTypeName,
    adminUsers,
    registeredUsers,
    incorporationDocuments,
    handlePrevStep,
    handleSubmit,
    roles,
}) => {
    return (
        <div className="mb-6">
            <div className="p-6 bg-white rounded-lg shadow-md">
                <h2 className="text-2xl font-bold text-center mb-6">Summary</h2>
                <div className="space-y-6">
                    <div className="p-4 border rounded-lg">
                        <h3 className="text-lg font-semibold mb-2">Organization Details</h3>
                        <table className="w-full text-sm text-left text-gray-700">
                            <tbody>
                                <tr>
                                    <td className="font-medium">Company Name:</td>
                                    <td>{companyName}</td>
                                </tr>
                                <tr>
                                    <td className="font-medium">TPIN:</td>
                                    <td>{tpin}</td>
                                </tr>
                                <tr>
                                    <td className="font-medium">Phone Number:</td>
                                    <td>{phoneNumber}</td>
                                </tr>
                                <tr>
                                    <td className="font-medium">Email:</td>
                                    <td>{organizationEmail}</td>
                                </tr>
                                <tr>
                                    <td className="font-medium">Country:</td>
                                    <td>{address.country}</td>
                                </tr>
                                <tr>
                                    <td className="font-medium">State:</td>
                                    <td>{address.state}</td>
                                </tr>
                                <tr>
                                    <td className="font-medium">City:</td>
                                    <td>{address.city}</td>
                                </tr>
                                <tr>
                                    <td className="font-medium">Street:</td>
                                    <td>{address.street}</td>
                                </tr>
                                <tr>
                                    <td className="font-medium">Zip Code:</td>
                                    <td>{address.zip_code}</td>
                                </tr>
                                <tr>
                                    <td className="font-medium">Business Type:</td>
                                    <td>{businessTypeName}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div className="p-4 border rounded-lg">
                        <h3 className="text-lg font-semibold mb-2">Admin User</h3>
                        {adminUsers.length > 0 ? (
                            <ul className="list-disc list-inside">
                                {adminUsers.map((user, index) => (
                                    <li key={index} className="text-sm text-gray-700">
                                        <strong>{user.adminUserFirstName} {user.adminUserLastName}</strong> - {user.adminUserEmail}
                                    </li>
                                ))}
                            </ul>
                        ) : (
                            <p className="text-sm text-gray-700">No admin users added.</p>
                        )}
                    </div>
                    <div className="p-4 border rounded-lg">
                        <h3 className="text-lg font-semibold mb-2">Registered Users</h3>
                        {registeredUsers.length > 0 ? (
                            <ul className="list-disc list-inside">
                                {registeredUsers.map((user, index) => {
                                    const roleName = roles.find(role => role.id === user.registeredUserRoleId)?.name || "Unknown Role";
                                    return (
                                        <li key={index} className="text-sm text-gray-700">
                                            <strong>{user.registeredUserFirstName} {user.registeredUserLastName}</strong> - {user.registeredUserEmail} <span className="capitalize">({roleName})</span>
                                        </li>
                                    );
                                })}
                            </ul>
                        ) : (
                            <p className="text-sm text-gray-700">No registered users added.</p>
                        )}
                    </div>
                    <div className="p-4 border rounded-lg">
                        <h3 className="text-lg font-semibold mb-2">Incorporation Documents</h3>
                        {incorporationDocuments.length > 0 ? (
                            <ul className="list-disc list-inside">
                                {incorporationDocuments.map((file, index) => (
                                    <li key={index} className="text-sm text-gray-700">
                                        <strong>Document {index + 1}:</strong> {file.name}
                                    </li>
                                ))}
                            </ul>
                        ) : (
                            <p className="text-sm text-gray-700">No incorporation documents added.</p>
                        )}
                    </div>
                </div>
            </div>
            <div className="mt-6 flex justify-between">
                <button
                    className="rounded-md bg-gray-200 px-4 py-2 font-semibold text-gray-700 transition duration-300 hover:bg-gray-300"
                    onClick={handlePrevStep}
                >
                    Previous
                </button>
                <button
                    className="rounded-md bg-wbv-theme px-4 py-2 font-semibold text-white transition duration-300 hover:bg-opacity-90"
                    onClick={handleSubmit}
                >
                    Submit
                </button>
            </div>
        </div>
    );
};

export default OnboardingSummary;