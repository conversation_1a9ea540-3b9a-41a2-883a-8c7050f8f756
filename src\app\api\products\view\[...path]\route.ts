import { NextResponse } from 'next/server';
import { Client as MinioClient } from 'minio';

const minioClient = new MinioClient({
    endPoint: process.env.S3_ENDPOINT!,
    port: Number(process.env.S3_PORT) || 443,
    useSSL: process.env.S3_USE_SSL === 'true',
    accessKey: process.env.S3_ACCESS_KEY!,
    secretKey: process.env.S3_SECRET_KEY!,
});

export async function GET(
    request: Request,
    { params }: { params: { path: string[] } }
) {
    try {
        // Join the path segments
        const imagePath = params.path.join('/');
        
        console.log('Received image request:', {
            path: params.path,
            joinedPath: imagePath,
            bucket: process.env.S3_BUCKET
        });

        // Generate presigned URL
        const presignedUrl = await minioClient.presignedGetObject(
            process.env.S3_BUCKET!,
            imagePath,
            24 * 60 * 60 // 24 hours expiry
        );

        console.log('Generated presigned URL:', presignedUrl);
        
        return NextResponse.redirect(presignedUrl);
    } catch (error) {
        console.error('Error handling image request:', error);
        return NextResponse.json({ error: 'Failed to process image request' }, { status: 500 });
    }
}