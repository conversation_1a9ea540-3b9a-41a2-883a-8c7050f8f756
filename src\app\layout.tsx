"use client";
import "jsvectormap/dist/jsvectormap.css";
import "flatpickr/dist/flatpickr.min.css";
import "@/css/satoshi.css";
import "@/css/style.css";
import "react-toastify/dist/ReactToastify.css";
import "react-phone-input-2/lib/style.css";
import React, { useEffect, useState } from "react";
import Loader from "@/components/common/Loader";
import { SessionProvider } from "next-auth/react";
import { ToastContainer, toast } from "react-toastify";
import VoiceFlowChat from "@/components/VoiceFlowChat";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [loading, setLoading] = useState<boolean>(true);
  const [serverError, setServerError] = useState<boolean>(false);

  useEffect(() => {
    const checkServerStatus = async () => {
      try {
        const response = await fetch("/api/checkServerStatus");
        const result = await response.json();
        if (result.status !== "ok") {
          throw new Error(result.message);
        }
        setServerError(false);
      } catch (error) {
        setServerError(true);
        toast.error("Server is down. Please try again later.", {
          position: "bottom-right",
        });
      }
    };
    setTimeout(() => {
      setLoading(false);
      checkServerStatus();
    }, 1000);
  }, []);

  if (serverError) {
    return (
      <html lang="en">
        <body suppressHydrationWarning={true}>
          <SessionProvider>
            <div className="dark:bg-boxdark-2 dark:text-bodydark">
              {loading ? (
                <Loader />
              ) : (
                <div className="flex min-h-screen items-center justify-center dark:bg-boxdark-2 dark:text-bodydark">
                  <div className="text-center">
                    <h1 className="text-2xl font-bold">Server Error</h1>
                    <p className="mt-4">
                      The server is currently down. Please try again later.
                    </p>
                  </div>
                  <ToastContainer />
                </div>
              )}
            </div>
          </SessionProvider>
        </body>
      </html>
    );
  }

  return (
    <html lang="en">
      <body suppressHydrationWarning={true}>
        <SessionProvider>
          <div className="dark:bg-boxdark-2 dark:text-bodydark">
            {loading ? <Loader /> : <>{children}</>}
            <VoiceFlowChat />
            <ToastContainer position="bottom-right" />
          </div>
        </SessionProvider>
      </body>
    </html>
  );
}