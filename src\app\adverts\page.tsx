"use client";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import React, { useState } from "react";

interface Advert {
  id: number;
  ad_type: string;
  starting: string; // ISO date string
  days: number;
  image: string;
  alt_text: string;
  url_to: string;
  created_by: number;
  advertiser_type?: string;
  advertiser_id?: number;
  views: number;
  clicks: number;
}

const AdvertsPage: React.FC = () => {
  const [adverts, setAdverts] = useState<Advert[]>([]);
  const [newAdvert, setNewAdvert] = useState<Partial<Advert>>({
    ad_type: "",
    starting: "",
    days: 0,
    image: "",
    alt_text: "",
    url_to: "",
    created_by: 0,
    advertiser_type: "",
    advertiser_id: 0,
  });
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  // Example enum options
  const adTypes = ["Banner", "Video", "Popup"];
  const advertiserTypes = ["Internal", "External"];

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >,
  ) => {
    const { name, value } = e.target;
    setNewAdvert((prev) => ({
      ...prev,
      [name]:
        name === "days" || name === "created_by" || name === "advertiser_id"
          ? Number(value)
          : value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const newAdvertWithId: Advert = {
      ...newAdvert,
      id: adverts.length + 1,
      views: 0,
      clicks: 0,
    } as Advert;
    setAdverts((prev) => [...prev, newAdvertWithId]);
    setNewAdvert({
      ad_type: "",
      starting: "",
      days: 0,
      image: "",
      alt_text: "",
      url_to: "",
      created_by: 0,
      advertiser_type: "",
      advertiser_id: 0,
    });
    setIsModalOpen(false); // Close the modal after adding the advert
  };

  return (
    <DefaultLayout>
      <Breadcrumb pageName="Adverts" />
      <div className="container mx-auto p-4">
        {/* <h1 className="text-2xl font-bold mb-4">Adverts</h1> */}
        <button
          onClick={() => setIsModalOpen(true)}
          className="mb-4 rounded bg-wbv-theme p-2 text-white"
        >
          Add Advert
        </button>

        {/* Modal */}
        {isModalOpen && (
          <div
            className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50"
            role="dialog"
            aria-modal="true"
            onClick={() => setIsModalOpen(false)}
          >
            <div
              className="w-full max-w-xs rounded-lg bg-white p-4 shadow-lg"
              onClick={(e) => e.stopPropagation()}
            >
              <h2 className="mb-2 text-lg font-bold">Add Advert</h2>
              <form onSubmit={handleSubmit}>
                <div className="grid grid-cols-1 gap-2">
                  {/* Ad Type */}
                  <label className="block">
                    <span className="text-sm text-gray-700">Ad Type</span>
                    <select
                      name="ad_type"
                      value={newAdvert.ad_type}
                      onChange={handleChange}
                      className="mt-1 block w-full rounded border p-1 text-sm"
                      required
                    >
                      <option value="" disabled>
                        Select Ad Type
                      </option>
                      {adTypes.map((type) => (
                        <option key={type} value={type}>
                          {type}
                        </option>
                      ))}
                    </select>
                  </label>

                  {/* Starting Date */}
                  <label className="block">
                    <span className="text-sm text-gray-700">Starting Date</span>
                    <input
                      type="date"
                      name="starting"
                      value={newAdvert.starting}
                      onChange={handleChange}
                      className="mt-1 block w-full rounded border p-1 text-sm"
                      required
                    />
                  </label>

                  {/* Days */}
                  <label className="block">
                    <span className="text-sm text-gray-700">Days</span>
                    <input
                      type="number"
                      name="days"
                      value={newAdvert.days}
                      onChange={handleChange}
                      className="mt-1 block w-full rounded border p-1 text-sm"
                      min={1}
                      required
                    />
                  </label>

                  {/* Image URL */}
                  <label className="block">
                    <span className="text-sm text-gray-700">Image URL</span>
                    <input
                      type="text"
                      name="image"
                      value={newAdvert.image}
                      onChange={handleChange}
                      placeholder="https://example.com/image.jpg"
                      className="mt-1 block w-full rounded border p-1 text-sm"
                      required
                    />
                  </label>

                  {/* Alt Text */}
                  <label className="block">
                    <span className="text-sm text-gray-700">Alt Text</span>
                    <input
                      type="text"
                      name="alt_text"
                      value={newAdvert.alt_text}
                      onChange={handleChange}
                      placeholder="Description of the image"
                      className="mt-1 block w-full rounded border p-1 text-sm"
                      required
                    />
                  </label>

                  {/* URL To */}
                  <label className="block">
                    <span className="text-sm text-gray-700">URL To</span>
                    <input
                      type="url"
                      name="url_to"
                      value={newAdvert.url_to}
                      onChange={handleChange}
                      placeholder="https://example.com"
                      className="mt-1 block w-full rounded border p-1 text-sm"
                      required
                    />
                  </label>
                </div>
                <div className="mt-3 flex justify-end">
                  <button
                    type="button"
                    onClick={() => setIsModalOpen(false)}
                    className="mr-2 rounded bg-gray-500 px-2 py-1 text-sm text-white"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="rounded bg-wbv-theme px-2 py-1 text-sm text-white"
                  >
                    Add Advert
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
        {/* Adverts Table */}
        <table className="mt-4 min-w-full bg-white">
          <thead>
            <tr>
              <th className="border py-2">ID</th>
              <th className="border py-2">Ad Type</th>
              <th className="border py-2">Starting Date</th>
              <th className="border py-2">Days</th>
              <th className="border py-2">Image</th>
              <th className="border py-2">Alt Text</th>
              <th className="border py-2">URL To</th>
              {/* <th className="py-2 border">Created By</th> */}
              {/* <th className="py-2 border">Advertiser Type</th> */}
              {/* <th className="py-2 border">Advertiser ID</th> */}
              {/* <th className="py-2 border">Views</th>
              <th className="py-2 border">Clicks</th> */}
            </tr>
          </thead>
          <tbody>
            {adverts.map((advert) => (
              <tr key={advert.id}>
                <td className="border px-4 py-2">{advert.id}</td>
                <td className="border px-4 py-2">{advert.ad_type}</td>
                <td className="border px-4 py-2">{advert.starting}</td>
                <td className="border px-4 py-2">{advert.days}</td>
                <td className="border px-4 py-2">
                  <a
                    href={advert.image}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500"
                  >
                    View Image
                  </a>
                </td>
                <td className="border px-4 py-2">{advert.alt_text}</td>
                <td className="border px-4 py-2">
                  <a
                    href={advert.url_to}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500"
                  >
                    Visit Site
                  </a>
                </td>
                {/* <td className="border px-4 py-2">{advert.created_by}</td>
                <td className="border px-4 py-2">{advert.advertiser_type || "N/A"}</td>
                <td className="border px-4 py-2">{advert.advertiser_id || "N/A"}</td>
                <td className="border px-4 py-2">{advert.views}</td>
                <td className="border px-4 py-2">{advert.clicks}</td> */}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </DefaultLayout>
  );
};

export default AdvertsPage;
