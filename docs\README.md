# whitebook-value-admin

WhitebookValue system admin portal site

## Introduction

This repository is home to the WhitebookValue admin portal site. It is used to manage and administer the White Book Value (WBV) system.

The site is NextJS based and has both production and staging instances which can be built by pushing to the main and staging branches respectively. The system is built with

## Setup

To set up the project, reference the [setup instructions](./setup.md).

## Testing

Once setup is complete, and you have saved any and all changes, run the following command to build and test your changes:

```sh
npm run build
```

```sh
npm run test
```

This will expose the app on `localhost:3000`
