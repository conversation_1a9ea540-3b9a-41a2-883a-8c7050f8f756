"use client";

import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import OnboardingRequest from "@/components/Onboarding/request/OnboardingRequest";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import { ToastContainer, toast } from "react-toastify";

interface AddressStruct {
    id: number;
    name: string;
    street: string;
    city: string;
    state: string;
    zip_code: string;
    country: string;
    email: string;
    phone: string;
    organization_id: number;
}

interface DocumentStruct {
    id: number;
    name: string;
    type: string;
    file: string;
    path: string;
    organization_id: number;
    created_at: string;
    created_by: number;
    updated_at: string;
    updated_by: number;
    deleted_at: string | null;
    deleted_by: number;
}

interface OnboardingRequestStruct {
    id: number;
    name: string;
    registration_number: string;
    tax_id: string;
    organization_type_id: number;
    parent_id: number;
    email: string;
    phone_number: string;
    status: string;
    address: AddressStruct;
    documents: DocumentStruct[];
    created_by: number;
    updated_by: number;
    deleted_by: number;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
}

const OnboardingRequestPage: React.FC = () => {
    const params = useParams();
    const requestId = Array.isArray(params.id)
        ? parseInt(params.id[0] || "0", 10)
        : parseInt(params.id || "0", 10);
    const [request, setRequest] = useState<OnboardingRequestStruct | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    const showSuccessToast = (message: string) => {
        toast.success(message, {
            position: "bottom-right",
        });
    };

    const showErrorToast = (message: string) => {
        toast.error(message, {
            position: "bottom-right",
        });
    };

    useEffect(() => {
        const fetchOrganizationInfo = async () => {
            try {
                if (requestId) {
                    const response = await fetch(`/api/organizations/${requestId}`, {
                        method: "GET",
                    });
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    const result = await response.json();
                    setRequest(result?.data);
                }
            } catch (err) {
                setError("Failed to fetch organization information");
                console.error("Error fetching single organization:", err);
            } finally {
                setLoading(false);
            }
        };

        fetchOrganizationInfo();
    }, [requestId]);


    const updateStatus = async (status: string) => {
        try {
            console.log("updateStatus rx status", status);
            const response = await fetch(`/api/onboard/${requestId}`, {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ status }), // Send status in the body
            });
            if (!response.ok) {
                showErrorToast("An error occurred. Please try again.");
            } else {
                showSuccessToast("Onboarding request status updated successfully.");
                const result = await response.json();
                setRequest(result?.data);
            }
        } catch (err) {
            setError(`Failed to update status to ${status}`);
            console.error(`Error updating status to ${status}:`, err);
            showErrorToast(`Failed to update status to ${status}`);
        }
    };

    const handleApprove = async () => {
        console.log("approve");
        await updateStatus("approved");
    };

    const handleReject = async () => {
        console.log("rejected");

        await updateStatus("rejected");
    };

    return (
        <DefaultLayout>
            <OnboardingRequest
                error={error}
                loading={loading}
                request={request}
                handleApprove={handleApprove}
                handleReject={handleReject}
                showSuccessToast={showSuccessToast}
                showErrorToast={showErrorToast}
            />
            <ToastContainer />
        </DefaultLayout>
    );
};
export default OnboardingRequestPage;