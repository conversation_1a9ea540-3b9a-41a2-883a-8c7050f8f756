import { NextRequest, NextResponse } from "next/server";
import { v2EngineAxiosInstance } from "@/utils/axios";

interface Shop {
  id: number;
  name: string;
  description?: string;
  organization_id: number;
  user_id: number;
}

interface ErrorResponse {
  message?: string;
  error?: string;
  errors?: unknown[];
}

export async function GET(req: NextRequest) {
  try {
    console.log("Fetching shops from /api/v2/shops");
    const response = await v2EngineAxiosInstance.get<{ data: Shop[] }>("/shops");

    if (response.data && Array.isArray(response.data.data)) {
      console.log(`Successfully fetched ${response.data.data.length} shops.`);
      return NextResponse.json(response.data.data);
    } else {
      console.error("Unexpected shop data structure from V2 engine:", response.data);
      return NextResponse.json(
        { error: "Failed to fetch shops due to unexpected format." },
        { status: 500 }
      );
    }
  } catch (err: unknown) {
    const error = err as { response?: { status?: number; data?: ErrorResponse }; message?: string };
    console.error(
      "Error fetching shops via V2 engine:",
      error.response?.status,
      error.response?.data || error.message || error
    );

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.response?.data?.error ||
      "Error fetching shops";

    return NextResponse.json({ error: message }, { status });
  }
}

export async function POST(req: NextRequest) {
  let requestBody: Partial<Shop> = {};
  try {
    requestBody = await req.json() as Partial<Shop>;

    const response = await v2EngineAxiosInstance.post<{ data: Shop }>("/shops", requestBody, {
      headers: {
        "Content-Type": "application/json",
      },
    });

    const shopData = response.data?.data;

    if (!shopData || !shopData.id) {
      console.warn("V2 engine response does not include a shop ID.");
      throw new Error("API response does not include a valid shop ID.");
    }

    return NextResponse.json(shopData, { status: 201 });
  } catch (err: unknown) {
    const error = err as { 
      response?: { status?: number; data?: ErrorResponse }; 
      message?: string 
    };

    console.error("--- Error During V2 Engine Request ---");
    console.error("Request Body Sent:", JSON.stringify(requestBody, null, 2));

    if (error.response) {
      console.error("V2 Engine Error Response:", {
        status: error.response.status,
        data: error.response.data,
      });
    } else {
      console.error("V2 Engine Error:", error.message || error);
    }
    console.error("-----------------------------------");

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.response?.data?.error ||
      (status === 400 ? "Bad Request to V2 engine" : "Error creating shop");
    const details = error.response?.data?.errors;

    return NextResponse.json(
      { error: message, details },
      { status }
    );
  }
}
