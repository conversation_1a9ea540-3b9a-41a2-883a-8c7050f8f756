import { NextRequest, NextResponse } from "next/server";
import { v2EngineAxiosInstance } from "@/utils/axios";

export async function GET(
    req: NextRequest,
    { params }: { params: { id: string } },
) {
    const { id } = params;

    try {
        const response = await v2EngineAxiosInstance.get(`/organizations/${Number(id)}/staff`);

        const organizationUsers = response.data;
        return NextResponse.json(organizationUsers);
    } catch (error) {
        console.error("Error fetching organization users:", error);
        return NextResponse.json(
            { error: "Error fetching organization users" },
            { status: 500 },
        );
    }
}

export async function POST(
    req: NextRequest,
    { params }: { params: { id: string } },
) {
    const { id } = params;
    const userData = await req.json();

    // Create the exact JSON structure expected by the backend
    const payload = {
        email: userData.email,
        title: userData.title,
        first_name: userData.first_name,
        last_name: userData.last_name,
        role_id: Number(userData.role_id)
    };

    console.log("Raw JSON payload for backend:", payload);

    try {
        // Set headers to ensure we're sending application/json
        const response = await v2EngineAxiosInstance.post(
            `/organizations/${Number(id)}/users`,
            payload,
            {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );
        return NextResponse.json(response.data);
    } catch (error) {
        console.error("Error adding organization user:", error);
        return NextResponse.json(
            { error: "Error adding organization user" },
            { status: 500 },
        );
    }
}