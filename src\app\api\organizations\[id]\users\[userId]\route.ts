import { NextRequest, NextResponse } from "next/server";
import { v2EngineAxiosInstance } from "@/utils/axios";

export async function PUT(
    req: NextRequest,
    { params }: { params: { id: string; userId: string } },
) {
    const { id, userId } = params;
    const userData = await req.json();
    const { ...userDetails } = userData;

    // Create the exact JSON structure expected by the backend
    const payload = {
        email: userDetails.email,
        title: userDetails.title,
        first_name: userDetails.first_name,
        last_name: userDetails.last_name,
        role_id: Number(userDetails.role_id)
    };

    try {
        // Set headers to ensure we're sending application/json
        const response = await v2EngineAxiosInstance.put(
            `/organizations/${Number(id)}/users/${userId}`,
            payload,
            {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );
        return NextResponse.json(response.data);
    } catch (error) {
        console.error("Error updating organization user:", error);
        return NextResponse.json(
            { error: "Error updating organization user" },
            { status: 500 },
        );
    }
}

export async function DELETE(
    req: NextRequest,
    { params }: { params: { id: string; userId: string } },
) {
    const { id, userId } = params;
    console.log("Received DELETE request for organization:", id, "with user ID:", userId);

    try {
        const response = await v2EngineAxiosInstance.delete(`/organizations/${Number(id)}/users/${Number(userId)}`);
        return NextResponse.json(response.data);
    } catch (error) {
        console.error("Error deleting organization user:", error);
        return NextResponse.json(
            { error: "Error deleting organization user" },
            { status: 500 },
        );
    }
}