import Link from 'next/link';
import React, { useState, useEffect, forwardRef } from 'react';
import { useParams } from "next/navigation";
import StaffMembersModal from '@/components/Modals/StaffMembersModal';
import ConfirmModal from '@/components/Modals/ConfirmModal';
import { formatDateTime, capitalizeFirstLetter } from '@/components/utils/utils';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDownload, faEye, faPencilAlt, faPlus, faTrash, faUserPlus, faUserEdit, } from '@fortawesome/free-solid-svg-icons';
import DocumentUploadModal from '@/components/Modals/DocumentUploadModal';

interface AddressStruct {
    id: number;
    name: string;
    street: string;
    city: string;
    state: string;
    zip_code: string;
    country: string;
    email: string;
    phone: string;
    organization_id: number;
}

interface DocumentStruct {
    id: number;
    name: string;
    type: string;
    file: string;
    path: string;
    organization_id: number;
    created_at: string;
    created_by: number;
    updated_at: string;
    updated_by: number;
    deleted_at: string | null;
    deleted_by: number;
}

interface OnboardingRequestStruct {
    id: number;
    name: string;
    registration_number: string;
    tax_id: string;
    organization_type_id: number;
    parent_id: number;
    email: string;
    phone_number: string;
    status: string;
    address: AddressStruct;
    documents: DocumentStruct[];
    created_by: number;
    updated_by: number;
    deleted_by: number;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
}

interface OnboardingRequestProps {
    request: OnboardingRequestStruct | null;
    handleApprove: () => void;
    handleReject: () => void;
    loading: boolean;
    error: string | null;
    showSuccessToast: (message: string) => void;
    showErrorToast: (message: string) => void;
}

interface UserFormData {
    title: string;
    firstName: string;
    lastName: string;
    email: string;
    roleId: number;
}

const OnboardingRequest = forwardRef<HTMLDivElement, OnboardingRequestProps>(
    ({ request, handleApprove, handleReject, loading, error, showSuccessToast, showErrorToast }, ref) => {
        const [isConfirmApproveModalOpen, setIsConfirmApproveModalOpen] = useState<boolean>(false);
        const [isConfirmRejectModalOpen, setIsConfirmRejectModalOpen] = useState<boolean>(false);
        const [isConfirmDeleteModalOpen, setIsConfirmDeleteModalOpen] = useState<boolean>(false);
        const [userToDelete, setUserToDelete] = useState<number | null>(null);
        const [activeTab, setActiveTab] = useState<'certificate' | 'admin_users' | 'registered_users'>('certificate');
        const [registeredUsers, setRegisteredUsers] = useState<any[]>([]);
        const [adminUsers, setAdminUsers] = useState<any[]>([]);
        const [isUserModalOpen, setIsUserModalOpen] = useState<boolean>(false);
        const [currentUser, setCurrentUser] = useState<any | null>(null);
        const params = useParams();
        const organizationId = Array.isArray(params.id)
            ? parseInt(params.id[0] || "0", 10)
            : parseInt(params.id || "0", 10);
        const [userError, setUserError] = useState<string | null>(null);

        useEffect(() => {
            // Fetch registered users from server
            const fetchRegisteredUsers = async () => {
                try {
                    const response = await fetch(`/api/organizations/${organizationId}/users`);
                    const data = await response.json();
                    setRegisteredUsers(data.data || []);
                    setUserError(null);
                } catch (error) {
                    console.error('Error fetching registered users:', error);
                    setRegisteredUsers([]);
                    setUserError("Failed to load registered users. Please try again later.");
                }
            };

            // Fetch admin users from server
            const fetchAdminUsers = async () => {
                try {
                    const response = await fetch(`/api/organizations/${organizationId}/admins`);
                    const data = await response.json();
                    setAdminUsers(data.data || []);
                } catch (error) {
                    console.error('Error fetching admin users:', error);
                    setAdminUsers([]);
                }
            };

            fetchRegisteredUsers();
            fetchAdminUsers();
        }, [organizationId]);

        const handleAddUser = () => {
            setCurrentUser(null);
            setIsUserModalOpen(true);
        };

        const handleUpdateUser = (user: any) => {
            setCurrentUser(user);
            setIsUserModalOpen(true);
        };

        const handleUserFormSubmit = async (userData: UserFormData) => {
            try {
                if (currentUser) {
                    // Update user logic
                    const response = await fetch(`/api/organizations/${organizationId}/users/${currentUser.id}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            userId: currentUser.id,
                            title: userData.title,
                            first_name: userData.firstName,
                            last_name: userData.lastName,
                            email: userData.email,
                            role_id: userData.roleId
                        }),
                    });


                    if (!response.ok) {
                        throw new Error('Failed to update user');
                    }

                    // Refresh the users list after update
                    const updatedUserData = await response.json();
                    setRegisteredUsers(prevUsers =>
                        prevUsers.map(user => user.id === currentUser.id ? updatedUserData.data : user)
                    );
                    showSuccessToast(`User ${userData.firstName} ${userData.lastName} updated successfully`);
                } else {
                    const response = await fetch(`/api/organizations/${organizationId}/users`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            email: userData.email,
                            title: userData.title,
                            first_name: userData.firstName,
                            last_name: userData.lastName,
                            role_id: userData.roleId
                        }),
                    });

                    if (!response.ok) {
                        throw new Error('Failed to add user');
                    }

                    // Refresh the users list after adding
                    const newUserData = await response.json();
                    setRegisteredUsers(prevUsers => [...prevUsers, newUserData.data]);
                    showSuccessToast(`User ${userData.firstName} ${userData.lastName} added successfully`);
                }
            } catch (error) {
                console.error('Error handling user submission:', error);
                showErrorToast(error instanceof Error ? error.message : 'Failed to save user');
            }

            setIsUserModalOpen(false);
        };


        const handleDeleteUser = (userId: number) => {
            setUserToDelete(userId);
            setIsConfirmDeleteModalOpen(true);
        };

        const confirmDeleteUser = async () => {
            if (userToDelete) {
                try {
                    const response = await fetch(`/api/organizations/${organizationId}/users/${userToDelete}`, {
                        method: 'DELETE',
                    });

                    if (!response.ok) {
                        throw new Error('Failed to delete user');
                    }

                    // Find the user before removing from the list (for the toast message)
                    const userToBeDeleted = registeredUsers.find(user => user.id === userToDelete);

                    // Remove the user from the list
                    setRegisteredUsers(prevUsers => prevUsers.filter(user => user.id !== userToDelete));

                    if (userToBeDeleted) {
                        showSuccessToast(`User ${userToBeDeleted.first_name} ${userToBeDeleted.last_name} deleted successfully`);
                    } else {
                        showSuccessToast('User deleted successfully');
                    }
                } catch (error) {
                    console.error('Error deleting user:', error);
                    showErrorToast(error instanceof Error ? error.message : 'Failed to delete user');
                }
            }
            setIsConfirmDeleteModalOpen(false);
        };

        const [isDocumentModalOpen, setIsDocumentModalOpen] = useState(false);
        const [currentDocument, setCurrentDocument] = useState<DocumentStruct | null>(null);
        const [documentToDelete, setDocumentToDelete] = useState<number | null>(null);
        const [documents, setDocuments] = useState<DocumentStruct[]>([]);
        const [documentLoading, setDocumentLoading] = useState(false);
        const [documentError, setDocumentError] = useState<string | null>(null);

        useEffect(() => {
            if (request?.documents) {
                setDocuments(request.documents);
            }
        }, [request]);


        const confirmApprove = () => {
            setIsConfirmApproveModalOpen(false);
            handleApprove();
        };

        const confirmReject = () => {
            setIsConfirmRejectModalOpen(false);
            handleReject();
        };
        const getDocumentDisplayName = (doc: DocumentStruct) => {
            if (!doc.path) return doc.name || 'Unnamed Document';
            const pathParts = doc.path.split('/');
            return pathParts[pathParts.length - 1];
        };

        const handleViewDocument = async (document: DocumentStruct) => {
            try {
                const response = await fetch(`/api/organizations/${organizationId}/documents/${document.id}/view`);
                if (!response.ok) {
                    throw new Error('Failed to get document view URL');
                }
                const data = await response.json();
                window.open(data.url, '_blank');
            } catch (error) {
                console.error('Error viewing document:', error);
                showErrorToast?.('Failed to view document');
            }
        };

        const handleDownloadDocument = async (document: DocumentStruct) => {
            try {
                const response = await fetch(`/api/organizations/${organizationId}/documents/${document.id}/download`);
                if (!response.ok) {
                    throw new Error('Failed to get document download URL');
                }
                const data = await response.json();

                // Create a temporary link and trigger download
                const link = window.document.createElement('a');
                link.href = data.url;
                link.setAttribute('download', getDocumentDisplayName(document));
                window.document.body.appendChild(link);
                link.click();
                window.document.body.removeChild(link);
            } catch (error) {
                console.error('Error downloading document:', error);
                showErrorToast?.('Failed to download document');
            }
        };

        const handleEditDocument = (document: DocumentStruct) => {
            setCurrentDocument(document);
            setIsDocumentModalOpen(true);
        };

        const handleDeleteDocument = (documentId: number) => {
            setDocumentToDelete(documentId);
            setIsConfirmDeleteModalOpen(true);
        };

        const confirmDeleteDocument = async () => {
            if (documentToDelete && organizationId) {
                try {
                    setDocumentLoading(true);
                    const response = await fetch(`/api/organizations/${organizationId}/documents/${documentToDelete}`, {
                        method: 'DELETE',
                    });

                    if (!response.ok) {
                        throw new Error('Failed to delete document');
                    }

                    setDocuments(prevDocuments =>
                        prevDocuments.filter(doc => doc.id !== documentToDelete)
                    );

                    showSuccessToast?.('Document deleted successfully');
                } catch (error) {
                    console.error('Error deleting document:', error);
                    showErrorToast?.('Failed to delete document');
                } finally {
                    setDocumentLoading(false);
                    setIsConfirmDeleteModalOpen(false);
                    setDocumentToDelete(null);
                }
            }
        };

        const handleAddDocument = () => {
            setCurrentDocument(null);
            setIsDocumentModalOpen(true);
        };

        const handleDocumentSubmit = async (file: File) => {
            if (!organizationId) return;

            try {
                setDocumentLoading(true);
                const formData = new FormData();
                formData.append('file', file);

                let url = `/api/organizations/${organizationId}/documents`;
                let method = 'POST';

                if (currentDocument) {
                    url = `${url}/${currentDocument.id}`;
                    method = 'PUT';
                }

                const response = await fetch(url, {
                    method,
                    body: formData,
                });

                if (!response.ok) {
                    throw new Error(currentDocument ? 'Failed to update document' : 'Failed to upload document');
                }

                const result = await response.json();

                if (currentDocument) {
                    setDocuments(prevDocs =>
                        prevDocs.map(doc =>
                            doc.id === currentDocument.id ? result?.data?.data : doc
                        )
                    );
                    showSuccessToast?.('Document updated successfully');
                } else {
                    setDocuments(prevDocs => [...prevDocs, result?.data?.data]);
                    showSuccessToast?.('Document uploaded successfully');
                }
                setIsDocumentModalOpen(false);
            } catch (error) {
                console.error('Error handling document:', error);
                showErrorToast?.(error instanceof Error ? error.message : 'Failed to process document');
            } finally {
                setDocumentLoading(false);
                setCurrentDocument(null);
            }
        };

        if (loading) return <div>Loading...</div>;
        if (error) return <div className="text-red-500">Error: {error}</div>;

        return (
            <div ref={ref} className="pb-6 pt-4">
                <div className="mb-4 flex items-center justify-between">
                    <h1 className="whitespace-nowrap text-3xl font-bold">
                        {request?.name}&nbsp;
                        <span className={`rounded-full px-2 py-1 text-xs font-semibold text-gray-100 ${request?.status === "pending" ? "bg-yellow-500" : request?.status === "approved" ? "bg-green-500" : "bg-red-500"}`}>
                            {capitalizeFirstLetter(request?.status ?? '')}
                        </span>
                    </h1>
                    <Link href="/onboarding/requests" className="text-blue-500 hover:underline">
                        Back to Onboarding
                    </Link>
                </div>
                <div className="flex flex-col gap-4 md:flex-row">
                    <div className="flex-1 bg-white p-4 shadow-md rounded-md">
                        <h2 className="mb-2 text-xl font-semibold">Organization Details</h2>
                        <p className="text-sm text-gray-700">
                            <strong>Registration Number:</strong> {request?.registration_number}
                        </p>
                        <p className="text-sm text-gray-700">
                            <strong>Tax ID:</strong> {request?.tax_id}
                        </p>
                        <p className="text-sm text-gray-700">
                            <strong>Email:</strong> {request?.email}
                        </p>
                        <p className="text-sm text-gray-700">
                            <strong>Phone Number:</strong> {request?.phone_number}
                        </p>
                    </div>
                    {/* Updated Address Information Card */}
                    <div className="w-full md:w-1/3 bg-white p-4 shadow-md rounded-md">
                        <h2 className="mb-2 text-xl font-semibold">Address Information</h2>
                        {request?.address ? (
                            <>
                                <p className="text-sm text-gray-700"><strong>Street:</strong> {request?.address.street}</p>
                                <p className="text-sm text-gray-700"><strong>City:</strong> {request.address.city}</p>
                                <p className="text-sm text-gray-700"><strong>State:</strong> {request.address.state}</p>
                                <p className="text-sm text-gray-700"><strong>Zip Code:</strong> {request.address.zip_code}</p>
                                <p className="text-sm text-gray-700"><strong>Country:</strong> {request.address.country}</p>
                            </>
                        ) : (
                            <p className="text-sm text-gray-700">No address available.</p>
                        )}
                    </div>
                    <div className="w-full md:w-1/3 bg-white p-4 shadow-md rounded-md">
                        <h2 className="mb-2 text-xl font-semibold">Meta Information</h2>
                        <p className="text-sm text-gray-700">
                            <strong>Date Applied:</strong> {request?.created_at ? formatDateTime(request.created_at) : "N/A"}
                        </p>
                        {request?.updated_at && (
                            <p className="text-sm text-gray-700">
                                <strong>Last Updated:</strong> {formatDateTime(request.updated_at)}
                            </p>
                        )}
                        {request?.deleted_at && (
                            <p className="text-sm text-gray-700">
                                <strong>Deleted At:</strong> {formatDateTime(request.deleted_at)}
                            </p>
                        )}
                    </div>
                </div>
                {/* New Card with Tabs */}
                <div className="mt-4 border border-gray-300 rounded-md">
                    <div className="flex w-full border-b border-gray-300">
                        <button
                            className={`w-1/3 py-2 text-center font-semibold border border-gray-400 ${activeTab === 'certificate' ? 'bg-gray-700 text-white' : 'bg-gray-200 text-gray-700'} rounded-tl-md transition duration-300`}
                            onClick={() => setActiveTab('certificate')}
                        >
                            Incorporation Documents
                        </button>
                        <button
                            className={`w-1/3 py-2 text-center font-semibold border border-gray-400 ${activeTab === 'admin_users' ? 'bg-gray-700 text-white' : 'bg-gray-200 text-gray-700'} transition duration-300`}
                            onClick={() => setActiveTab('admin_users')}
                        >
                            Admin Users
                        </button>
                        <button
                            className={`w-1/3 py-2 text-center font-semibold border border-gray-400 ${activeTab === 'registered_users' ? 'bg-gray-700 text-white' : 'bg-gray-200 text-gray-700'} rounded-tr-md transition duration-300`}
                            onClick={() => setActiveTab('registered_users')}
                        >
                            Registered Users
                        </button>
                    </div>
                    <div className="mt-4 bg-white p-4 shadow-md rounded-md">
                        {activeTab === 'certificate' && (
                            <div>
                                <div className="flex justify-end mb-4">
                                    <button
                                        onClick={handleAddDocument}
                                        className="flex items-center gap-1 rounded-md bg-gray-800 px-3 py-1 text-sm text-white hover:bg-opacity-90"
                                    >
                                        <FontAwesomeIcon icon={faPlus} />
                                        Add Document
                                    </button>
                                </div>

                                {documentLoading && <p className="text-sm text-gray-500">Loading documents...</p>}
                                {documentError && <p className="text-sm text-red-500">{documentError}</p>}

                                {documents.length > 0 ? (
                                    <div className="overflow-x-auto">
                                        <table className="min-w-full divide-y divide-gray-200">
                                            <thead className="bg-gray-50">
                                                <tr>
                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Added</th>
                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody className="bg-white divide-y divide-gray-200">
                                                {documents.map((doc) => (
                                                    <tr key={doc.id}>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{getDocumentDisplayName(doc)}</td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{capitalizeFirstLetter(doc.type)}</td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatDateTime(doc.created_at)}</td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                            <div className="inline-flex divide-x divide-gray-300 border border-gray-300 rounded-md bg-white shadow-sm">
                                                                <button
                                                                    onClick={() => handleViewDocument(doc)}
                                                                    className="px-3 py-1 text-blue-600 hover:text-blue-900 hover:bg-gray-50 rounded-l-full transition-colors duration-200"
                                                                    title="View"
                                                                >
                                                                    <FontAwesomeIcon icon={faEye} />
                                                                </button>
                                                                <button
                                                                    onClick={() => handleDownloadDocument(doc)}
                                                                    className="px-3 py-1 text-green-600 hover:text-green-900 hover:bg-gray-50 transition-colors duration-200"
                                                                    title="Download"
                                                                >
                                                                    <FontAwesomeIcon icon={faDownload} />
                                                                </button>
                                                                <button
                                                                    onClick={() => handleEditDocument(doc)}
                                                                    className="px-3 py-1 text-yellow-600 hover:text-yellow-900 hover:bg-gray-50 transition-colors duration-200"
                                                                    title="Edit"
                                                                >
                                                                    <FontAwesomeIcon icon={faPencilAlt} />
                                                                </button>
                                                                <button
                                                                    onClick={() => handleDeleteDocument(doc.id)}
                                                                    className="px-3 py-1 text-red-600 hover:text-red-900 hover:bg-gray-50 rounded-r-full transition-colors duration-200"
                                                                    title="Delete"
                                                                >
                                                                    <FontAwesomeIcon icon={faTrash} />
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                ) : (
                                    <p className="text-sm text-gray-700">No documents available.</p>
                                )}
                            </div>
                        )}
                        {activeTab === 'admin_users' && (
                            <div>
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">First Name</th>
                                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Name</th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {adminUsers && adminUsers.length > 0 ? (
                                            adminUsers.map((admin) => (
                                                <tr key={admin.id}>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{admin.id}</td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{admin.email}</td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{admin.first_name}</td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{admin.last_name}</td>
                                                </tr>
                                            ))
                                        ) : (
                                            <tr>
                                                <td colSpan={4} className="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">
                                                    No admin users found
                                                </td>
                                            </tr>
                                        )}
                                    </tbody>
                                </table>
                            </div>
                        )}

                        {activeTab === 'registered_users' && (
                            <div>
                                <div className="flex justify-end mb-4">
                                    <button
                                        onClick={handleAddUser}
                                        className="flex items-center gap-1 rounded-md bg-gray-800 px-3 py-1 text-sm text-white hover:bg-opacity-90"
                                    >
                                        <FontAwesomeIcon icon={faUserPlus} />
                                        Add User
                                    </button>
                                </div>

                                {userError && <p className="text-sm text-red-500 mb-4">{userError}</p>}

                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">First Name</th>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Name</th>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {registeredUsers && registeredUsers.length > 0 ? (
                                                registeredUsers.map((user) => (
                                                    <tr key={user.id}>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{user.id}</td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.email}</td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.first_name}</td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.last_name}</td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                            <div className="inline-flex divide-x divide-gray-300 border border-gray-300 rounded-md bg-white shadow-sm">
                                                                <button
                                                                    onClick={() => handleUpdateUser(user)}
                                                                    className="px-3 py-1 text-yellow-600 hover:text-yellow-900 hover:bg-gray-50 rounded-l-full transition-colors duration-200"
                                                                    title="Edit"
                                                                >
                                                                    <FontAwesomeIcon icon={faUserEdit} />
                                                                </button>
                                                                <button
                                                                    onClick={() => handleDeleteUser(user.id)}
                                                                    className="px-3 py-1 text-red-600 hover:text-red-900 hover:bg-gray-50 rounded-r-full transition-colors duration-200"
                                                                    title="Delete"
                                                                >
                                                                    <FontAwesomeIcon icon={faTrash} />
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                ))
                                            ) : (
                                                <tr>
                                                    <td colSpan={5} className="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">
                                                        No registered users found
                                                    </td>
                                                </tr>
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
                {/* Action Buttons */}
                <div className="mt-4 flex space-x-4">
                    {request?.status !== "approved" && (
                        <button
                            className="rounded bg-wbv-theme px-4 py-2 text-white hover:bg-opacity-90"
                            onClick={() => setIsConfirmApproveModalOpen(true)}
                        >
                            Approve
                        </button>
                    )}
                    {request?.status !== "rejected" && (
                        <button
                            className="rounded bg-red-500 px-4 py-2 text-white hover:bg-opacity-90"
                            onClick={() => setIsConfirmRejectModalOpen(true)}
                        >
                            Reject
                        </button>
                    )}
                </div>
                {/* Confirm Approve Modal */}
                <ConfirmModal
                    isOpen={isConfirmApproveModalOpen}
                    onClose={() => setIsConfirmApproveModalOpen(false)}
                    onConfirm={confirmApprove}
                    title="Confirm Approval"
                    message={`Are you sure you want to approve the onboarding request for <strong>${request?.name}</strong>?`}
                    cta="Approve"
                    ctaBtnColor="theme"
                />
                {/* Confirm Reject Modal */}
                <ConfirmModal
                    isOpen={isConfirmRejectModalOpen}
                    onClose={() => setIsConfirmRejectModalOpen(false)}
                    onConfirm={confirmReject}
                    title="Confirm Rejection"
                    message={`Are you sure you want to reject the onboarding request for <strong>${request?.name}</strong>?`}
                    cta="Reject"
                    ctaBtnColor="danger"
                />
                {/* User Modal */}
                {isUserModalOpen && (
                    <StaffMembersModal
                        isOpen={isUserModalOpen}
                        onClose={() => setIsUserModalOpen(false)}
                        title={currentUser ? "Update User" : "Add User"}
                        currentUser={currentUser}
                        onSubmit={handleUserFormSubmit}
                    />
                )}

                {/* ConfirmModal for deleting a user */}
                <ConfirmModal
                    isOpen={isConfirmDeleteModalOpen}
                    onClose={() => setIsConfirmDeleteModalOpen(false)}
                    onConfirm={confirmDeleteUser}
                    title="Confirm Deletion"
                    message={`Are you sure you want to delete this user?`}
                    cta="Delete"
                    ctaBtnColor="danger"
                />
                {/* Document Upload Modal */}
                <DocumentUploadModal
                    isOpen={isDocumentModalOpen}
                    onClose={() => setIsDocumentModalOpen(false)}
                    onSubmit={handleDocumentSubmit}
                    currentDocument={currentDocument}
                />

                {/* Confirm Delete Document Modal */}
                <ConfirmModal
                    isOpen={isConfirmDeleteModalOpen}
                    onClose={() => setIsConfirmDeleteModalOpen(false)}
                    onConfirm={confirmDeleteDocument}
                    title="Confirm Delete"
                    message="Are you sure you want to delete this document? This action cannot be undone."
                    cta="Delete"
                    ctaBtnColor="danger"
                />
            </div>
        );
    }
);

OnboardingRequest.displayName = 'OnboardingRequest';

export default OnboardingRequest;
