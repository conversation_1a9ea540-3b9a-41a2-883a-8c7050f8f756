import React, { useEffect, useState } from "react";
import { Page, Text, View, Document, Image } from "@react-pdf/renderer";
import { createTw } from "react-pdf-tailwind";
import { InvoiceStruct } from "@/schemas/billingSchema";
import { formatCurrency } from "@/components/utils/utils";

interface InvoiceProps {
  invoiceId: number;
}

const tw = createTw({
  theme: {
    extend: {
      colors: {
        cyan: {
          600: "#0891b2",
          700: "#0e7490",
        },
        gray: {
          300: "#d1d5db",
          500: "#6b7280",
          700: "#374151",
        },
      },
    },
  },
});

const InvoicePDF: React.FC<InvoiceProps> = ({ invoiceId }) => {
  const [invoice, setInvoice] = useState<InvoiceStruct>({
    id: 0,
    due_date: "",
    contact_email: "",
    address: {
      name: "",
      street: "",
      city: "",
      state: "",
      zip_code: 0,
      country: "",
      phone_number: "",
      created_at: "",
      created_by: 0,
      updated_at: "",
      updated_by: 0,
    },
    receipts: [],
    payments: [],
    summary: {
      total_api_calls: 0,
      cost_per_api_call: 0,
      total_billed: 0,
      pricing_type: "",
    },
    subtotal: 0,
    total: 0,
    tpin: "",
    currency_id: 0,
    status: "",
    notes: [],
    created_at: "",
    created_by: 0,
    updated_at: "",
    updated_by: 0,
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchInvoice = async () => {
      try {
        const invoiceResponse = await fetch(
          `/api/billing/invoices/${invoiceId}`,
          {
            method: "GET",
          },
        );
        const data = await invoiceResponse.json();
        setInvoice(data.data);
        setLoading(false);
      } catch (err) {
        setError("Error fetching invoice data");
        console.error("Error fetching invoice data:", err);
        setLoading(false);
      }
    };

    fetchInvoice();
  }, [invoiceId]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  if (loading) return <Text>Loading...</Text>;
  if (error) return <Text>{error}</Text>;

  return (
    <Document>
      <Page size="A4" style={tw("p-8 bg-white")}>
        <View style={tw("flex flex-row justify-between mb-8")}>
          <View style={tw("flex flex-row items-center")}>
            <Image
              style={tw("h-16 mr-2")}
              src={"/images/logo/Kuala-Horizontal-Black.png"}
            />
          </View>
          <View style={tw("text-gray-700 text-right")}>
            <Text style={tw("text-xl font-bold mb-2")}>RECEIPT</Text>
            <Text style={tw("text-sm")}>
              Date:{" "}
              {invoice?.created_at
                ? formatDate(invoice?.receipts[0]?.created_at)
                : ""}
            </Text>
            <Text style={tw("text-sm")}>
              Receipt No: {invoice?.receipts[0]?.id}
            </Text>
          </View>
        </View>
        <View style={tw("border-b-2 border-gray-300 pb-8 mb-8")}>
          <Text style={tw("text-2xl font-bold mb-4")}>Bill To:</Text>
          <Text style={tw("text-gray-700 mb-2")}>{invoice?.address?.name}</Text>
          <Text style={tw("text-gray-700 mb-2")}>
            {invoice?.address.street}
          </Text>
          <Text style={tw("text-gray-700 mb-2")}>
            {invoice?.address.city}, {invoice?.address.state},{" "}
            {invoice?.address.country}
          </Text>
          <Text style={tw("text-gray-700")}>{invoice?.contact_email}</Text>
        </View>
        <View style={tw("mb-8 border-b-2 border-gray-300")}>
          <View style={tw("flex flex-row text-left bg-gray-300 p-2")}>
            <Text style={tw("w-1/3 py-2 font-bold uppercase text-gray-700")}>
              Description
            </Text>
            <Text style={tw("w-1/3 py-2 font-bold uppercase text-gray-700")}>
              Quantity
            </Text>
            <Text style={tw("w-1/3 py-2 font-bold uppercase text-gray-700")}>
              Price
            </Text>
          </View>
          {invoice?.receipts[0]?.line_items.map((item, index) => (
            <View
              style={tw("flex flex-row text-left p-2 text-sm")}
              key={invoice.id}
            >
              <Text style={tw("w-1/3 py-2 text-gray-700")}>{item.note}</Text>
              <Text style={tw("w-1/3 py-2 text-gray-700")}>
                {item.quantity}
              </Text>
              <Text style={tw("w-1/3 py-2 text-gray-700")}>
                {formatCurrency(item.total, "ZMW")}
              </Text>
            </View>
          ))}
        </View>
        <View style={tw("flex justify-end mb-8")}>
          <Text style={tw("mr-2 text-gray-700")}>Subtotal:</Text>
          <Text style={tw("text-gray-700")}>
            {formatCurrency(invoice?.subtotal, "ZMW")}
          </Text>
        </View>
        <View style={tw("flex justify-end mb-8")}>
          <Text style={tw("mr-2 text-gray-700")}>Total:</Text>
          <Text style={tw("text-gray-700")}>
            {formatCurrency(invoice?.total, "ZMW")}
          </Text>
        </View>
        {invoice?.status !== "paid" && (
          <View style={tw("border-t-2 border-gray-300 pt-8 mb-8")}>
            <Text style={tw("text-gray-700 mb-2")}>
              Payment is due within 30 days. Late payments are subject to fees.
            </Text>
            <Text style={tw("text-gray-700 mb-2")}>
              Please make checks payable to Kuala Tech Limited and mail to:
            </Text>
            <Text style={tw("text-gray-700")}>
              Unit 8D, 1st Floor, Pangaea Office Park Plot No. 2374, Off Great
              East Road, Lusaka, Zambia.
              <Text style={tw("font-bold")}> Email: </Text>
              <EMAIL>
            </Text>
          </View>
        )}
      </Page>
    </Document>
  );
};

export default InvoicePDF;
