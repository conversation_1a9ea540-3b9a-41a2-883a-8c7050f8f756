import React from 'react';
import { Listbox, ListboxButton, ListboxOption, ListboxOptions, Transition } from '@headlessui/react';
import { SelectorIcon, CheckIcon } from '@heroicons/react/solid';

interface AddAdminUserFormProps {
    handleAddAdminUser: (title: string, firstName: string, lastName: string, email: string) => void;
    adminUserTitle: string;
    setAdminUserTitle: (value: string) => void;
    adminUserFirstName: string;
    setAdminUserFirstName: (value: string) => void;
    adminUserLastName: string;
    setAdminUserLastName: (value: string) => void;
    adminUserEmail: string;
    setAdminUserEmail: (value: string) => void;
    errors: { [key: string]: string };
}
const titles = ["Mr", "Ms", "Mrs", "Dr", "Prof"];
const AddAdminUserForm: React.FC<AddAdminUserFormProps> = ({
    handleAddAdminUser,
    adminUserTitle,
    setAdminUserTitle,
    adminUserFirstName,
    setAdminUserFirstName,
    adminUserLastName,
    setAdminUserLastName,
    adminUserEmail,
    setAdminUserEmail,
    errors
}) => {
    return (
        <form
            onSubmit={(e) => {
                e.preventDefault();
                handleAddAdminUser(adminUserTitle, adminUserFirstName, adminUserLastName, adminUserEmail);
                setAdminUserFirstName('');
                setAdminUserLastName('');
                setAdminUserEmail('');
            }}
        >
            <div className="mt-2">
                <label
                    htmlFor="registered_title"
                    className="block text-sm font-medium text-gray-700"
                >
                    Title <span className="text-red-500">*</span>
                </label>
                <Listbox value={adminUserTitle} onChange={setAdminUserTitle}>
                    <div className="relative mt-1">
                        <ListboxButton className={`relative w-full cursor-default rounded-md border border-gray-300 py-2 pl-3 pr-10 text-left shadow-md focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-11 ${adminUserTitle ? "bg-[#e8f0fe]" : ""}`} style={{ fontSize: "14px" }}>
                            <span className="block truncate">{adminUserTitle || "Select Title"}</span>
                            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                <SelectorIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                            </span>
                        </ListboxButton>
                        <Transition
                            as={React.Fragment}
                            leave="transition ease-in duration-100"
                            leaveFrom="opacity-100"
                            leaveTo="opacity-0"
                        >
                            <ListboxOptions className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none md:text-md" style={{ fontSize: "14px" }}>
                                {titles.map((title) => (
                                    <ListboxOption
                                        key={title}
                                        className={({ active }) =>
                                            `relative cursor-default select-none py-2 pl-10 pr-4 ${active ? "bg-[#f1f1f1] text-gray-700" : "text-gray-900"
                                            }`}
                                        value={title}
                                    >
                                        {({ selected }) => (
                                            <>
                                                <span
                                                    className={`block truncate ${selected ? "font-medium" : "font-normal"
                                                        }`}
                                                >
                                                    {title}
                                                </span>
                                                {selected ? (
                                                    <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-white">
                                                        <CheckIcon className="h-5 w-5" aria-hidden="true" />
                                                    </span>
                                                ) : null}
                                            </>
                                        )}
                                    </ListboxOption>
                                ))}
                            </ListboxOptions>
                        </Transition>
                    </div>
                </Listbox>
                {errors.adminUserTitle && <p className="text-red-500 text-sm">{errors.adminUserTitle}</p>}
            </div>
            <div className="mt-2">
                <label
                    htmlFor="admin_first_name"
                    className="block text-sm font-medium text-gray-700"
                >
                    First Name <span className="text-red-500">*</span>
                </label>
                <input
                    type="text"
                    id="admin_first_name"
                    name="admin_first_name"
                    value={adminUserFirstName}
                    onChange={(e) => setAdminUserFirstName(e.target.value)}
                    className="mt-1 w-full rounded-md shadow-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-wbv-theme"
                    style={{ fontSize: "14px" }}
                />
                {errors.adminUserFirstName && <p className="text-red-500 text-sm">{errors.adminUserFirstName}</p>}
            </div>
            <div className="mt-2">
                <label
                    htmlFor="admin_last_name"
                    className="block text-sm font-medium text-gray-700"
                >
                    Last Name <span className="text-red-500">*</span>
                </label>
                <input
                    type="text"
                    id="admin_last_name"
                    name="admin_last_name"
                    value={adminUserLastName}
                    onChange={(e) => setAdminUserLastName(e.target.value)}
                    className="mt-1 w-full rounded-md shadow-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-wbv-theme"
                    style={{ fontSize: "14px" }}
                />
                {errors.adminUserLastName && <p className="text-red-500 text-sm">{errors.adminUserLastName}</p>}
            </div>
            <div className="mt-2">
                <label
                    htmlFor="admin_email"
                    className="block text-sm font-medium text-gray-700"
                >
                    Email <span className="text-red-500">*</span>
                </label>
                <input
                    type="email"
                    id="admin_email"
                    name="admin_email"
                    value={adminUserEmail}
                    onChange={(e) => setAdminUserEmail(e.target.value)}
                    className="mt-1 w-full rounded-md shadow-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-wbv-theme"
                    style={{ fontSize: "14px" }}
                />
                {errors.adminUserEmail && <p className="text-red-500 text-sm">{errors.adminUserEmail}</p>}
            </div>
            <button
                type="submit"
                className="mt-4 w-full rounded-md bg-wbv-theme px-4 py-2 font-semibold text-white transition duration-300 hover:bg-opacity-90"
            >
                Add Admin User
            </button>
        </form>
    );
};

export default AddAdminUserForm;