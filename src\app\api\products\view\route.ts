import { NextResponse } from 'next/server';
import { Client as MinioClient } from 'minio';

const minioClient = new MinioClient({
    endPoint: process.env.S3_ENDPOINT!,
    port: Number(process.env.S3_PORT) || 443,
    useSSL: process.env.S3_USE_SSL === 'true',
    accessKey: process.env.S3_ACCESS_KEY!,
    secretKey: process.env.S3_SECRET_KEY!,
});

export async function GET(request: Request) {
    try {
        // Extract path from URL
        const url = new URL(request.url);
        const fullPath = url.pathname;
        const pathParts = fullPath.split('/api/products/view/');
        const imagePath = pathParts[1];

        console.log('Attempting to fetch image:', {
            fullPath,
            imagePath,
            bucket: process.env.S3_BUCKET
        });

        if (!imagePath) {
            return NextResponse.json({ error: 'Image path is required' }, { status: 400 });
        }

        // Generate presigned URL
        const presignedUrl = await minioClient.presignedGetObject(
            process.env.S3_BUCKET!,
            imagePath,
            24 * 60 * 60 // 24 hours expiry
        );

        console.log('Generated presigned URL:', presignedUrl);

        // Redirect to the presigned URL
        return NextResponse.redirect(presignedUrl);
    } catch (error) {
        console.error('Error handling image request:', error);
        return NextResponse.json({ error: 'Failed to process image request' }, { status: 500 });
    }
}