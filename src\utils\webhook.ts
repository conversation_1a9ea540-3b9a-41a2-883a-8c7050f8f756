// pages/api/webhook.js
import { NextApiRequest, NextApiResponse } from "next";

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === "POST") {
    const data = req.body;
    // Process the data received from the webhook
    console.log("Webhook data:", data);
    res.status(200).json({ message: "Data received" });
  } else {
    res.status(405).json({ message: "Method not allowed" });
  }
}
