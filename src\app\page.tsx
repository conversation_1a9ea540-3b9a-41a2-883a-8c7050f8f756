"use client";

import DefaultLayout from "@/components/Layouts/DefaultLayout";
import Whitebook from "@/components/Dashboard/Whitebook";
import IndividualWhitebook from "@/components/Dashboard/IndividualWhiteBook";
import { useSession } from "next-auth/react";

const Home = () => {
  const { data: session, status } = useSession();
  let role = null;
  if (session?.user) {
    role = session.user.role;
  }

  return (
    <>
      <DefaultLayout>
        {role == "admin" || role == "super_admin" ? (
          <Whitebook />
        ) : (
          <IndividualWhitebook />
        )}
      </DefaultLayout>
    </>
  );
};

export default Home;
