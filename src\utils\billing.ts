import {
  InvoiceStruct,
  Address,
  Payment,
  PaymentResponse,
  Summary,
  LineItem,
  Receipt,
} from "@/schemas/billingSchema";

export async function openPaymentLink(
  invoiceToPay: { invoiceId: number; total: number },
  newTab: boolean = true,
): Promise<void> {
  try {
    const response = await fetch("/api/billing/payments", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(invoiceToPay),
    });

    if (!response.ok) {
      throw new Error("Failed to generate payment link");
    }

    const data = await response.json();
    const url = data.url;

    if (newTab) {
      window.open(url, "_blank");
    } else {
      window.location.href = url;
    }
  } catch (error) {
    console.error("Error generating payment link:", error);
  }
}

export const isValidAddress = (address: any): address is Address => {
  return (
    typeof address.name === "string" &&
    typeof address.street === "string" &&
    typeof address.city === "string" &&
    typeof address.state === "string" &&
    typeof address.zip_code === "number" &&
    typeof address.country === "string" &&
    typeof address.phone_number === "string" &&
    typeof address.created_at === "string" &&
    typeof address.created_by === "number" &&
    typeof address.updated_at === "string" &&
    typeof address.updated_by === "number"
  );
};

export const isValidPaymentResponse = (
  paymentResponse: any,
): paymentResponse is PaymentResponse => {
  return (
    typeof paymentResponse.id === "number" &&
    typeof paymentResponse.amount === "number" &&
    typeof paymentResponse.external_id === "string" &&
    typeof paymentResponse.narration === "string" &&
    typeof paymentResponse.status_code === "number" &&
    typeof paymentResponse.payer_number === "string" &&
    typeof paymentResponse.account_number === "string" &&
    typeof paymentResponse.response_code === "string" &&
    typeof paymentResponse.response_message === "string" &&
    typeof paymentResponse.created_at === "string" &&
    typeof paymentResponse.created_by === "number" &&
    typeof paymentResponse.updated_at === "string" &&
    typeof paymentResponse.updated_by === "number"
  );
};

export const isValidPayment = (payment: any): payment is Payment => {
  return (
    typeof payment.id === "number" &&
    typeof payment.amount === "number" &&
    typeof payment.type === "string" &&
    typeof payment.status === "string" &&
    typeof payment.currency_id === "number" &&
    (typeof payment.invoice_id === "number" || payment.invoice_id === null) &&
    typeof payment.receipt_id === "number" &&
    typeof payment.transaction_id === "number" &&
    isValidPaymentResponse(payment.payment_response) &&
    typeof payment.created_at === "string" &&
    typeof payment.created_by === "number" &&
    typeof payment.updated_at === "string" &&
    typeof payment.updated_by === "number"
  );
};

export const isValidSummary = (summary: any): summary is Summary => {
  return (
    typeof summary.total_api_calls === "number" &&
    typeof summary.cost_per_api_call === "number" &&
    typeof summary.total_billed === "number" &&
    typeof summary.pricing_type === "string"
  );
};

export const isValidLineItem = (lineItem: any): lineItem is LineItem => {
  return (
    typeof lineItem.note === "string" &&
    (typeof lineItem.price === "number" || lineItem.price === undefined) &&
    typeof lineItem.quantity === "number" &&
    typeof lineItem.total === "number"
  );
};

export const isValidReceipt = (receipt: any): receipt is Receipt => {
  return (
    typeof receipt.id === "number" &&
    Array.isArray(receipt.line_items) &&
    receipt.line_items.every(isValidLineItem) &&
    typeof receipt.total === "number" &&
    typeof receipt.currency_id === "number" &&
    typeof receipt.invoice_id === "number" &&
    typeof receipt.payment_id === "number" &&
    typeof receipt.created_at === "string" &&
    typeof receipt.created_by === "number" &&
    typeof receipt.updated_at === "string" &&
    typeof receipt.updated_by === "number"
  );
};

export const isValidInvoice = (invoice: any): invoice is InvoiceStruct => {
  return (
    typeof invoice.id === "number" &&
    typeof invoice.due_date === "string" &&
    typeof invoice.contact_email === "string" &&
    isValidAddress(invoice.address) &&
    Array.isArray(invoice.receipts) &&
    invoice.receipts.every(isValidReceipt) &&
    Array.isArray(invoice.payments) &&
    invoice.payments.every(isValidPayment) &&
    isValidSummary(invoice.summary) &&
    typeof invoice.subtotal === "number" &&
    typeof invoice.total === "number" &&
    typeof invoice.tpin === "string" &&
    typeof invoice.currency_id === "number" &&
    typeof invoice.status === "string" &&
    Array.isArray(invoice.notes) &&
    invoice.notes.every((note: any) => typeof note === "string") &&
    typeof invoice.created_at === "string" &&
    typeof invoice.created_by === "number" &&
    typeof invoice.updated_at === "string" &&
    typeof invoice.updated_by === "number"
  );
};

export const validateInvoices = (
  invoices: any[],
): invoices is InvoiceStruct[] => {
  if (!Array.isArray(invoices)) {
    console.error("Invalid data: invoices is not an array");
    return false;
  }

  for (const invoice of invoices) {
    if (!isValidInvoice(invoice)) {
      console.error("Invalid invoice data:", invoice);
      return false;
    }
  }

  return true;
};
