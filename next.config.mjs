import dotenv from "dotenv";

// Load environment variables based on the environment
if (process.env.NODE_ENV === "development") {
  dotenv.config({ path: ".env.local" });
} else {
  dotenv.config();
}

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  env: {
    WHITEBOOK_ADMIN_API_KEY: process.env.WHITEBOOK_API_KEY,
    WHITEBOOK_API_URL_V2: process.env.WHITEBOOK_API_URL_V2,
    WHITEBOOK_API_URL: process.env.WHITEBOOK_API_URL,
    PRIMENET_CLIENT_ID: process.env.PRIMENET_CLIENT_ID,
    PRIMENET_CLIENT_SECRET: process.env.PRIMENET_CLIENT_SECRET,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "placehold.jp",
        port: "",
        pathname: "/**"
      },
    ],
  },
  eslint: {
    ignoreDuringBuilds: true
  }
};

export default nextConfig;
