import React from 'react';


interface TabsProps {
    activeTab: "individual" | "organization";
    handleTabChange: (tab: "individual" | "organization") => void;
    tab1?: string;
    tab2?: string;
}


const OnboardingTabs: React.FC<TabsProps> = ({ activeTab, handleTabChange, tab1, tab2 }) => {
    return (
        <div className="flex w-full">
            <button
                className={`w-1/2 py-2 text-center font-semibold ${activeTab === tab1?.toLowerCase()
                    ? "bg-wbv-theme text-white"
                    : "bg-gray-200 text-gray-700"
                    } rounded-l-md transition duration-300`}
                onClick={() => handleTabChange((tab1?.toLowerCase() as "individual" | "organization") || "individual")}
            >
                {tab1}
            </button>
            <button
                className={`w-1/2 py-2 text-center font-semibold ${activeTab === tab2?.toLowerCase()
                    ? "bg-wbv-theme text-white"
                    : "bg-gray-200 text-gray-700"
                    } rounded-r-md transition duration-300`}
                onClick={() => handleTabChange((tab2?.toLowerCase() as "individual" | "organization") || "organization")}
            >
                {tab2}
            </button>
        </div>
    );
};

export default OnboardingTabs;