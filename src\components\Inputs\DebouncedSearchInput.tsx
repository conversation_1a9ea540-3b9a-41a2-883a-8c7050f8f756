import React, { useState, useEffect } from "react";

interface DebouncedSearchInputProps {
  value: string;
  onChange: (value: string) => void;
  debounced: number;
  placeholder: string;
}

const DebouncedSearchInput: React.FC<DebouncedSearchInputProps> = ({
  value: initValue,
  onChange,
  debounced: debouncedValue,
  placeholder,
}) => {
  const [inputValue, setInputValue] = useState(initValue);
  const [debounced, setDebounced] = useState(debouncedValue);
  useEffect(() => {
    setInputValue(initValue);
    setDebounced(debouncedValue);
    // console.log("initValue", initValue);
    // console.log("debouncedValueInput", debouncedValueInput);
  }, [initValue]);

  // * 0.5s after set value in state, call onChange
  useEffect(() => {
    const timeout = setTimeout(() => {
      onChange(inputValue);
    }, debounced);
    // console.log("inputValue", inputValue);
    return () => clearTimeout(timeout);
  }, [inputValue]);

  return (
    <input
      value={inputValue}
      onChange={(e) => setInputValue(e.target.value)}
      placeholder={placeholder}
      className="w-1/5 rounded border border-b-2 border-b-wbv-theme bg-white p-2 outline-none duration-300 focus:w-1/3"
    />
  );
};

export default DebouncedSearchInput;
