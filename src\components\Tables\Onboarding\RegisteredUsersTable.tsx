import React, { useState } from 'react';
import { PencilIcon, TrashIcon, CheckIcon } from '@heroicons/react/solid';
import { registeredUserSchema } from "@/components/Forms/Onboarding/schemas/schemas";

interface Role {
    id: number;
    name: string;
    label: string;
    description: string;
    permissions: Array<{
        id: number;
        name: string;
        description: string;
        created_at: string;
        updated_at: string;
        deleted_at: string | null;
    }>;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
}

interface RegisteredUser {
    registeredUserTitle: string;
    registeredUserFirstName: string;
    registeredUserLastName: string;
    registeredUserEmail: string;
    registeredUserRoleId: number;
}

interface RegisteredUsersTableProps {
    roles: Role[];
    users: RegisteredUser[];
    handleEditUser: (index: number, updatedUser: RegisteredUser) => void;
    handleRemoveUser: (index: number) => void;
}

const titles = ["Mr", "Ms", "Mrs", "Dr", "Prof"];

const RegisteredUsersTable: React.FC<RegisteredUsersTableProps> = ({ users, handleEditUser, handleRemoveUser, roles }) => {
    const [editingIndex, setEditingIndex] = useState<number | null>(null);
    const [editedUser, setEditedUser] = useState<RegisteredUser | null>(null);
    const [errors, setErrors] = useState<{ [key: string]: string }>({});

    const rolesToDisplay = ["reviewer", "approver", "initiator"];
    const filteredRoles = roles.filter(role => rolesToDisplay.includes(role.name.toLowerCase()));

    const handleEditClick = (index: number, user: RegisteredUser) => {
        setEditingIndex(index);
        setEditedUser(user);
    };

    const handleSaveClick = (index: number) => {
        if (editedUser) {
            // Check for duplicate email
            const emailExists = users.some((user, i) => user.registeredUserEmail === editedUser.registeredUserEmail && i !== index);
            if (emailExists) {
                setErrors({ registeredUserEmail: "Email already exists." });
                return;
            }

            const validationResult = registeredUserSchema.safeParse(editedUser);
            if (!validationResult.success) {
                const newErrors: { [key: string]: string } = {};
                validationResult.error.errors.forEach((error) => {
                    newErrors[error.path[0]] = error.message;
                });
                setErrors(newErrors);
                return;
            } else {
                setErrors({});
                handleEditUser(index, editedUser);
                setEditingIndex(null);
                setEditedUser(null);
            }
        }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>, field: string) => {
        if (editedUser) {
            const value = field === 'registeredUserRoleId' ? Number(e.target.value) : e.target.value;
            setEditedUser({ ...editedUser, [field]: value });
        }
    };
    const handleRoleInputChange = (e: React.ChangeEvent<HTMLSelectElement>, field: number) => {
        if (editedUser) {
            setEditedUser({ ...editedUser, [field]: e.target.value });
        }
    };

    const handleTitleChange = (value: string) => {
        if (editedUser) {
            setEditedUser({ ...editedUser, registeredUserTitle: value });
        }
    };

    console.log("editedUser", editedUser);

    return (
        <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
                <thead>
                    <tr>
                        <th className="py-2 px-4 border-b text-left">Title</th>
                        <th className="py-2 px-4 border-b text-left">First Name</th>
                        <th className="py-2 px-4 border-b text-left">Last Name</th>
                        <th className="py-2 px-4 border-b text-left">Email</th>
                        <th className="py-2 px-4 border-b text-left">Role</th>
                        <th className="py-2 px-4 border-b text-left">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {users.map((user, index) => (
                        <tr key={index}>
                            {editingIndex === index ? (
                                <>
                                    <td className="py-2 px-4 border-b">
                                        <select
                                            value={editedUser?.registeredUserTitle || ""}
                                            onChange={(e) => handleTitleChange(e.target.value)}
                                            className="w-full cursor-default rounded border border-gray-300 bg-white py-1 px-2 text-left focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-8 capitalize"
                                        >
                                            {titles.map((title) => (
                                                <option
                                                    key={title}
                                                    value={title}
                                                    className="hover:bg-gray-200 capitalize"
                                                >
                                                    {title}
                                                </option>
                                            ))}
                                        </select>
                                        {errors.registeredUserTitle && <p className="text-red-500 text-sm">{errors.registeredUserTitle}</p>}
                                    </td>
                                    <td className="py-2 px-4 border-b">
                                        <input
                                            type="text"
                                            value={editedUser?.registeredUserFirstName}
                                            onChange={(e) => handleInputChange(e, 'registeredUserFirstName')}
                                            className="w-full border rounded px-2 py-1"
                                        />
                                        {errors.registeredUserFirstName && <p className="text-red-500 text-sm">{errors.registeredUserFirstName}</p>}
                                    </td>
                                    <td className="py-2 px-4 border-b">
                                        <input
                                            type="text"
                                            value={editedUser?.registeredUserLastName}
                                            onChange={(e) => handleInputChange(e, 'registeredUserLastName')}
                                            className="w-full border rounded px-2 py-1"
                                        />
                                        {errors.registeredUserLastName && <p className="text-red-500 text-sm">{errors.registeredUserLastName}</p>}
                                    </td>
                                    <td className="py-2 px-4 border-b">
                                        <input
                                            type="email"
                                            value={editedUser?.registeredUserEmail}
                                            onChange={(e) => handleInputChange(e, 'registeredUserEmail')}
                                            className="w-full border rounded px-2 py-1"
                                        />
                                        {errors.registeredUserEmail && <p className="text-red-500 text-sm">{errors.registeredUserEmail}</p>}
                                    </td>
                                    <td className="py-2 px-4 border-b">
                                        <select
                                            value={editedUser?.registeredUserRoleId || ""}
                                            onChange={(e) => handleInputChange(e, 'registeredUserRoleId')}
                                            className="w-full border rounded px-2 py-1 capitalize"
                                        >
                                            {filteredRoles.map((role) => (
                                                <option key={role.id} value={role.id} className="capitalize">
                                                    {role.label}
                                                </option>
                                            ))}
                                        </select>
                                        {errors.registeredUserRoleId && <p className="text-red-500 text-sm">{errors.registeredUserRoleId}</p>}
                                    </td>
                                    <td className="py-2 px-4 border-b">
                                        <button onClick={() => handleSaveClick(index)} className="text-green-500 hover:text-green-700">
                                            Save
                                        </button>
                                    </td>
                                </>
                            ) : (
                                <>
                                    <td className="py-2 px-4 border-b capitalize">{user.registeredUserTitle}</td>
                                    <td className="py-2 px-4 border-b">{user.registeredUserFirstName}</td>
                                    <td className="py-2 px-4 border-b">{user.registeredUserLastName}</td>
                                    <td className="py-2 px-4 border-b">{user.registeredUserEmail}</td>
                                    <td className="py-2 px-4 border-b capitalize">{filteredRoles.find(role => role.id === user.registeredUserRoleId)?.name}</td>
                                    <td className="py-2 px-4 border-b">
                                        <button onClick={() => handleEditClick(index, user)} className="text-blue-500 hover:text-blue-700 mr-2">
                                            <PencilIcon className="h-5 w-5" />
                                        </button>
                                        <button onClick={() => handleRemoveUser(index)} className="text-red-500 hover:text-red-700">
                                            <TrashIcon className="h-5 w-5" />
                                        </button>
                                    </td>
                                </>
                            )}
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default RegisteredUsersTable;