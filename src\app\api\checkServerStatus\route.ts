import { NextResponse } from "next/server";

export async function GET() {
  try {
    const response = await fetch(`${process.env.WHITEBOOK_API_URL}`);
    if (!response.ok) {
      throw new Error("Server is down");
    }
    return NextResponse.json({ status: "ok" });
  } catch (error) {
    return NextResponse.json(
      { status: "error", message: "Server is down" },
      { status: 500 },
    );
  }
}
