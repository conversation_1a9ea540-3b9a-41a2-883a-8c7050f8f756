import React, { useState } from "react";
import { Bar, Line, Pie } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
} from "chart.js";
import { formatDateTime } from "../utils/utils";
import OrganizationModal from "../Modals/OrganizationModal";
import ConfirmModal from "../Modals/ConfirmModal";
import Link from "next/link";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
);

interface OrganizationStruct {
  name: string;
  registration_number: string;
  tax_id: string;
  organization_type_id: number;
  parent_id: number;
  email: string;
  phone_number: string;
  postal_address: string;
  town_id: number;
  street_address: string;
  id: number;
  created_by: number;
  updated_by: number;
  deleted_by: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}
interface OrganizationProps {
  organization: OrganizationStruct;
  handleDelete: (id: number) => void;
  handleUpdate: (updatedOrganization: Partial<OrganizationStruct>) => void;
  loading: boolean;
  error: string | null;
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const Organization: React.FC<OrganizationProps> = ({
  organization,
  loading,
  error,
  isModalOpen,
  setIsModalOpen,
  handleUpdate,
  handleDelete,
}) => {
  const [selectedOrganization, setSelectedOrganization] =
    useState<Partial<OrganizationStruct> | null>(null);

  const [newOrganization, setNewOrganization] = useState<
    Partial<OrganizationStruct>
  >({
    name: "",
    street_address: "",
    postal_address: "",
    email: "",
    phone_number: "",
  });

  const [isConfirmDeleteModalOpen, setIsConfirmDeleteModalOpen] =
    useState<boolean>(false);
  const [isConfirmSuspendModalOpen, setIsConfirmSuspendModalOpen] =
    useState<boolean>(false);

  const usageData = {
    labels: ["January", "February", "March", "April", "May", "June"],
    datasets: [
      {
        label: "Usage",
        data: [65, 59, 80, 81, 56, 55],
        backgroundColor: "rgba(75,192,192,0.4)",
        borderColor: "rgba(75,192,192,1)",
        borderWidth: 1,
      },
    ],
  };

  const loginsData = {
    labels: ["Week 1", "Week 2", "Week 3", "Week 4"],
    datasets: [
      {
        label: "Logins",
        data: [12, 19, 3, 5],
        backgroundColor: "rgba(153,102,255,0.4)",
        borderColor: "rgba(153,102,255,1)",
        borderWidth: 1,
      },
    ],
  };

  const activityData = {
    labels: ["Search", "View", "Edit", "Delete"],
    datasets: [
      {
        label: "Activity",
        data: [300, 50, 100, 40],
        backgroundColor: [
          "rgba(255,99,132,0.4)",
          "rgba(54,162,235,0.4)",
          "rgba(255,206,86,0.4)",
          "rgba(75,192,192,0.4)",
        ],
        borderColor: [
          "rgba(255,99,132,1)",
          "rgba(54,162,235,1)",
          "rgba(255,206,86,1)",
          "rgba(75,192,192,1)",
        ],
        borderWidth: 1,
      },
    ],
  };

  const searchHistoryData = {
    labels: ["Car", "Bike", "Truck", "Bus"],
    datasets: [
      {
        label: "Search History",
        data: [200, 150, 100, 50],
        backgroundColor: "rgba(255,159,64,0.4)",
        borderColor: "rgba(255,159,64,1)",
        borderWidth: 1,
      },
    ],
  };

  const fmvApiUsageData = {
    labels: ["January", "February", "March", "April", "May", "June"],
    datasets: [
      {
        label: "FMV API Usage",
        data: [20, 30, 40, 50, 60, 70],
        backgroundColor: "rgba(255,206,86,0.4)",
        borderColor: "rgba(255,206,86,1)",
        borderWidth: 1,
      },
    ],
  };

  const billingSummaryData = {
    labels: ["January", "February", "March", "April", "May", "June"],
    datasets: [
      {
        label: "Billing Summary",
        data: [1000, 1500, 2000, 2500, 3000, 3500],
        backgroundColor: "rgba(75,192,192,0.4)",
        borderColor: "rgba(75,192,192,1)",
        borderWidth: 1,
      },
    ],
  };

  const openUpdateModal = (organization: Partial<OrganizationStruct>) => {
    setSelectedOrganization(organization);
    setNewOrganization(organization);
    setIsModalOpen(true);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewOrganization((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handlePhoneChange = (value: string) => {
    setNewOrganization((prevData) => ({
      ...prevData,
      phone_number: value,
    }));
  };

  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleUpdate(newOrganization);
    setNewOrganization({
      name: "",
      street_address: "",
      postal_address: "",
      email: "",
      phone_number: "",
    });
    setSelectedOrganization(null);
    setIsModalOpen(false);
  };

  const openSuspendModal = () => {
    setIsConfirmSuspendModalOpen(true);
  };

  const openDeleteModal = () => {
    setIsConfirmDeleteModalOpen(true);
  };

  const confirmDelete = () => {
    setIsConfirmDeleteModalOpen(false);
  };

  const confirmSuspend = () => {
    setIsConfirmSuspendModalOpen(false);
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div className="text-red-500">Error: {error}</div>;

  return (
    <div className="pb-6 pt-4">
      <div className="mb-4 flex items-center justify-between">
        <h1 className="whitespace-nowrap text-3xl font-bold">
          {organization?.name}&nbsp;
          <span className="rounded-full bg-wbv-theme px-2 py-1 text-xs font-semibold text-gray-100">
            Active
          </span>
        </h1>
        <Link href="/organizations" className="text-blue-500 hover:underline">
          Back to Organizations
        </Link>
      </div>
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="flex-1 bg-white p-4 shadow-md">
          <h2 className="mb-2 text-xl font-semibold">Organization Details</h2>
          <p className="text-sm text-gray-700">
            <strong>Registration Number:</strong>{" "}
            {organization?.registration_number}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Tax ID:</strong> {organization?.tax_id}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Organization Type ID:</strong>{" "}
            {organization?.organization_type_id}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Parent ID:</strong> {organization?.parent_id}
          </p>
        </div>
        <div className="flex-1 bg-white p-4 shadow-md">
          <h2 className="mb-2 text-xl font-semibold">Contact Information</h2>
          <p className="text-sm text-gray-700">
            <strong>Email:</strong> {organization?.email}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Phone Number:</strong> {organization?.phone_number}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Postal Address:</strong> {organization?.postal_address}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Street Address:</strong> {organization?.street_address}
          </p>
        </div>
        <div className="flex-1 bg-white p-4 shadow-md">
          <h2 className="mb-2 text-xl font-semibold">Meta Information</h2>
          <p className="text-sm text-gray-700">
            <strong>Created By:</strong> {organization?.created_by}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Updated By:</strong> {organization?.updated_by}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Deleted By:</strong> {organization?.deleted_by}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Created At:</strong>{" "}
            {organization?.created_at
              ? formatDateTime(organization.created_at)
              : "N/A"}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Updated At:</strong>{" "}
            {organization?.updated_at &&
              organization.updated_at !== "0001-01-01T00:00:00Z"
              ? formatDateTime(organization.updated_at)
              : "N/A"}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Deleted At:</strong>{" "}
            {organization?.deleted_at
              ? formatDateTime(organization.deleted_at)
              : "N/A"}
          </p>
        </div>
      </div>
      {/* Action Buttons */}
      <div className="mt-4 flex space-x-4">
        <button
          className="rounded bg-wbv-theme px-4 py-2 text-white hover:bg-opacity-90"
          onClick={() => organization && openUpdateModal(organization)}
        >
          Edit
        </button>
        <button
          className="rounded bg-yellow-500 px-4 py-2 text-white hover:bg-opacity-90"
          onClick={() => organization && openSuspendModal()}
        >
          Suspend
        </button>
        <button
          className="rounded bg-red-500 px-4 py-2 text-white hover:bg-opacity-90"
          onClick={() => organization && openDeleteModal()}
        >
          Delete
        </button>
      </div>
      {/* Organization Statistics */}
      <div className="mt-4 bg-white p-4 shadow-md">
        <h2 className="mb-2 text-xl font-semibold">Organization Statistics</h2>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <h3 className="text-lg font-semibold">Usage</h3>
            <Bar data={usageData} />
          </div>
          <div>
            <h3 className="text-lg font-semibold">Logins Made</h3>
            <Line data={loginsData} />
          </div>
          <div>
            <h3 className="text-lg font-semibold">Activity Summary</h3>
            <Pie data={activityData} />
          </div>
          <div>
            <h3 className="text-lg font-semibold">Search History</h3>
            <Bar data={searchHistoryData} />
          </div>
          <div>
            <h3 className="text-lg font-semibold">FMV API Usage</h3>
            <Line data={fmvApiUsageData} />
          </div>
          <div>
            <h3 className="text-lg font-semibold">Billing Summary</h3>
            <Bar data={billingSummaryData} />
          </div>
        </div>
      </div>
      {/* Modal */}
      {isModalOpen && (
        <OrganizationModal
          selectedOrganization={selectedOrganization}
          onSubmit={onSubmit}
          newOrganization={newOrganization}
          handleChange={handleChange}
          handlePhoneChange={handlePhoneChange}
          setIsModalOpen={setIsModalOpen}
        />
      )}
      {/* Confirm Delete Modal */}
      <ConfirmModal
        isOpen={isConfirmDeleteModalOpen}
        onClose={() => setIsConfirmDeleteModalOpen(false)}
        onConfirm={confirmDelete}
        title="Confirm Deletion"
        message={`Are you sure you want to delete the organization <strong>${organization?.name}?</strong>`}
        cta="Delete"
        ctaBtnColor="danger"
      />
      {/* Confirm Suspend Modal */}
      <ConfirmModal
        isOpen={isConfirmSuspendModalOpen}
        onClose={() => setIsConfirmSuspendModalOpen(false)}
        onConfirm={confirmSuspend}
        title="Confirm Suspension"
        message={`Are you sure you want to suspend the organization <strong>${organization?.name}?</strong>`}
        cta="Suspend"
        ctaBtnColor="warning"
      />
    </div>
  );
};

export default Organization;