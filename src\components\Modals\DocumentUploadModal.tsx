import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faFileUpload, faFileEdit } from "@fortawesome/free-solid-svg-icons";

interface DocumentStruct {
    id: number;
    name: string;
    type: string;
    file: string;
    path: string;
    organization_id: number;
    created_at: string;
    created_by: number;
    updated_at: string;
    updated_by: number;
    deleted_at: string | null;
    deleted_by: number;
}

interface DocumentUploadModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (file: File) => void;
    currentDocument?: DocumentStruct | null;
}

const DocumentUploadModal: React.FC<DocumentUploadModalProps> = ({ isOpen, onClose, onSubmit, currentDocument }) => {
    const [file, setFile] = useState<File | null>(null);
    const [error, setError] = useState('');

    useEffect(() => {
        if (!isOpen) {
            setFile(null);
            setError('');
        }
    }, [isOpen]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (!file && !currentDocument) {
            setError('Please select a file');
            return;
        }

        if (file) {
            onSubmit(file);
        }
        onClose();
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50 p-4">
            <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-lg">
                <div className="flex flex-col items-center">
                    <FontAwesomeIcon
                        icon={currentDocument ? faFileEdit : faFileUpload}
                        className="mb-2 text-3xl text-wbv-theme"
                    />
                    <h2 className="mb-2 text-center text-xl font-bold">
                        {currentDocument ? 'Update' : 'Upload New'} Document
                    </h2>
                    <div className="mb-4 h-1 w-full bg-gradient-to-r from-transparent via-wbv-theme to-transparent"></div>
                </div>
                <form onSubmit={handleSubmit}>
                    <div className="mb-4">
                        <label className="block text-md font-medium text-gray-700">
                            File {!currentDocument && <span className="text-red-500">*</span>}
                        </label>
                        <input
                            type="file"
                            onChange={(e) => setFile(e.target.files?.[0] || null)}
                            className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 shadow-md focus:outline-none focus:ring-2 focus:ring-wbv-theme md:text-md h-11"
                            style={{ fontSize: "14px" }}
                            required={!currentDocument}
                        />
                        {currentDocument && !file && (
                            <p className="mt-1 text-sm text-gray-500">
                                Leave empty to keep the current file
                            </p>
                        )}
                    </div>
                    {error && <p className="mb-4 text-sm text-red-500">{error}</p>}
                    <div className="mt-4 flex justify-center">
                        <button
                            type="button"
                            onClick={onClose}
                            className="mr-2 rounded bg-gray-500 p-2 text-white hover:bg-gray-600"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            className="rounded bg-wbv-theme p-2 text-white hover:bg-opacity-90"
                        >
                            {currentDocument ? 'Update' : 'Upload'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default DocumentUploadModal;