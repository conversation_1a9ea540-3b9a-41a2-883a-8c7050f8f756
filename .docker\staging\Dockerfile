# Base node image
FROM node:22-bullseye-slim  as base

ARG NODE_ENV=test
ARG DOTENV_KEY
ARG COMMIT_SHA
ARG RELEASE_SEMVER

# Set environment variables for base and all layers that inherit from it
ENV LOG_LEVEL=debug
ENV DEBUG_API=true
ENV NODE_ENV=${NODE_ENV}
ENV DOTENV_KEY=${DOTENV_KEY}
ENV COMMIT_SHA ${COMMIT_SHA}
ENV RELEASE_SEMVER ${RELEASE_SEMVER}

RUN apt-get update && apt-get install -y curl tini

# Install all node_modules, including dev dependencies
FROM base as deps

WORKDIR /app

ADD package.json package-lock.json ./
# Fetch and decrypt environment variables from dotenv-vault
RUN npm install --include=dev

# Setup production node_modules
FROM base as production-deps

WORKDIR /app

COPY --from=deps /app/node_modules ./node_modules
ADD package.json package-lock.json ./
RUN npm prune --omit=dev

# Build the app
FROM base as build

WORKDIR /app

COPY --from=deps /app/node_modules ./node_modules
ADD package.json package-lock.json ./

ADD . .
ENV NODE_ENV=test
RUN npm run build

# Finally, build the production image with minimal footprint
FROM base

ENV NODE_ENV=test
WORKDIR /app

COPY --from=production-deps /app/node_modules ./node_modules
COPY --from=build /app/.next ./.next
COPY --from=build /app/public ./public
COPY --from=build /app/package.json ./package.json

RUN mkdir -p tmp

ADD . .

EXPOSE 3000

CMD ["npm", "run", "start"]