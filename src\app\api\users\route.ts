import { v2EngineAxiosInstance } from "@/utils/axios";
import axios from "axios";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  const token = request.headers.get("token");
  if (!token) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  try {
    const response = await v2EngineAxiosInstance("/users", {
      headers: {
        Authorization: `Bear<PERSON> ${token}`,
      },
    });
    return NextResponse.json(response.data);
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: "Error fetching users" },
      { status: 500 },
    );
  }
}

export async function POST(request: Request) {
  try {
    const userData = await request.json();
    const response = await axios.post(
      `${process.env.WHITEBOOK_API_URL_V2}/users`,
      userData,
      {
        headers: {
          "X-WHITEBOOK-API-Key": process.env.WHITEBOOK_ADMIN_API_KEY,
          "Content-Type": "application/json",
        },
      },
    );
    return NextResponse.json(response.data, { status: 201 });
  } catch (error) {
    console.error("Error adding user:", error);
    return NextResponse.json({ error: "Error adding user" }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const userData = await request.json();
    const { id, ...updateData } = userData;
    const response = await axios.put(
      `${process.env.WHITEBOOK_API_URL_V2}/users/${id}`,
      updateData,
      {
        headers: {
          "X-WHITEBOOK-API-Key": process.env.WHITEBOOK_ADMIN_API_KEY,
          "Content-Type": "application/json",
        },
      },
    );
    return NextResponse.json(response.data, { status: 200 });
  } catch (error) {
    console.error("Error updating user:", error);
    return NextResponse.json({ error: "Error updating user" }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    const response = await axios.delete(
      `${process.env.WHITEBOOK_API_URL_V2}/users/${id}`,
      {
        headers: {
          "X-WHITEBOOK-API-Key": process.env.WHITEBOOK_ADMIN_API_KEY,
          "Content-Type": "application/json",
        },
      },
    );
    return NextResponse.json(
      { message: "User deleted successfully" },
      { status: 200 },
    );
  } catch (error) {
    console.error("Error deleting user:", error);
    return NextResponse.json({ error: "Error deleting user" }, { status: 500 });
  }
}
