import React from 'react';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTrash, faSpinner, faCheckCircle, faExclamationCircle } from "@fortawesome/free-solid-svg-icons";
import { ProductImageSectionProps } from '@/types/product/modal';
import { getImageUrl } from '@/utils/productUtils';

const ProductImageSection: React.FC<ProductImageSectionProps> = ({
  imageUploads,
  onImageUpload,
  onRemoveImage,
  isEditing,
  isSubmitting = false
}) => {
  if (!isEditing) {
    return (
      <div className="col-span-2 p-4 border rounded bg-gray-50">
        <p className="text-sm text-gray-600">
          <strong>Note:</strong> You'll be able to add images after creating the product.
        </p>
      </div>
    );
  }

  return (
    <div className="col-span-2">
      <label className="mb-1 block text-sm font-medium">Images Upload</label>
      <input
        type="file"
        multiple
        accept="image/*"
        onChange={onImageUpload}
        disabled={isSubmitting}
        className={`mb-2 w-full rounded border border-gray-300 p-2 focus:border-wbv-theme focus:outline-none focus:ring-2 focus:ring-wbv-theme ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
      />
      {/* Display Upload Status/Preview */}
      <div className="mt-2 grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4">
        {imageUploads.map((upload) => (
          <div
            key={upload._localId}
            className={`relative rounded border p-2 flex flex-col ${upload.markedForDeletion ? 'border-red-300 bg-red-50' : ''}`}
          >
            <div className="aspect-square overflow-hidden rounded mb-2">
              {upload.originalFile && (
                <img
                  src={URL.createObjectURL(upload.originalFile)}
                  alt={upload.name}
                  className={`h-full w-full object-cover ${upload.markedForDeletion ? 'opacity-40' : ''}`}
                  onLoad={(e) => URL.revokeObjectURL((e.target as HTMLImageElement).src)}
                />
              )}
              {!upload.originalFile && upload.path && (
                <img
                  src={getImageUrl(upload.path)}
                  alt={upload.name}
                  className={`h-full w-full object-cover ${upload.markedForDeletion ? 'opacity-40' : ''}`}
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/images/placeholder.png';
                  }}
                />
              )}
              {upload.status === 'uploading' && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                  <FontAwesomeIcon icon={faSpinner} spin className="text-white text-2xl" />
                </div>
              )}
              {upload.markedForDeletion && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="bg-red-100 text-red-700 px-2 py-1 rounded text-xs font-medium">
                    Will be deleted on save
                  </div>
                </div>
              )}
            </div>

            <div className="flex items-center justify-between">
              <span className="truncate text-xs" title={upload.name}>
                {upload.name.length > 15 ? upload.name.substring(0, 12) + '...' : upload.name}
              </span>
              <div className="flex items-center">
                {upload.status === 'success' && !upload.markedForDeletion && <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mr-1" />}
                {upload.status === 'error' && <FontAwesomeIcon icon={faExclamationCircle} className="text-red-500 mr-1" title={upload.errorMessage} />}
                <button
                  type="button"
                  onClick={() => onRemoveImage(upload._localId)}
                  className={`${upload.markedForDeletion ? 'text-blue-500 hover:text-blue-700' : 'text-red-500 hover:text-red-700'} ${(upload.status === 'uploading' || isSubmitting) ? 'opacity-50 cursor-not-allowed' : ''}`}
                  title={upload.markedForDeletion ? "Restore Image" : "Remove Image"}
                  disabled={upload.status === 'uploading' || isSubmitting}
                >
                  <FontAwesomeIcon icon={upload.markedForDeletion ? faCheckCircle : faTrash} />
                </button>
              </div>
            </div>
          </div>
        ))}

        {imageUploads.length === 0 && (
          <div className="col-span-full text-sm text-gray-500 italic p-4 border rounded">
            No images uploaded yet. Click "Choose File" to add images.
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductImageSection;
