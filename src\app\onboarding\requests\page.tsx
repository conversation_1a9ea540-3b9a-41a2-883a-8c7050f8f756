"use client";

import DefaultLayout from "@/components/Layouts/DefaultLayout";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import React, { useEffect, useState } from "react";
import OnboardingOrganizations from "@/components/Organizations/OnboardingOrganizations";

interface Organization {
    id: number;
    name: string;
    email: string;
    phone_number: string;
    status: string;
    tax_id: string;
}

const OrganizationsPage: React.FC = () => {
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState<any>(null);
    const [organizations, setOrganizations] = useState<Organization[]>([]);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await fetch("/api/organizations", { method: "GET" });
                if (!response.ok) {
                    setError(`HTTP error! status: ${response.status}`);
                }
                const result = await response.json();
                setOrganizations(result.data);
            } catch (error) {
                setError(error);
                console.error("Error fetching organizations:", error);
            } finally {
                setLoading(false); // Update loading state
            }
        };

        fetchData();
    }, []); // Empty dependency array means this effect runs once after the initial render

    return (
        <DefaultLayout>
            <Breadcrumb pageName="Onboarding" />
            <OnboardingOrganizations
                organizations={organizations}
                loading={loading}
                error={error}
            />
        </DefaultLayout>
    );
};

export default OrganizationsPage;
