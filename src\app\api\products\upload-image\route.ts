import { NextRequest, NextResponse } from "next/server";
import path from "path";
import * as Minio from "minio"; // Import MinIO client

// --- MinIO Client Configuration ---
const minioClient = new Minio.Client({
    endPoint: process.env.S3_ENDPOINT || "", // Ensure fallback or error handling if missing
    port: parseInt(process.env.S3_PORT || "443", 10),
    useSSL: process.env.S3_USE_SSL === "true",
    accessKey: process.env.S3_ACCESS_KEY || "", // Ensure fallback or error handling
    secretKey: process.env.S3_SECRET_KEY || "", // Ensure fallback or error handling
});
// --- End MinIO Client Configuration ---

export async function POST(req: NextRequest) {
  // Check if MinIO client is configured properly (basic check)
  if (!process.env.S3_ENDPOINT || !process.env.S3_ACCESS_KEY || !process.env.S3_SECRET_KEY) {
      console.error("MinIO environment variables are not fully configured.");
      return NextResponse.json({ error: "Server configuration error for image upload." }, { status: 500 });
  }

  try {
    const formData = await req.formData();
    const file = formData.get("image") as File | null;
    const productId = formData.get("productId") as string | null;

    if (!file) {
      return NextResponse.json({ error: "No image file provided." }, { status: 400 });
    }

    // 1. Generate unique filename
    const uniqueFilename = `${Date.now()}-${file.name.replace(/[^a-zA-Z0-9.]/g, '_')}`;

    // 2. Determine Bucket and Path using .env variables
    const bucketName = process.env.S3_BUCKET || "whitebook"; // Use bucket from .env
    const basePath = process.env.S3_PRODUCT_IMAGES_PATH || "product_images"; // Use path from .env
    const objectPath = productId
        ? `${basePath}/${productId}/${uniqueFilename}`
        : `${basePath}/temp/${uniqueFilename}`; // Upload to temp if no productId yet

    console.log(`Attempting to upload to MinIO: Bucket: ${bucketName}, Path: ${objectPath}`);

    // 3. Convert file buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // 4. ** Actual MinIO Upload Call **
    const metaData = { 'Content-Type': file.type };
    await minioClient.putObject(bucketName, objectPath, buffer, file.size, metaData);
    console.log(`Successfully uploaded ${objectPath} to bucket ${bucketName}`);
    // --- End MinIO Upload Call ---

    // 5. Construct response aligned with backend expectation
    const fileExtension = path.extname(file.name);

    // Extract the unique name without extension
    const uniqueNameWithoutExt = uniqueFilename.substring(0, uniqueFilename.lastIndexOf('.'));

    const responseData = {
      name: uniqueNameWithoutExt,  // Use unique name without extension as the 'name'
      type: fileExtension,         // Correctly identifies type
      is_default: false,           // Default set here, will be adjusted by frontend logic
      file: uniqueFilename,        // Use unique filename with extension for the 'file' field
      path: objectPath,            // Use the actual MinIO object path (incl. unique name)
    };

    console.log("Returning Upload Response Data:", responseData);
    return NextResponse.json(responseData, { status: 201 });

  } catch (error: unknown) {
    // Type-safe error handling
    let errorMessage = "Unknown error";
    if (error && typeof error === "object") {
      if ("code" in error && typeof (error as { code?: string }).code === "string") {
        errorMessage = `MinIO Error (${(error as { code: string }).code})`;
      } else if ("message" in error && typeof (error as { message?: string }).message === "string") {
        errorMessage = (error as { message: string }).message;
      }
    }
    console.error("Error during image upload process:", error);
    return NextResponse.json(
      { error: `Image upload failed: ${errorMessage}` },
      { status: 500 }
    );
  }
}