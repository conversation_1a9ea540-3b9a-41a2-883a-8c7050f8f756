import { NextRequest, NextResponse } from "next/server";
import { v2EngineAxiosInstance, APIErrorResponse } from "@/utils/axios";

// GET /api/products - Fetch all products
export async function GET(req: NextRequest) {
  try {
    const response = await v2EngineAxiosInstance.get("/products");
    return NextResponse.json(response.data);
  } catch (error) {
    console.error("Error fetching products:", error);
    return NextResponse.json(
      { error: "Error fetching products" },
      { status: 500 },
    );
  }
}

// POST /api/products - Create a new product
export async function POST(req: NextRequest) {
  let requestBody = {}; // Variable to hold the parsed body for logging
  try {
    requestBody = await req.json() as Record<string, unknown>;
    console.log("--- Received Product Payload in API Route ---");
    console.log(JSON.stringify(requestBody, null, 2));
    console.log("---------------------------------------------");

    // Explicitly set Content-Type header for the request to V2 engine
    const response = await v2EngineAxiosInstance.post("/products", requestBody, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return NextResponse.json(response.data, { status: 201 }); // Use 201 for successful creation
  } catch (error: unknown) {
    const err = error as APIErrorResponse;
    console.error("--- Error During V2 Engine Request ---");
    console.error("Request Body Sent:", JSON.stringify(requestBody, null, 2)); // Log body again on error
    console.error(
        "V2 Engine Error Response:", 
        err.response?.status, 
        err.response?.data || err.response?.message || err
    );
    console.error("-----------------------------------");
    
    // Extract status and message for the frontend response
    const status = err.response?.status || 500;
    const message =
      (typeof err.response?.data?.message === "string" && err.response.data?.message) ||
      (typeof err.response?.data?.error === "string" && err.response.data?.error) ||
      (status === 400 ? "Bad Request to V2 engine" : "Error creating product");
    const details = err.response?.data?.errors;

    return NextResponse.json(
      { error: message, details: details }, 
      { status: status }, 
    );
  }
}
