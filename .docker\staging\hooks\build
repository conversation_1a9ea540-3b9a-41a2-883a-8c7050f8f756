#!/bin/bash
# hooks/build
# https://docs.docker.com/docker-cloud/builds/advanced/

echo "[***] Build hook running..."
# Source environment variables
source gh_source

# Define a custom error handler function
error_handler() {
  echo "An error occurred. Failed to build Docker image"
  echo "Changing status of deployment to failed"

  # Mark deploy as failed
  failure_content=$(<failure.json)

  curl -sSL \
    -X POST \
    -H "Accept: application/vnd.github+json" \
    -H "Authorization: Bearer $GH_TOKEN" \
    -H "X-GitHub-Api-Version: 2022-11-28" \
    https://api.github.com/repos/$GH_OWNER/$GH_REPO/deployments/$GH_DEPLOYMENT_ID/statuses \
    -d "$failure_content"

  exit 1
}

# Set the custom error handler to be triggered on errors
trap 'error_handler' ERR

in_progress_content=$(<in_progress.json)

curl -sSL \
  -X POST \
  -H "Accept: application/vnd.github+json" \
  -H "Authorization: Bearer $GH_TOKEN" \
  -H "X-GitHub-Api-Version: 2022-11-28" \
  https://api.github.com/repos/$GH_OWNER/$GH_REPO/deployments/$GH_DEPLOYMENT_ID/statuses \
  -d "$in_progress_content"

COMMIT_SHA=$(git rev-parse HEAD)
RELEASE_SEMVER=$(git describe --tags --always)

echo -e "-- Variables --\nCOMMIT_SHA=$COMMIT_SHA\nRELEASE_SEMVER=$RELEASE_SEMVER\nIMAGE_NAME=$IMAGE_NAME"

docker build \
  --build-arg DOTENV_KEY=$DOTENV_KEY_STAGING \
  --build-arg NODE_ENV=test \
  --build-arg COMMIT_SHA=$COMMIT_SHA \
  --build-arg RELEASE_SEMVER=$RELEASE_SEMVER \
  -f Dockerfile \
  -t $IMAGE_NAME ../../
