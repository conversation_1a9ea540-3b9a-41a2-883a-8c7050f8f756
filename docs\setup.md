# Setup

The admin site consumes APIs from the WhitebookValue Engine and as such should be developed in tandem with it to avoid accidentally 'polluting' the staging or production instances of the system.

If you are **Absolutely** sure about what you are doing, you may point the site to the `staging` instance and test your changes. To do so simply change the development `.env` file.

Since the project is very similar (type script, node js webapp) to the WhibookValue UI, you can follow the [Setup instructions](https://github.com/kualatechzm/whitebook-value-ui/blob/develop/docs/Setup.md) for the whitebook to set this project up and be ready for development

### Recommended IDE Setup

[VSCode](https://code.visualstudio.com/)

### Project Setup

Check the package.json file for dependencies and devDependencies

After setting up the development environment and getting comfortable with the dependencies, run the following command to install the dependencies.

```sh
npm install
```

### Run Tests (Unit & Integration Testing)

Run the following command to run automated unit tests

```sh
npm run test
```

### Lint with [ESLint](https://eslint.org/)

The project uses `ESLint` to check code and find problems in it

```sh
npm run lint
```
