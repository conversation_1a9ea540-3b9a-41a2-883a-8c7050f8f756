name: Bump alpha version on dev branch
on:
  workflow_dispatch:
  push:
    branches:
      - dev

permissions:
  actions: write
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  prepare:
    name: get tags
    runs-on: ubuntu-latest
    outputs:
      head_tag: ${{ steps.check.outputs.head_tag }}
    steps:
      - name: checkout
        uses: actions/checkout@v3

      - name: retrieve tags
        run: git fetch --depth=1 origin +refs/tags/*:refs/tags/*

      - name: set output variables
        id: check
        # Check if the push is a tag or if it's from another repo
        run: |
          tag=""
          if [[ "${{ github.ref }}" == refs/heads/* ]]; then
            tag="$(git tag --points-at HEAD)"
          fi
          echo "head_tag=${tag}" >> $GITHUB_OUTPUT

      - name: workflow initiator
        run: |
          echo "Event: ${{ github.event_name }}"
          echo "Branch or tag: ${{ github.ref_name }}"
          echo "Actor: ${{ github.actor }}"

  calculate-version:
    name: get gitversion
    runs-on: ubuntu-latest
    needs: prepare
    if: github.event_name == 'push' && needs.prepare.outputs.head_tag == ''
    outputs:
      semVer: ${{ steps.gitversion.outputs.semVer }}
      fullSemVer: ${{ steps.gitversion.outputs.fullSemVer }}
      sha: ${{ steps.gitversion.outputs.sha }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: install gitversion
        uses: gittools/actions/gitversion/setup@v3.1.1
        with:
          versionSpec: '6.0.x'

      - name: run gitversion
        id: gitversion
        uses: gittools/actions/gitversion/execute@v3.1.1
        with:
          useConfigFile: true

  bump-alpha-version:
    name: bump alpha version
    runs-on: ubuntu-latest
    needs: [prepare, calculate-version]
    if:
      github.event_name == 'push' && needs.prepare.outputs.has_tag == '0' &&
      needs.prepare.outputs.head_tag != needs.calculate-version.outputs.semVer
    permissions:
      contents: write
    steps:
      - name: checkout repo
        uses: actions/checkout@v3

      - name: setup node
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: bump alpha version to ${{ env.SEM_VER }}
        env:
          SEM_VER: ${{ needs.calculate-version.outputs.semVer }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          FILE_TO_COMMIT: package.json
          DESTINATION_BRANCH: ${{ github.ref }}
          REPO: ${{ github.repository }}
          REPO_OWNER: ${{ github.repository_owner }}
          REPO_NAME: ${{ github.event.repository.name }}
        run: |
          echo $SEM_VER > version
          git add version

      - name: commit changes
        env:
          FULL_SEM_VER: ${{ needs.calculate-version.outputs.fullSemVer }}
          SEM_VER: ${{ needs.calculate-version.outputs.semVer }}
        run: |
          git config --local user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"
          git status
          git commit -am "chore(version): bump version to $SEM_VER"
          git tag -a -m "Version: $FULL_SEM_VER" "v$SEM_VER"
          git push && git push --tags
