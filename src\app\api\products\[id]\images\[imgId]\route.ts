import { NextRequest, NextResponse } from "next/server";
import { v2EngineAxiosInstance, APIErrorResponse } from "@/utils/axios";

// DELETE /api/products/[id]/images/[imgId] - Delete a product image
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string; imgId: string } },
) {
  const { id, imgId } = params;
  
  if (isNaN(Number(id)) || isNaN(Number(imgId))) {
    return NextResponse.json({ error: "Invalid ID format" }, { status: 400 });
  }

  try {
    console.log(`Deleting image ${imgId} from product ${id}`);
    
    // Call the backend API to delete the image using the RESTful endpoint pattern
    await v2EngineAxiosInstance.delete(`/products/${id}/images/${imgId}`);
    
    // Return a success response
    return NextResponse.json(
      { success: true, message: "Image deleted successfully" },
      { status: 200 }
    );
  } catch (error: unknown) {
    const err = error as APIErrorResponse;
    console.error("--- Error During V2 Engine Request ---");
    console.error(
      "V2 Engine Error Response:",
      err.response?.status,
      err.response?.data || err.response?.message || err
    );
    console.error("-----------------------------------");
    
    // Extract status and message for the frontend response
    const status = err.response?.status || 500;
    const message =
      (err.response?.data?.message as string) ||
      (err.response?.data?.error as string) ||
      (err.response?.message as string) ||
      (status === 400 ? "Bad Request to V2 engine" : "Error deleting product image");
    
    return NextResponse.json(
      { error: message },
      { status }
    );
  }
}
