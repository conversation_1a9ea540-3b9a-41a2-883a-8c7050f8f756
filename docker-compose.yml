services:
  admin:
    container_name: admin
    build: .
    network_mode: host
    env_file: 
      - path: .env
        required: true
      - path: .env.vault
        required: true
    ports:
      - ${PORT}:${PORT}
    volumes:
      - .:/app
      - /app/node_modules
    command: npm run dev
  admin-cache:
    container_name: admin-cache
    network_mode: host
    image: redis:7.2.7-alpine
    restart: unless-stopped
    ports:
      - '6379:6379'
    healthcheck:
      test: ["CMD-SHELL", "redis-cli ping | grep PONG"]
      interval: 1s
      timeout: 3s
      retries: 5
    command: ["redis-server"]
    volumes: 
      - redis-cache:/data

volumes:
  redis-cache:
    driver: local
