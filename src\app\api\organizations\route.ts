import { NextRequest, NextResponse } from "next/server";
import { v2EngineAxiosInstance } from "@/utils/axios";

export async function GET(req: NextRequest) {
  try {
    const response = await v2EngineAxiosInstance.get("/organizations");
    return NextResponse.json(response.data);
  } catch (error) {
    console.error("Error fetching organizations:", error);
    return NextResponse.json(
      { error: "Error fetching organizations" },
      { status: 500 },
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const response = await v2EngineAxiosInstance.post("/organizations", body);
    return NextResponse.json(response.data, { status: 201 });
  } catch (error) {
    console.error("Error creating organization:", error);
    return NextResponse.json(
      { error: "Error creating organization" },
      { status: 500 },
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    const body = await req.json();
    const { id, ...updateData } = body;
    const response = await v2EngineAxiosInstance.put(
      `/organization/${id}`,
      updateData,
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    );
    return NextResponse.json(response.data);
  } catch (error) {
    console.error("Error updating organization:", error);
    return NextResponse.json(
      { error: "Error updating organization" },
      { status: 500 },
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { id } = await req.json();
    const response = await v2EngineAxiosInstance.delete(`/organizations/${id}`);
    return NextResponse.json({ message: "Organization deleted successfully" });
  } catch (error) {
    console.error("Error deleting organization:", error);
    return NextResponse.json(
      { error: "Error deleting organization" },
      { status: 500 },
    );
  }
}
