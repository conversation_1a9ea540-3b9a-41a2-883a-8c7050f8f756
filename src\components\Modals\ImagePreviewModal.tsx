import React, { useState, useEffect, useRef, useCallback } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faXmark,
  faChevronLeft,
  faChevronRight,
  faSearchPlus,
  faSearchMinus,
  faRotateRight
} from "@fortawesome/free-solid-svg-icons";

interface ImagePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  imagePath: string;
  productName: string;
  allImages: string[];
  currentIndex: number;
  onNavigate: (newIndex: number) => void;
}

// Helper function to properly construct image URLs
const getImageUrl = (path: string): string => {
  if (!path) return '/images/placeholder.png';

  // Remove any leading/trailing slashes and clean the path
  const cleanPath = path.trim().replace(/^\/+|\/+$/g, '');

  // If the path is empty after cleaning, return placeholder
  if (!cleanPath) return '/images/placeholder.png';

  return `/api/products/view/${cleanPath}`;
};

const ImagePreviewModal: React.FC<ImagePreviewModalProps> = ({
  isOpen,
  onClose,
  imagePath,
  productName,
  allImages,
  currentIndex,
  onNavigate
}) => {
  // Move hooks outside of conditional
  const [zoomLevel, setZoomLevel] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [rotation, setRotation] = useState(0);
  const imageRef = useRef<HTMLImageElement>(null);

  

  // Reset zoom and position when image changes
  useEffect(() => {
    setZoomLevel(1);
    setPosition({ x: 0, y: 0 });
    setRotation(0);
  }, [imagePath]);

  const handleZoomIn = useCallback(() => {
    setZoomLevel(prev => Math.min(prev + 0.25, 3)); // Max zoom 3x
  }, []);

  const handleZoomOut = useCallback(() => {
    setZoomLevel(prev => Math.max(prev - 0.25, 0.5)); // Min zoom 0.5x
  }, []);

  const handleRotate = useCallback(() => {
    setRotation(prev => (prev + 90) % 360);
  }, []);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (zoomLevel > 1) {
      setIsDragging(true);
      setDragStart({ x: e.clientX - position.x, y: e.clientY - position.y });
    }
  }, [zoomLevel, position]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isDragging && zoomLevel > 1) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  }, [isDragging, zoomLevel, dragStart]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (zoomLevel > 1 && e.touches.length === 1) {
      setIsDragging(true);
      setDragStart({
        x: e.touches[0].clientX - position.x,
        y: e.touches[0].clientY - position.y
      });
    }
  }, [zoomLevel, position]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (isDragging && zoomLevel > 1 && e.touches.length === 1) {
      e.preventDefault(); // Prevent scrolling when dragging
      setPosition({
        x: e.touches[0].clientX - dragStart.x,
        y: e.touches[0].clientY - dragStart.y
      });
    }
  }, [isDragging, zoomLevel, dragStart]);

  const handleTouchEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  const handlePrevious = useCallback(() => {
    const newIndex = currentIndex > 0 ? currentIndex - 1 : allImages.length - 1;
    onNavigate(newIndex);
  }, [currentIndex, allImages.length, onNavigate]);

  const handleNext = useCallback(() => {
    const newIndex = currentIndex < allImages.length - 1 ? currentIndex + 1 : 0;
    onNavigate(newIndex);
  }, [currentIndex, allImages.length, onNavigate]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowLeft':
          handlePrevious();
          break;
        case 'ArrowRight':
          handleNext();
          break;
        case 'Escape':
          onClose();
          break;
        case '+':
          handleZoomIn();
          break;
        case '-':
          handleZoomOut();
          break;
        case 'r':
          handleRotate();
          break;
        default:
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, handlePrevious, handleNext, onClose, handleZoomIn, handleZoomOut, handleRotate]);

  // Early return after hooks
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-90 p-4">
      {/* Close button */}
      <button
        className="absolute right-4 top-4 z-10 rounded-full bg-black bg-opacity-50 p-2 text-white hover:bg-opacity-70"
        onClick={onClose}
      >
        <FontAwesomeIcon icon={faXmark} className="text-xl" />
      </button>

      {/* Image container */}
      <div
        className="relative h-full w-full max-w-6xl overflow-hidden flex items-center justify-center"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        style={{ cursor: zoomLevel > 1 ? 'grab' : 'default' }}
      >
        <img
          ref={imageRef}
          src={getImageUrl(imagePath)}
          alt={`${productName} - Image ${currentIndex + 1}`}
          className="max-h-[80vh] max-w-full object-contain transition-transform"
          style={{
            transform: `translate(${position.x}px, ${position.y}px) scale(${zoomLevel}) rotate(${rotation}deg)`,
            transformOrigin: 'center',
            transition: isDragging ? 'none' : 'transform 0.2s ease-out'
          }}
          onError={(e) => {
            console.error(`Failed to load image: ${imagePath}`);
            (e.target as HTMLImageElement).src = '/images/placeholder.png';
          }}
        />
      </div>

      {/* Navigation buttons */}
      {allImages.length > 1 && (
        <>
          <button
            className="absolute left-4 top-1/2 z-10 -translate-y-1/2 rounded-full bg-black bg-opacity-50 p-3 text-white hover:bg-opacity-70"
            onClick={handlePrevious}
          >
            <FontAwesomeIcon icon={faChevronLeft} className="text-xl" />
          </button>
          <button
            className="absolute right-4 top-1/2 z-10 -translate-y-1/2 rounded-full bg-black bg-opacity-50 p-3 text-white hover:bg-opacity-70"
            onClick={handleNext}
          >
            <FontAwesomeIcon icon={faChevronRight} className="text-xl" />
          </button>
        </>
      )}

      {/* Image counter */}
      <div className="absolute bottom-4 left-1/2 z-10 -translate-x-1/2 rounded-full bg-black bg-opacity-50 px-4 py-2 text-white">
        {currentIndex + 1} / {allImages.length}
      </div>

      {/* Controls */}
      <div className="absolute bottom-4 right-4 z-10 flex space-x-2">
        <button
          className="rounded-full bg-black bg-opacity-50 p-3 text-white hover:bg-opacity-70"
          onClick={handleZoomIn}
          title="Zoom In"
        >
          <FontAwesomeIcon icon={faSearchPlus} />
        </button>
        <button
          className="rounded-full bg-black bg-opacity-50 p-3 text-white hover:bg-opacity-70"
          onClick={handleZoomOut}
          title="Zoom Out"
        >
          <FontAwesomeIcon icon={faSearchMinus} />
        </button>
        <button
          className="rounded-full bg-black bg-opacity-50 p-3 text-white hover:bg-opacity-70"
          onClick={handleRotate}
          title="Rotate"
        >
          <FontAwesomeIcon icon={faRotateRight} />
        </button>
      </div>
    </div>
  );
};

export default ImagePreviewModal;
