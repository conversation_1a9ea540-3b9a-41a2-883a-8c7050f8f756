export const getPageSizeOptions = (data: any[]) => {
  const totalRows = data?.length;
  let pageSizeOptions: number[] = [];
  if (totalRows >= 5 && totalRows < 10) {
    pageSizeOptions = [5, totalRows];
    if (totalRows == 5) {
      pageSizeOptions.pop();
    }
  }
  if (totalRows >= 10 && totalRows < 20) {
    pageSizeOptions = [5, 10, totalRows];
    if (totalRows == 10) {
      pageSizeOptions.pop();
    }
  }
  if (totalRows >= 20 && totalRows < 30) {
    pageSizeOptions = [5, 10, 20, totalRows];
    if (totalRows == 20) {
      pageSizeOptions.pop();
    }
  }
  if (totalRows >= 30 && totalRows < 40) {
    pageSizeOptions = [5, 10, 20, 30, totalRows];
    if (totalRows == 30) {
      pageSizeOptions.pop();
    }
  }
  if (totalRows >= 40 && totalRows < 50) {
    pageSizeOptions = [5, 10, 20, 30, 40, totalRows];
    if (totalRows == 40) {
      pageSizeOptions.pop();
    }
  }
  if (totalRows >= 50) {
    pageSizeOptions = [5, 10, 20, 30, 40, 50];
    if (totalRows == 50) {
      pageSizeOptions.pop();
    }
  }
  return pageSizeOptions;
};
