import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faFilePdf, faWallet } from "@fortawesome/free-solid-svg-icons";
import { formatCurrency, formatDateTime } from "@/components/utils/utils";
import { InvoiceStruct } from "@/schemas/billingSchema";
import Link from "next/link";
import InvoicePDF from "./InvoicePDF";
import { PDFDownloadLink } from "@react-pdf/renderer";
import ConfirmPaymentModal from "@/components/Modals/ConfirmPaymentModal";

interface InvoiceProps {
  invoice: InvoiceStruct;
  loading: boolean;
  error: string | null;
  handleInvoicePayment: () => void;
  isConfirmPaymentModalOpen: boolean;
  setIsConfirmPaymentModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const Invoice: React.FC<InvoiceProps> = ({
  invoice,
  loading,
  error,
  handleInvoicePayment,
  isConfirmPaymentModalOpen,
  setIsConfirmPaymentModalOpen,
}) => {
  if (loading) return <div>Loading...</div>;
  if (error) return <div>{error}</div>;

  return (
    <div className="pb-6 pt-4">
      <div className="mb-4 flex items-center justify-between">
        <h1 className="whitespace-nowrap text-3xl font-bold">
          Invoice No: {invoice?.id}&nbsp;
          <span
            className={`rounded-full px-2 py-1 text-xs font-semibold text-gray-100 ${invoice?.status === "overdue"
              ? "bg-red-500"
              : invoice?.status === "pending"
                ? "bg-yellow-500"
                : "bg-wbv-theme"
              }`}
          >
            {invoice?.status.charAt(0).toUpperCase() + invoice?.status.slice(1)}
          </span>
        </h1>
        <Link href="/billing" className="text-blue-500 hover:underline">
          Back to Billing
        </Link>
      </div>
      <div className="mx-auto max-w-xl rounded-lg bg-white px-8 py-10 shadow-lg">
        <div className="mb-8 flex items-center justify-between">
          <div className="flex items-center">
            <img
              className="mr-2 h-16"
              src={"/images/logo/Kuala-Horizontal-Black.png"}
              alt="Logo"
            />
          </div>
          <div className="text-right text-gray-700">
            <div className="mb-2 text-xl font-bold">INVOICE</div>
            <div className="text-sm">
              Date:{" "}
              {invoice?.created_at ? formatDateTime(invoice.created_at) : ""}
            </div>
            <div className="text-sm">Invoice No: {invoice?.id}</div>
          </div>
        </div>
        <div className="mb-8 border-b-2 border-gray-300 pb-8">
          <h2 className="mb-4 text-2xl font-bold">Bill To:</h2>
          {/* Billing Address */}
          <div className="mb-2 text-gray-700">{invoice?.address.name}</div>
          <div className="mb-2 text-gray-700">{invoice?.address.street}</div>
          <div className="mb-2 text-gray-700">
            {invoice?.address.city}, {invoice?.address.state},{" "}
            {invoice?.address.country}
          </div>
          <div className="text-gray-700">{invoice?.contact_email}</div>
        </div>
        <table className="mb-8 w-full border-b-2 border-gray-300 text-left">
          <thead>
            <tr>
              <th className="py-2 font-bold uppercase text-gray-700">
                Description
              </th>
              <th className="py-2 font-bold uppercase text-gray-700">
                Quantity
              </th>
              <th className="py-2 font-bold uppercase text-gray-700">Price</th>
            </tr>
          </thead>
          <tbody>
            <tr key={invoice.id}>
              <td className="py-4 text-gray-700">FMV Transactions</td>
              <td className="py-4 text-gray-700">
                {invoice?.summary.total_api_calls}
              </td>
              <td className="py-4 text-gray-700">
                {formatCurrency(invoice?.summary.total_billed, "ZMW")}
              </td>
            </tr>
          </tbody>
        </table>
        <div className="mb-8 flex justify-end">
          <div className="mr-8 text-gray-700">
            <div className="mb-2">Subtotal:</div>
            <div className="text-xl font-bold">Total:</div>
          </div>
          <div className="text-right text-gray-700">
            <div className="mb-2">
              {formatCurrency(invoice?.subtotal, "ZMW")}
            </div>
            <div className="text-xl font-bold">
              {formatCurrency(invoice?.total, "ZMW")}
            </div>
          </div>
        </div>
        {invoice?.status === "paid" ? (
          <>
            <div className="mb-8 text-center text-2xl font-bold text-green-600">
              PAID
            </div>
            <PDFDownloadLink
              document={<InvoicePDF invoiceId={invoice?.id} />}
              fileName={`invoice-${invoice?.id}.pdf`}
              className="text-black hover:opacity-90"
              title="Download Invoice PDF"
            >
              <button className="w-full rounded-lg bg-black px-3 py-3 font-bold text-white hover:bg-opacity-90">
                <span>
                  <FontAwesomeIcon className="px-2" icon={faFilePdf} />
                </span>
                Download
              </button>
            </PDFDownloadLink>
          </>
        ) : (
          <>
            <div className="mb-8 border-t-2 border-gray-300 pt-8">
              <div className="mb-2 text-gray-700">
                Payment is due within 30 days. Late payments are subject to
                fees.
              </div>
              <div className="mb-2 text-gray-700">
                Please make checks payable to Kuala Tech Limited and mail to:
              </div>
              <div className="text-gray-700">
                Unit 8D, 1st Floor, Pangaea Office Park Plot No. 2374, Off Great
                East Road, Lusaka, Zambia.
                <span className="font-bold"> Email: </span>
                <EMAIL>
              </div>
            </div>
            <button
              className="my-2 w-full rounded-lg bg-wbv-theme px-3 py-3 font-bold text-white hover:bg-opacity-90"
              onClick={() => setIsConfirmPaymentModalOpen(true)}
            >
              <span>
                <FontAwesomeIcon className="px-2" icon={faWallet} />
              </span>
              Pay Now
            </button>
            <PDFDownloadLink
              document={<InvoicePDF invoiceId={invoice?.id} />}
              fileName={`invoice-${invoice?.id}.pdf`}
              className="text-black hover:opacity-90"
              title="Download Invoice PDF"
            >
              <button className="w-full rounded-lg bg-black px-3 py-3 font-bold text-white hover:bg-opacity-90">
                <span>
                  <FontAwesomeIcon className="px-2" icon={faFilePdf} />
                </span>
                Download
              </button>
            </PDFDownloadLink>
          </>
        )}
      </div>
      {/* Confirm Cancellation Modal */}
      <ConfirmPaymentModal
        isOpen={isConfirmPaymentModalOpen}
        onClose={() => setIsConfirmPaymentModalOpen(false)}
        onConfirm={handleInvoicePayment}
        title={"Confirm Payment"}
        message={`Are you sure you want to proceed with the payment for invoice <strong>#${invoice?.id}?</strong>`}
        cta={"Proceed"}
        ctaBtnColor={"theme"}
      />
    </div>
  );
};

export default Invoice;
