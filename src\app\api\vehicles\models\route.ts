import { NextRequest, NextResponse } from "next/server";
import { v2EngineAxiosInstance } from "@/utils/axios";

export async function GET(req: NextRequest) {
  try {
    const response = await v2EngineAxiosInstance.get("/models");
    return NextResponse.json(response.data);
  } catch (error) {
    console.error("Error fetching models:", error);
    return NextResponse.json(
      { error: "Error fetching models" },
      { status: 500 },
    );
  }
}
