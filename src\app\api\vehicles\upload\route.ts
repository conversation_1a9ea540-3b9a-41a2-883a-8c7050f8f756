import { v2EngineAxiosInstance } from "@/utils/axios";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData(); // Parse the incoming form data
    const file = formData.get("file");

    if (!file || typeof file === "string") {
      return NextResponse.json(
        { error: "No file uploaded or invalid file data" },
        { status: 400 },
      );
    }

    // Prepare the file for sending to the backend
    const axiosFormData = new FormData();
    axiosFormData.append("file", file);

    // Send file to the Gin backend
    const response = await v2EngineAxiosInstance.post("/upload", axiosFormData);

    // Return the backend's response
    return NextResponse.json(response.data, { status: response.status });
  } catch (error) {
    console.error("Error uploading file:", error);

    return NextResponse.json(
      { error: "Error uploading file" },
      { status: 500 },
    );
  }
}
