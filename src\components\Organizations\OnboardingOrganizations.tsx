import React from "react";
import { createColumnHelper, SortingState } from "@tanstack/react-table";
import "@/css/table.css";
import DebouncedSearchInput from "@/components/Inputs/DebouncedSearchInput";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faMagnifyingGlass } from "@fortawesome/free-solid-svg-icons";
import TanStackTable from "@/components/Tables/TanStackTable";
import Link from "next/link";
import { getPageSizeOptions } from "../Tables/utils";
import { capitalizeFirstLetter } from "../utils/utils";

interface Organization {
  id: number;
  name: string;
  email: string;
  phone_number: string;
  status: string;
  tax_id: string;
}

interface OrganizationsProps {
  organizations: Organization[];
  loading: boolean;
  error: string | null;
}

const OnboardingOrganizations: React.FC<OrganizationsProps> = ({
  organizations,
  loading,
  error,
}) => {
  const [globalFilter, setGlobalFilter] = React.useState("");
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const columnHelper = createColumnHelper<Organization>();
  {/* Table Columns */ }
  const columns = [
    columnHelper.accessor("name", {
      cell: (info) => (
        <div className="max-width-cell truncated-cell">{info?.getValue()}</div>
      ),
      header: "Name",
    }),
    columnHelper.accessor("email", {
      cell: (info) => (
        <div className="max-width-cell truncated-cell">{info?.getValue()}</div>
      ),
      header: "Email",
    }),
    columnHelper.accessor("tax_id", {
      cell: (info) => <div className="max-width-cell">{info?.getValue()}</div>,
      header: "Tax ID",
    }),
    columnHelper.accessor("phone_number", {
      cell: (info) => <div className="max-width-cell">{info?.getValue()}</div>,
      header: "Phone",
    }),
    columnHelper.accessor("status", {
      cell: (info) => (
        <div className="max-width-cell">
          <span
            className={`rounded-full px-2 py-1 text-xs font-semibold ${info.getValue() === "approved"
              ? "bg-green-100 text-green-800"
              : info.getValue() === "pending" || info.getValue() === ""
                ? "bg-yellow-100 text-yellow-800"
                : "bg-red-100 text-red-800"
              }`}
          >
            {info?.getValue() === "" ? "Pending" : capitalizeFirstLetter(info?.getValue())}
          </span>
        </div>
      ),
      header: "Status",
    }),
    columnHelper.display({
      id: "actions",
      cell: (info) => (
        <div className="flex space-x-4">
          <Link
            href={`requests/${info.row.original.id}`}
            className="text-blue-500 underline hover:text-opacity-90"
            title="View"
          >
            View request
          </Link>
        </div>
      ),
      header: "Action",
    }),
  ];

  if (loading) return <div>Loading...</div>;
  if (error) return <div className="text-red-500">Error: {error}</div>;

  return (
    <div className="flex flex-col pt-4">
      <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
          {/* {Search and Add} */}
          <div className="mb-2 flex justify-between">
            <div className="flex w-full items-center gap-1">
              <FontAwesomeIcon icon={faMagnifyingGlass} />
              <DebouncedSearchInput
                value={globalFilter ?? ""}
                onChange={(value) => setGlobalFilter(String(value))}
                debounced={500}
                placeholder="Search requests..."
              />
            </div>
          </div>
          {/* Organizations Table */}
          {loading ? (
            <p>Loading...</p>
          ) : error ? (
            <p className="text-red-500">{error}</p>
          ) : (
            <div className="my-5 overflow-hidden rounded border-b border-gray-200 shadow">
              <TanStackTable
                data={organizations} // Use filtered organizations
                columns={columns}
                globalFilter={globalFilter}
                sorting={sorting}
                setGlobalFilter={setGlobalFilter}
                setSorting={setSorting}
                pageSizeOptions={getPageSizeOptions(organizations)}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OnboardingOrganizations;
