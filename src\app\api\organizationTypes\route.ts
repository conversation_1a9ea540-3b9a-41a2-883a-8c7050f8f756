import { NextRequest, NextResponse } from "next/server";
import { v2EngineAxiosInstance } from "@/utils/axios";

export async function GET(req: NextRequest) {
    try {
        const response = await v2EngineAxiosInstance.get("/organization-types");
        return NextResponse.json(response.data);
    } catch (error) {
        console.error("Error fetching organization-types:", error);
        return NextResponse.json(
            { error: "Error fetching organization-types" },
            { status: 500 },
        );
    }
}