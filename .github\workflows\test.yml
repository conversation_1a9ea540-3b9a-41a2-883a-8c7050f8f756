name: Test
on:
  push:
    branches:
      - main
      - staging
      - dev
  pull_request:
    branches:
      - main
      - staging
      - dev

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v4

      - name: detect package manager
        id: detect-package-manager
        run: |
          if [ -f "${{ github.workspace }}/yarn.lock" ]; then
            echo "manager=yarn" >> $GITHUB_OUTPUT
            echo "command=install" >> $GITHUB_OUTPUT
            echo "runner=yarn" >> $GITHUB_OUTPUT
            exit 0
          elif [ -f "${{ github.workspace }}/package.json" ]; then
            echo "manager=npm" >> $GITHUB_OUTPUT
            echo "command=ci" >> $GITHUB_OUTPUT
            echo "runner=npx --no-install" >> $GITHUB_OUTPUT
            exit 0
          else
            echo "Unable to determine package manager" >&2
            exit 1
          fi
      
      - name: setup node
        uses: actions/setup-node@v3
        with:
          node-version: "22"
          cache: ${{ steps.detect-package-manager.outputs.manager }}

      # Insure unit tests are set up to run
      # - name: run tests
      #   run: npm test 
