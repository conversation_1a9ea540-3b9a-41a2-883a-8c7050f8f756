export interface Address {
  name: string;
  street: string;
  city: string;
  state: string;
  zip_code: number;
  country: string;
  phone_number: string;
  created_at: string;
  created_by: number;
  updated_at: string;
  updated_by: number;
}

export interface PaymentResponse {
  id: number;
  amount: number;
  external_id: string;
  narration: string;
  status_code: number;
  payer_number: string;
  account_number: string;
  response_code: string;
  response_message: string;
  created_at: string;
  created_by: number;
  updated_at: string;
  updated_by: number;
}

export interface Payment {
  id: number;
  amount: number;
  type: string;
  status: string;
  currency_id: number;
  invoice_id: number | null;
  receipt_id: number;
  transaction_id: number;
  payment_response: PaymentResponse;
  created_at: string;
  created_by: number;
  updated_at: string;
  updated_by: number;
}

export interface Summary {
  total_api_calls: number;
  cost_per_api_call: number;
  total_billed: number;
  pricing_type: string;
}

export interface LineItem {
  note: string;
  price?: number;
  quantity: number;
  total: number;
}

export interface Receipt {
  id: number;
  line_items: LineItem[];
  total: number;
  currency_id: number;
  invoice_id: number;
  payment_id: number;
  created_at: string;
  created_by: number;
  updated_at: string;
  updated_by: number;
}

export interface InvoiceStruct {
  id: number;
  due_date: string;
  contact_email: string;
  address: Address;
  receipts: Receipt[];
  payments: Payment[];
  summary: Summary;
  subtotal: number;
  total: number;
  tpin: string;
  currency_id: number;
  status: string;
  notes: string[];
  created_at: string;
  created_by: number;
  updated_at: string;
  updated_by: number;
}
