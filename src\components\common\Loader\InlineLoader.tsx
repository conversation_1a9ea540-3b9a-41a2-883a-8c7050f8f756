import React from 'react';

interface InlineLoaderProps {
  size?: 'small' | 'medium' | 'large';
  message?: string;
  className?: string;
}

const InlineLoader: React.FC<InlineLoaderProps> = ({ 
  size = 'medium', 
  message = 'Loading...', 
  className = '' 
}) => {
  const sizeClasses = {
    small: 'h-6 w-6',
    medium: 'h-10 w-10',
    large: 'h-16 w-16'
  };

  return (
    <div className={`flex flex-col items-center justify-center p-8 ${className}`}>
      <div className={`${sizeClasses[size]} animate-spin rounded-full border-4 border-solid border-wbv-theme border-t-transparent mb-4`}></div>
      {message && <p className="text-gray-600 dark:text-gray-300 text-center">{message}</p>}
    </div>
  );
};

export default InlineLoader;
