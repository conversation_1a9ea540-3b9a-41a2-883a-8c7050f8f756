"use client";

import React, { useEffect, useState } from "react";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import VehicleVariants from "@/components/Vehicles/Variants/VehicleVariants";

const VehicleVariantsPage = () => {
  const [variants, setVariants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<any>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch("/api/vehicles/variants", {
          method: "GET",
        });
        // console.log("response", response);
        const data = await response.json();
        setVariants(data.data);
      } catch (error) {
        setError(error);
        console.error("Error fetching variants:", error);
      } finally {
        setLoading(false); // Update loading state
      }
    };

    fetchData();
  }, []); // Empty dependency array means this effect runs once after the initial render

  return (
    <DefaultLayout>
      <Breadcrumb pageName="Vehicle Variants" />
      <VehicleVariants variants={variants} loading={loading} error={error} />
    </DefaultLayout>
  );
};

export default VehicleVariantsPage;
