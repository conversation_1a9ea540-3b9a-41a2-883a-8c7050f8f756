import React, { useState } from "react";

interface CreateShopModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: number; // Pass user_id dynamically
  organizationId?: number | null; // Pass organization_id dynamically (optional)
  onShopCreated: (newShopId: number) => void;
}
interface ErrorResponse {
  error?: string;
}

interface ShopCreateResponse {
  id: number;
}

const CreateShopModal: React.FC<CreateShopModalProps> = ({
  isOpen,
  onClose,
  userId,
  organizationId,
  onShopCreated,
}) => {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    organization_id: organizationId || null, // Use organization_id if provided
    user_id: organizationId ? null : userId, // Set user_id to null if organization_id exists
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    try {
      const response = await fetch("/api/shops", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json() as ErrorResponse;
        throw new Error(errorData.error ?? "Failed to create shop.");
      }

      const result = await response.json() as ShopCreateResponse;
      onShopCreated(result.id); // Pass the new shop ID to the parent
      onClose(); // Close the modal on success
    } catch (err) {
      let message = "Failed to create shop.";
      if (err instanceof Error) {
        message = err.message;
        console.error("Error creating shop:", message);
      } else {
        console.error("Unknown error creating shop:", err);
      }
      setError(message);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className="w-full max-w-lg rounded-lg bg-white shadow-lg flex flex-col max-h-[90vh]">
        <h2 className="flex-shrink-0 border-b p-6 text-xl font-bold text-wbv-theme">
          Create Shop
        </h2>
        <form onSubmit={handleSubmit} className="flex flex-col px-6 py-4">
          {error && <p className="text-red-500 mb-4">{error}</p>}
          <div className="mb-4">
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              Shop Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Enter shop name"
              className="mt-1 block w-full h-[40px] rounded-md border-gray-300 p-2 shadow-sm focus:border-wbv-theme focus:ring-wbv-theme sm:text-sm"
              required
            />
          </div>
          <div className="mb-4">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Enter shop description"
              className="mt-1 block w-full p-2 rounded-md border-gray-300 shadow-sm focus:border-wbv-theme focus:ring-wbv-theme sm:text-sm"
              rows={4}
            />
          </div>
          <div className="flex justify-end space-x-2">
            <button
              type="button"
              onClick={onClose}
              disabled={isSubmitting}
              className="rounded bg-gray-500 px-4 py-2 text-white hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="rounded bg-wbv-theme px-4 py-2 text-white hover:bg-opacity-90 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? "Creating..." : "Create Shop"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateShopModal;
