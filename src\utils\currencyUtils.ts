import React from 'react';
import { Currency } from "@/types/product/index";


// Cache for currencies to avoid repeated API calls
let currencyCache: Currency[] | null = null;
let currencyFetchPromise: Promise<Currency[]> | null = null;


 //Fetches currencies from the backend API
 //Uses caching to avoid repeated API calls

export const fetchCurrencies = async (): Promise<Currency[]> => {
  // Return cached data if available
  if (currencyCache) {
    return currencyCache;
  }

  // Return existing promise if fetch is already in progress
  if (currencyFetchPromise) {
    return currencyFetchPromise;
  }

  // Create new fetch promise
  currencyFetchPromise = (async () => {
    try {
      const response = await fetch('/api/currencies');
      if (!response.ok) {
        throw new Error(`Failed to fetch currencies: ${response.status}`);
      }
      const data = await response.json() as Currency[];

      // Cache the result
      currencyCache = data;
      currencyFetchPromise = null; // Clear the promise

      return data;
    } catch (error) {
      currencyFetchPromise = null; // Clear the promise on error
      throw error;
    }
  })();

  return currencyFetchPromise;
};


 //Maps currency ID to currency code using backend data
 

export const mapCurrencyIdToString = async (id: number): Promise<string> => {
  try {
    const currencies = await fetchCurrencies();
    const currency = currencies.find(c => c.id === id);
    return currency?.code || 'ZMW';
  } catch (error) {
    console.error('Error fetching currencies for mapping:', error);
    return 'ZMW';
  }
};


// Synchronous version that uses cached data only

 
export const mapCurrencyIdToStringSync = (id: number): string => {
  if (!currencyCache) {
    return 'ZMW';
  }

  const currency = currencyCache.find(c => c.id === id);
  return currency?.code || 'ZMW';
};


export const getCurrencyById = async (id: number): Promise<Currency | null> => {
  try {
    const currencies = await fetchCurrencies();
    return currencies.find(c => c.id === id) || null;
  } catch (error) {
    console.error('Error fetching currency by ID:', error);
    return null;
  }
};

export const getAllCurrencies = async (): Promise<Currency[]> => {
  return fetchCurrencies();
};

//Clears the currency cache (useful for testing or when data needs to be refreshed)
export const clearCurrencyCache = (): void => {
  currencyCache = null;
  currencyFetchPromise = null;
};

//Checks if currencies are cached
export const isCurrencyCached = (): boolean => {
  return currencyCache !== null;
};


 // Hook-like function for React components to use currencies
  //Returns currencies, loading state, and error state

export const useCurrencies = () => {
  const [currencies, setCurrencies] = React.useState<Currency[]>([]);
  const [loading, setLoading] = React.useState<boolean>(false);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const loadCurrencies = async () => {
      // If already cached, use cached data
      if (currencyCache) {
        setCurrencies(currencyCache);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const data = await fetchCurrencies();
        setCurrencies(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch currencies');
      } finally {
        setLoading(false);
      }
    };

    loadCurrencies();
  }, []);

  return { currencies, loading, error };
};
