import { z } from "zod";

export const addOrganizationSchema = z.object({
  name: z.string().min(1, "Name is required"),
  registration_number: z.string().min(1, "Registration number is required"),
  tax_id: z.string().min(1, "Tax ID is required"),
  organization_type_id: z
    .number()
    .int()
    .positive("Organization type ID must be a positive integer"),
  parent_id: z
    .number()
    .int()
    .positive("Parent ID must be a positive integer")
    .optional(),
  email: z.string().email("Invalid email address"),
  phone_number: z.string().regex(/^\+?[1-9]\d{1,14}$/, "Invalid phone number"),
  postal_address: z.string().min(1, "Postal address is required"),
  town_id: z.number().int().positive("Town ID must be a positive integer"),
  street_address: z.string().min(1, "Street address is required"),
  created_by: z
    .number()
    .int()
    .positive("Created by must be a positive integer"),
  updated_by: z
    .number()
    .int()
    .positive("Updated by must be a positive integer")
    .optional(),
  deleted_by: z
    .number()
    .int()
    .positive("Deleted by must be a positive integer")
    .optional(),
});

export type AddOrganizationSchema = z.infer<typeof addOrganizationSchema>;
