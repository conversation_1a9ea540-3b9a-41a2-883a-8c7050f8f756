import { NextRequest, NextResponse } from "next/server";
import { v2EngineAxiosInstance } from "@/utils/axios";

export async function GET(req: NextRequest) {
  try {
    const authorizationHeader = req.headers.get("Authorization");
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");
    const organizationId = searchParams.get("organizationId");

    let endpoint = "";
    if (userId) {
      endpoint = `users/${userId}/invoices`;
    } else if (organizationId) {
      endpoint = `organizations/${organizationId}/invoices`;
    } else {
      return NextResponse.json(
        { error: "Missing userId or organizationId query parameter" },
        { status: 400 },
      );
    }

    const response = await v2EngineAxiosInstance.get(endpoint, {
      headers: {
        Authorization: authorizationHeader || "",
      },
    });

    return NextResponse.json(response.data);
  } catch (error) {
    console.error("Error fetching invoices:", error);
    return NextResponse.json(
      { error: "Error fetching invoices" },
      { status: 500 },
    );
  }
}
