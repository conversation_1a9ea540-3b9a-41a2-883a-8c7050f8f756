import { NextRequest, NextResponse } from "next/server";
import { v2EngineAxiosInstance } from "@/utils/axios";
import { Client as MinioClient } from 'minio';
import axios from "axios";

const {
    S3_ENDPOINT,
    S3_BUCKET,
    S3_USE_SSL,
    S3_ACCESS_KEY,
    S3_SECRET_KEY,
    S3_ONBOARDING_DOCUMENTS_PATH,
    WHITEBOOK_API_URL_V2,
    WHITEBOOK_ADMIN_API_KEY
} = process.env;

export const runtime = 'nodejs';

const minioClient = new MinioClient({
    endPoint: S3_ENDPOINT!,
    port: S3_USE_SSL === 'true' ? 443 : 80,
    useSSL: S3_USE_SSL === 'true',
    accessKey: S3_ACCESS_KEY!,
    secretKey: S3_SECRET_KEY!,
});

export async function GET(
    req: NextRequest,
    { params }: { params: { id: string } },
) {
    const { id } = params;

    try {
        const response = await v2EngineAxiosInstance.get(`/organizations/${Number(id)}/documents`);

        const organization = response.data;
        return NextResponse.json(organization);
    } catch (error) {
        console.error("Error fetching organization:", error);
        return NextResponse.json(
            { error: "Error fetching organization" },
            { status: 500 },
        );
    }
}

export async function POST(req: NextRequest, { params }: { params: { id: string } },
) {
    try {
        const formData = await req.formData();
        const file = formData.get('file') as File;
        const organizationId = params.id;

        if (!file) {
            return NextResponse.json(
                { error: 'File is required' },
                { status: 400 }
            );
        }

        // Generate timestamp for unique naming
        const timestamp = Date.now();
        const fileExtension = file.name.split('.').pop();
        const baseFileName = `${timestamp}-${file.name.split('.')[0]}`;

        // Upload to Minio
        const buffer = Buffer.from(await file.arrayBuffer());
        const fileName = `${S3_ONBOARDING_DOCUMENTS_PATH}/${baseFileName}.${fileExtension}`;

        try {
            await minioClient.putObject(S3_BUCKET!, fileName, buffer, buffer.length, {
                'Content-Type': file.type || undefined,
            });
        } catch (uploadError) {
            console.error('Upload error:', uploadError);
            return NextResponse.json({ error: 'Upload failed' }, { status: 500 });
        }

        // Record in backend    
        const backendData = {
            name: baseFileName,
            type: `.${fileExtension}`,
            path: fileName,
            file: `${baseFileName}.${fileExtension}`
        };

        const response = await axios.post(
            `${WHITEBOOK_API_URL_V2}/organizations/${organizationId}/documents`,
            backendData,
            {
                headers: {
                    "X-WHITEBOOK-API-Key": WHITEBOOK_ADMIN_API_KEY,
                    "Content-Type": "application/json",
                },
            }
        );

        if (response.status !== 201) {
            throw new Error('Failed to create document record in backend');
        }

        return NextResponse.json({
            data: response.data,
            message: 'Document uploaded and recorded successfully'
        }, { status: 201 });

    } catch (error) {
        console.error('Error handling document:', error);
        return NextResponse.json(
            { error: 'Failed to process document' },
            { status: 500 }
        );
    }
}