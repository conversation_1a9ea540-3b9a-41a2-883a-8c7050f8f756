#!/bin/bash
# hooks/post_build
# https://docs.docker.com/docker-cloud/builds/advanced/

echo "[***] Post build hook..."
# Source environment variables
source gh_source

success_content=$(<success.json)

curl -sSL \
  -X POST \
  -H "Accept: application/vnd.github+json" \
  -H "Authorization: Bearer $GH_TOKEN" \
  -H "X-GitHub-Api-Version: 2022-11-28" \
  https://api.github.com/repos/$GH_OWNER/$GH_REPO/deployments/$GH_DEPLOYMENT_ID/statuses \
  -d "$success_content"
