import React, { useState, useEffect, useRef } from 'react';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faRotateLeft, faSpinner } from "@fortawesome/free-solid-svg-icons";
import VehicleDetailsModal from './VehicleDetailsModal';
import ProductDetailsSection from './ProductDetailsSection';
import ProductImageSection from './ProductImageSection';
import ProductBasicFormSection from './ProductBasicFormSection';
import VehicleSelectorSection from './VehicleSelectorSection';
import { capitalizeFirstLetter } from '../utils/utils';

// Import types from the types folder
import {
  Product,
  ProductCustomDetail,
  Currency,
  ImageUploadStatus,
} from '@/types/product/index';
import { ProductModalProps } from '@/types/product/modal';

// Import utility functions
import {
  processExistingImages,
  createImageUploadStatus,
  uploadImagesToMinIO
} from '@/utils/productUtils';
import { fetchCurrencies } from '@/utils/currencyUtils';

type DeleteErrorResponse = {
  [key: string]: unknown;
};

const ProductModal: React.FC<ProductModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  currentProduct
}) => {
  // Store original product data for the discard changes feature
  const originalProductData = useRef<Partial<Product>>(currentProduct || {
    name: '',
    productType: '',
    currency_id: undefined,
    price: 0,
    variant: undefined,
    description: '',
    productDetails: [],
  });

  const originalProductDetails = useRef<ProductCustomDetail[]>(
    currentProduct?.productDetails || []
  );

  const originalImageUploads = useRef<ImageUploadStatus[]>([]);

  const [productData, setProductData] = useState<Partial<Product>>(
    originalProductData.current
  );

  const [productDetails, setProductDetails] = useState<ProductCustomDetail[]>(
    originalProductDetails.current
  );

  // State for currencies
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [loadingCurrencies, setLoadingCurrencies] = useState<boolean>(false);
  const [currencyError, setCurrencyError] = useState<string | null>(null);
  const [isVehicleModalOpen, setIsVehicleModalOpen] = useState<boolean>(false);
  const [isVehicleSelectorChecked, setIsVehicleSelectorChecked] = useState<boolean>(false);

  // State to store the complete vehicle details
  const [vehicleDetails, setVehicleDetails] = useState<{
    submodelId: number | null;
    manufacturerName: string;
    modelName: string;
    year: number | null;
    submodelName: string;
    displayText: string;
  }>({
    submodelId: null,
    manufacturerName: '',
    modelName: '',
    year: null,
    submodelName: '',
    displayText: ''
  });
  //  NEW Image Upload State
  const [imageUploads, setImageUploads] = useState<ImageUploadStatus[]>([]);

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isDiscarding, setIsDiscarding] = useState<boolean>(false);

  // Initialize with existing image data when editing
  useEffect(() => {
      setImageUploads([]);
      if (currentProduct?.images && Array.isArray(currentProduct.images)) {
          // Process existing images using the utility function
          const initialUploads = processExistingImages(currentProduct.images);

          setImageUploads(initialUploads);
          // Store original image uploads for discard changes feature
          originalImageUploads.current = [...initialUploads];
      }
  }, [currentProduct]);


  // Fetch currencies when the modal opens
  useEffect(() => {
    if (isOpen) {
      const loadCurrencies = async () => {
        setLoadingCurrencies(true);
        setCurrencyError(null);
        try {
          const data = await fetchCurrencies();
          setCurrencies(data);

          // Set default currency_id if adding a new product and currencies loaded
          if (!currentProduct && data.length > 0) {
             setProductData(prev => ({ ...prev, currency_id: data[0].id })); // Default to first currency
          }
          // setting currency data for editing
          if (currentProduct && currentProduct.currency_id) {
             setProductData(prev => ({ ...prev, currency_id: currentProduct.currency_id }));
          } else if (currentProduct && !currentProduct.currency_id && data.length > 0) {
             // Handle cases where editing product might not have currency_id initially
             setProductData(prev => ({ ...prev, currency_id: data[0].id }));
          }

        } catch (error) {
          setCurrencyError(error instanceof Error ? error.message : "Unknown error");
        } finally {
          setLoadingCurrencies(false);
        }
      };
      loadCurrencies();

      // If editing a product with a variant, initialize the display text
      if (currentProduct?.variant) {
        // Set a placeholder display text until we can fetch the actual details
        setVehicleDetails(prev => ({
          ...prev,
          submodelId: Number(currentProduct.variant),
          displayText: `ID: ${currentProduct.variant}`
        }));

        // Set the checkbox to checked since we have a vehicle variant
        setIsVehicleSelectorChecked(true);

        // Optionally, you could fetch the vehicle details here to display the full info
        // This would require a new API endpoint to get vehicle details by submodel ID
      }
    }
  }, [isOpen, currentProduct]);

  // Clear vehicle details and variant when the checkbox is unchecked
  useEffect(() => {
    if (!isVehicleSelectorChecked) {
      setVehicleDetails({
        submodelId: null,
        manufacturerName: '',
        modelName: '',
        year: null,
        submodelName: '',
        displayText: ''
      });
      setProductData(prev => ({
        ...prev,
        variant: undefined
      }));
    }
  }, [isVehicleSelectorChecked]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setProductData(prev => ({
      ...prev,
      [name]:
        name === 'currency_id'
          ? parseInt(value, 10)
          : (name === 'name' || name === 'description')
            ? capitalizeFirstLetter(value)
            : value
    }));
  };

  const handleProductDetailAdd = () => {
    setProductDetails(prev => [...prev, {
      customFieldName: '',
      fieldType: '',
      value: ''
    }]);
  };

  const handleProductDetailChange = (index: number, field: keyof ProductCustomDetail, value: string) => {
    const updatedDetails = [...productDetails];

    // Handle field type changes
    if (field === 'fieldType') {
      // If changing to boolean, set default value to "false" if current value is not "true" or "false"
      if (value === 'boolean') {
        const currentValue = updatedDetails[index].value.toLowerCase();
        if (currentValue !== 'true' && currentValue !== 'false') {
          updatedDetails[index] = {
            ...updatedDetails[index],
            [field]: value,
            value: 'false' // Set default value to "false"
          };
        } else {
          updatedDetails[index] = {
            ...updatedDetails[index],
            [field]: value
          };
        }
      }
      // If changing to number, ensure value is a valid integer
      else if (value === 'number') {
        const currentValue = updatedDetails[index].value;
        // Try to parse as integer
        const parsedValue = parseInt(currentValue, 10);
        if (isNaN(parsedValue)) {
          updatedDetails[index] = {
            ...updatedDetails[index],
            [field]: value,
            value: '0' // Set default value to "0"
          };
        } else {
          updatedDetails[index] = {
            ...updatedDetails[index],
            [field]: value,
            value: String(parsedValue) // Ensure it's an integer
          };
        }
      }
      // If changing to decimal, ensure value is a valid decimal
      else if (value === 'decimal') {
        const currentValue = updatedDetails[index].value;
        // Try to parse as float
        const parsedValue = parseFloat(currentValue);
        if (isNaN(parsedValue)) {
          updatedDetails[index] = {
            ...updatedDetails[index],
            [field]: value,
            value: '0.0' // Set default value to "0.0"
          };
        } else {
          updatedDetails[index] = {
            ...updatedDetails[index],
            [field]: value,
            value: String(parsedValue.toFixed(2)) // Format with 2 decimal places
          };
        }
      }
      // For other field types, just update the field type
      else {
        updatedDetails[index] = {
          ...updatedDetails[index],
          [field]: value
        };
      }
    }
    // Handle value changes
    else if (field === 'value') {
      // For boolean fields, ensure value is either "true" or "false"
      if (updatedDetails[index].fieldType === 'boolean') {
        // Only allow "true" or "false" for boolean fields
        if (value.toLowerCase() !== 'true' && value.toLowerCase() !== 'false') {
          // If invalid value, keep the current value
          return;
        }

        // Ensure proper casing (lowercase)
        updatedDetails[index] = {
          ...updatedDetails[index],
          [field]: value.toLowerCase()
        };
      }
      // For number fields, validate as integer
      else if (updatedDetails[index].fieldType === 'number') {
        // Allow empty string for user typing experience
        if (value === '') {
          updatedDetails[index] = {
            ...updatedDetails[index],
            [field]: value
          };
        } else {
          // Check if it's a valid number
          const parsedValue = parseInt(value, 10);
          if (!isNaN(parsedValue)) {
            updatedDetails[index] = {
              ...updatedDetails[index],
              [field]: String(parsedValue)
            };
          }
          // If not a valid number, don't update
        }
      }
      // For decimal fields, validate as decimal
      else if (updatedDetails[index].fieldType === 'decimal') {
        // Allow empty string and partial decimal input (e.g., "10.")
        if (value === '' || value === '.' || /^\d*\.?\d*$/.test(value)) {
          updatedDetails[index] = {
            ...updatedDetails[index],
            [field]: value
          };
        }
        // If not a valid decimal format, don't update
      }
      // For other field types, just update the value
      else {
        updatedDetails[index] = {
          ...updatedDetails[index],
          [field]: value
        };
      }
    }
    // For other fields (like customFieldName), just update normally
    else {
      updatedDetails[index] = {
        ...updatedDetails[index],
        [field]: value
      };
    }

    setProductDetails(updatedDetails);
    setProductData(prev => ({
      ...prev,
      productDetails: updatedDetails
    }));
  };

  const handleProductDetailRemove = (indexToRemove: number) => {
    const updatedDetails = productDetails.filter((_, index) => index !== indexToRemove);
    setProductDetails(updatedDetails);
    setProductData(prev => ({
      ...prev,
      productDetails: updatedDetails
    }));
  };


  // Modified to only store files locally without uploading to MinIO immediately
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;

    const filesToUpload = Array.from(e.target.files);
    e.target.value = "";

    for (const file of filesToUpload) {
      // Use the utility function to create a new image upload status
      const newUploadStatus = createImageUploadStatus(file);

      // Add to state immediately
      setImageUploads(prev => [...prev, newUploadStatus]);
    }
  };

  const handleVehicleDetailsSelect = (details: {
    submodelId: number;
    manufacturerName: string;
    modelName: string;
    year: number;
    submodelName: string;
  }) => {
    // Create a formatted display text
    const displayText = `${details.manufacturerName} ${details.modelName} (${details.year}) ${details.submodelName}`;

    // Update the vehicle details state
    setVehicleDetails({
      ...details,
      displayText
    });

    // Update the product data with the variant ID
    setProductData(prev => ({
      ...prev,
      variant: details.submodelId
    }));

    setIsVehicleModalOpen(false);

    // Keep the checkbox checked after selection since we now have a vehicle selected
    // This indicates to the user that a vehicle is linked
    setIsVehicleSelectorChecked(true);
  };
  //Function to remove or restore an image
  const handleRemoveImage = async (localIdToRemove: string) => {
    const imageToRemove = imageUploads.find(upload => upload._localId === localIdToRemove);

    if (!imageToRemove) {
      return;
    }

    // If it's an existing image, toggle the markedForDeletion flag
    if (imageToRemove.isExisting && productData.id) {
      // If the image is already marked for deletion, restore it
      if (imageToRemove.markedForDeletion) {
        setImageUploads(prev => prev.map(upload =>
          upload._localId === localIdToRemove
            ? {
                ...upload,
                markedForDeletion: false // Restore the image
              }
            : upload
        ));
      } else {
        // Otherwise, mark it for deletion
        setImageUploads(prev => prev.map(upload =>
          upload._localId === localIdToRemove
            ? {
                ...upload,
                markedForDeletion: true // Mark for deletion
              }
            : upload
        ));
      }
    } else {
      // For new uploads or uploads without a path, just remove from state
      setImageUploads(prev => prev.filter(upload => upload._localId !== localIdToRemove));
    }
  };

  // Discard changes and revert to original values
  const handleDiscardChanges = async () => {
    try {
      setIsDiscarding(true);

      // Reset product data
      setProductData({...originalProductData.current});

      // Reset product details
      setProductDetails([...originalProductDetails.current]);

      // Reset image uploads
      setImageUploads([...originalImageUploads.current]);

      // Reset vehicle details
      if (originalProductData.current.variant) {
        setVehicleDetails({
          submodelId: Number(originalProductData.current.variant),
          manufacturerName: '',
          modelName: '',
          year: null,
          submodelName: '',
          displayText: `ID: ${originalProductData.current.variant}`
        });
        // Set checkbox to checked if original product had a variant
        setIsVehicleSelectorChecked(true);
      } else {
        setVehicleDetails({
          submodelId: null,
          manufacturerName: '',
          modelName: '',
          year: null,
          submodelName: '',
          displayText: ''
        });
        // Uncheck the checkbox if original product had no variant
        setIsVehicleSelectorChecked(false);
      }

      // Simulate a small delay to show loading state
      await new Promise(resolve => setTimeout(resolve, 300));
    } finally {
      setIsDiscarding(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSubmitting(true);

      // Only handle images when editing an existing product, not during creation
      if (currentProduct) {
        // Get images marked for deletion
        const markedForDeletion = imageUploads.filter(upload =>
          upload.isExisting && upload.markedForDeletion && productData.id
        );

        // If there are images marked for deletion, fetch the latest image data from the backend
        if (markedForDeletion.length > 0 && productData.id) {
          try {
            // Fetch the latest image data from the backend to get the correct image IDs
            const response = await fetch(`/api/products/${productData.id}/images`);

            if (response.ok) {
              const data = await response.json();

              if (data.images && Array.isArray(data.images)) {


                // Process each image marked for deletion
                for (const imageToDelete of markedForDeletion) {
                  try {
                    // Try to find the matching image in the backend data
                    const filename = imageToDelete.path.split('/').pop();
                    const matchingImage = data.images.find((img: any) => {
                      // Check various properties that might contain the filename
                      if (img.path && img.path.includes(filename)) return true;
                      if (img.file && img.file === filename) return true;
                      if (img.name && img.name === filename) return true;
                      return false;
                    });

                    if (matchingImage && matchingImage.id) {

                      // Make API call to remove the image using the RESTful endpoint
                      const deleteResponse = await fetch(`/api/products/${productData.id}/images/${matchingImage.id}`, {
                        method: 'DELETE',
                      });

                      if (!deleteResponse.ok) {
                        const errorData = await deleteResponse.json() as DeleteErrorResponse;
                        console.error('Failed to delete image:', errorData);
                      }
                    } else {
                      console.warn(`Could not find matching image ID for path ${imageToDelete.path}`);
                    }
                  } catch (error) {
                    console.error('Error processing image deletion:', error);
                    // Continue with other deletions even if one fails
                  }
                }
              }
            }
          } catch (error) {
            console.error('Error fetching image data from backend:', error);
          }
        }

        // Get all images that need to be uploaded (not existing and have originalFile)
        const imagesToUpload = imageUploads
            .filter(upload => !upload.isExisting && upload.originalFile);

        // Upload images to MinIO
        const uploadedImages = await uploadImagesToMinIO(imagesToUpload, productData.id);

        // Get existing images that are not marked for deletion
        const existingImages = imageUploads
            .filter(upload => upload.isExisting && !upload.markedForDeletion && upload.status === 'success')
            .map(upload => upload.path);

        // Set default image if needed
        const newUploads = uploadedImages.map((upload, index) => ({
            ...upload,
            is_default: existingImages.length === 0 && index === 0 // Set as default for the first new image upload
        }));

        onSubmit({
            ...productData,
            productDetails,
            variant: productData.variant ? Number(productData.variant) : undefined, // Ensure variant is number or undefined
            // Include both existing image paths and new image data
            images: existingImages,
            imagesData: newUploads
        });
      } else {
        // For new product creation, don't include any image data
        onSubmit({
            ...productData,
            productDetails,
            variant: productData.variant ? Number(productData.variant) : undefined, // Ensure variant is number or undefined
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };


  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className="w-full max-w-4xl rounded-lg bg-white shadow-lg flex flex-col max-h-[90vh]">
        <h2 className="flex-shrink-0 border-b p-6 text-xl font-bold">
          {currentProduct ? "Edit" : "Add"} Product
        </h2>
        <form onSubmit={handleSubmit} className="flex flex-grow flex-col overflow-hidden">
          <div className="flex-grow overflow-y-auto px-6 py-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {/* Basic Form Fields */}
              <ProductBasicFormSection
                productData={productData}
                currencies={currencies}
                loadingCurrencies={loadingCurrencies}
                currencyError={currencyError}
                handleChange={handleChange}
                isLoading={isSubmitting || isDiscarding}
              />

              {/* Vehicle Selector */}
              <VehicleSelectorSection
                isVehicleSelectorChecked={isVehicleSelectorChecked}
                setIsVehicleSelectorChecked={setIsVehicleSelectorChecked}
                vehicleDetails={vehicleDetails}
                setIsVehicleModalOpen={setIsVehicleModalOpen}
                variant={productData.variant}
                isLoading={isSubmitting || isDiscarding}
              />

              {/* Product Details Section */}
              <ProductDetailsSection
                productDetails={productDetails}
                onAddDetail={handleProductDetailAdd}
                onChangeDetail={handleProductDetailChange}
                onRemoveDetail={handleProductDetailRemove}
                isLoading={isSubmitting || isDiscarding}
              />

              {/* Image Upload Section */}
              <ProductImageSection
                imageUploads={imageUploads}
                onImageUpload={handleImageUpload}
                onRemoveImage={handleRemoveImage}
                isEditing={!!currentProduct}
                isSubmitting={isSubmitting || isDiscarding}
              />
            </div>
          </div>

          <div className="flex flex-shrink-0 items-center justify-end space-x-2 border-t p-4">
            <button
              type="button"
              onClick={async () => {
                await handleDiscardChanges();
                onClose();
              }}
              disabled={isSubmitting || isDiscarding}
              className="rounded bg-gray-500 px-4 py-2 text-white hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>

            {currentProduct && (
              <button
                type="button"
                onClick={handleDiscardChanges}
                disabled={isSubmitting || isDiscarding}
                className="rounded bg-amber-500 px-4 py-2 text-white hover:bg-amber-600 items-center disabled:opacity-50 disabled:cursor-not-allowed"
                title="Discard changes and revert to original values"
              >
                {isDiscarding ? (
                  <>
                    <FontAwesomeIcon icon={faSpinner} spin className="mr-2" />
                    Discarding...
                  </>
                ) : (
                  <>
                    <FontAwesomeIcon icon={faRotateLeft} className="mr-2" />
                    Discard Changes
                  </>
                )}
              </button>
            )}
            <button
              type="submit"
              disabled={isSubmitting || isDiscarding}
              className="rounded bg-wbv-theme px-4 py-2 text-white hover:bg-opacity-90 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <FontAwesomeIcon icon={faSpinner} spin className="mr-2" />
                  {currentProduct ? "Updating..." : "Creating..."}
                </>
              ) : (
                currentProduct ? "Update" : "Add Product"
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Vehicle Details Modal */}
      <VehicleDetailsModal
        isOpen={isVehicleModalOpen}
        onClose={() => {
          // Close the modal when Cancel is clicked
          setIsVehicleModalOpen(false);
        }}
        onSelect={handleVehicleDetailsSelect}
      />
    </div>
  );
};

export default ProductModal;
