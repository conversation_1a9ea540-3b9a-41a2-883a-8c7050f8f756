import React, { useState, useEffect } from "react";
import { SortingState } from "@tanstack/react-table";
import DebouncedSearchInput from "@/components/Inputs/DebouncedSearchInput";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faMagnifyingGlass } from "@fortawesome/free-solid-svg-icons";
import TanStackTable from "@/components/Tables/TanStackTable";
import ConfirmPaymentModal from "../Modals/ConfirmPaymentModal";
import { getPageSizeOptions } from "@/components/Tables/utils";
import { useSession } from "next-auth/react";
import { openPaymentLink } from "@/utils/billing";
import { InvoiceStruct } from "@/schemas/billingSchema";
import { getColumns } from "./table/Table";

interface BillingProps {
  invoices: InvoiceStruct[] | null;
  loading: boolean;
  error: string | null;
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  handleCancellation: (id: number) => void;
}

const Billing: React.FC<BillingProps> = ({
  invoices,
  loading,
  error,
  handleCancellation,
}) => {
  const [user, setUser] = useState<{
    first_name: string;
    last_name: string;
    role_name: string;
  } | null>(null);
  const { data: session, status } = useSession();

  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        const response = await fetch(`/api/users/${session?.user?.id}`, {
          method: "GET",
        });
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const userData = await response.json();
        setUser({
          first_name: userData.first_name,
          last_name: userData.last_name,
          role_name: userData.role_name,
        });
      } catch (error) {
        console.error("Error fetching user info:", error);
      }
    };

    if (session) {
      fetchUserInfo();
    }
  }, [session]);

  const [isConfirmPaymentModalOpen, setIsConfirmPaymentModalOpen] =
    useState<boolean>(false);
  const [invoiceToCancel, setInvoiceToCancel] = useState<number | null>(null);
  const [invoiceToPay, setInvoiceToPay] = useState<number | null>(null);
  const [globalFilter, setGlobalFilter] = React.useState("");
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [filterStatus, setFilterStatus] = useState<string[] | null>(["all"]);

  const openCancellationModal = (id: number) => {
    setInvoiceToCancel(id);
    setIsConfirmPaymentModalOpen(true);
  };

  const openPaymentModal = (invoice_id: number) => {
    setInvoiceToPay(invoice_id);
    setIsConfirmPaymentModalOpen(true);
  };

  const handlePaymentConfirmation = () => {
    if (invoiceToPay !== null) {
      const invoice = invoices?.find((invoice) => invoice.id === invoiceToPay);
      if (invoice) {
        openPaymentLink(
          { invoiceId: invoiceToPay, total: invoice.total },
          false,
        );
      }
      setInvoiceToPay(null);
      setIsConfirmPaymentModalOpen(false);
    }
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div className="text-red-500">Error: {error}</div>;

  if (!invoices || invoices.length === 0) {
    return <div>No invoices available</div>;
  }

  const filteredInvoices = filterStatus?.includes("all")
    ? invoices
    : filterStatus
      ? invoices.filter((invoice) => filterStatus?.includes(invoice.status))
      : invoices;

  const allCount = invoices.length;
  const unpaidCount = invoices.filter(
    (invoice) => invoice.status === "pending" || invoice.status === "overdue",
  ).length;
  const paidCount = invoices.filter(
    (invoice) => invoice.status === "paid",
  ).length;

  const tableColumns = getColumns(openPaymentModal, openCancellationModal, user);

  return (
    <div className="flex flex-col pt-4">
      <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
          {/* Search and Filter buttons */}
          <div className="mb-2 flex justify-between items-end">
            <div className="flex w-full items-center gap-1">
              <FontAwesomeIcon icon={faMagnifyingGlass} />
              <DebouncedSearchInput
                value={globalFilter ?? ""}
                onChange={(value) => {
                  setFilterStatus(["all"]);
                  setGlobalFilter(String(value));
                }}
                debounced={500}
                placeholder="Search payments..."
              />
            </div>
            <div className="flex items-center gap-2">
              <div className="relative inline-block text-left">
                <div className="flex flex-row">
                  <button
                    onClick={() => setFilterStatus(["all"])}
                    className={`inline-flex w-20 items-center justify-center rounded-l border border-gray-300 px-4 py-2 text-xs font-semibold ${filterStatus?.includes("all")
                      ? "bg-wbv-theme text-white"
                      : "bg-white text-gray-700"
                      }`}
                  >
                    All
                    <span className="ml-1 rounded-full bg-gray-200 px-1 py-0.15 text-xs font-semibold text-gray-700">
                      {allCount}
                    </span>
                  </button>
                  <button
                    onClick={() => setFilterStatus(["pending", "overdue"])}
                    className={`inline-flex w-20 items-center justify-center border border-gray-300 px-4 py-2 text-xs font-semibold ${filterStatus?.includes("pending") ||
                      filterStatus?.includes("overdue")
                      ? "bg-wbv-theme text-white"
                      : "bg-white text-gray-700"
                      }`}
                  >
                    Unpaid
                    <span className="ml-1 rounded-full bg-gray-200 px-1 py-0.15 text-xs font-semibold text-gray-700">
                      {unpaidCount}
                    </span>
                  </button>
                  <button
                    onClick={() => setFilterStatus(["paid"])}
                    className={`inline-flex w-20 items-center justify-center rounded-r border border-gray-300 px-4 py-2 text-xs font-semibold ${filterStatus?.includes("paid")
                      ? "bg-wbv-theme text-white"
                      : "bg-white text-gray-700"
                      }`}
                  >
                    Paid
                    <span className="ml-1 rounded-full bg-gray-200 px-1 py-0.15 text-xs font-semibold text-gray-700">
                      {paidCount}
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
          {/* Confirm Cancellation Modal */}
          <ConfirmPaymentModal
            isOpen={isConfirmPaymentModalOpen}
            onClose={() => setIsConfirmPaymentModalOpen(false)}
            onConfirm={handlePaymentConfirmation}
            title={"Confirm Payment"}
            message={`Are you sure you want to proceed with the payment for invoice <strong>#${invoiceToPay}?</strong>`}
            cta={"Proceed"}
            ctaBtnColor={"theme"}
          />

          {/* invoices Table */}
          {loading ? (
            <p>Loading...</p>
          ) : error ? (
            <p className="text-red-500">{error}</p>
          ) : (
            <div className="my-5 overflow-hidden rounded border-b border-gray-200 shadow">
              <TanStackTable
                data={filteredInvoices}
                columns={tableColumns}
                globalFilter={globalFilter}
                sorting={sorting}
                setGlobalFilter={setGlobalFilter}
                setSorting={setSorting}
                pageSizeOptions={getPageSizeOptions(filteredInvoices)}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Billing;