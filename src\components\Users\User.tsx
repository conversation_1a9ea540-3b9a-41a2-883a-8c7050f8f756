import React, { useState } from "react";
import { formatDateTime } from "../utils/utils";
import Link from "next/link";
import UserModal from "../Modals/UserModal";
import ConfirmModal from "../Modals/ConfirmModal";
import { Bar, Line, Pie } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
);

interface UserStruct {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  role_name: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  created_by: number;
  updated_by: number;
  deleted_by: number;
  last_login: string;
  organization_id: number;
  role_description: string;
  role_id: number;
  role_label: string;
  title: string;
}

interface UserProps {
  user: UserStruct;
  handleDelete: (id: number) => void;
  handleUpdate: (updatedUser: Partial<UserStruct>) => void;
  loading: boolean;
  error: string | null;
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  showPassword: boolean;
  setShowPassword: React.Dispatch<React.SetStateAction<boolean>>;
}

const User: React.FC<UserProps> = ({
  user,
  loading,
  error,
  isModalOpen,
  setIsModalOpen,
  showPassword,
  setShowPassword,
  handleUpdate,
  handleDelete,
}) => {
  const [selectedUser, setSelectedUser] = useState<Partial<UserStruct> | null>(
    null,
  );
  const [newUser, setNewUser] = useState<Partial<UserStruct>>({
    first_name: "",
    last_name: "",
    email: "",
    phone_number: "",
    role_name: "",
  });

  const [isConfirmDeleteModalOpen, setIsConfirmDeleteModalOpen] =
    useState<boolean>(false);
  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleUpdate(newUser);
    setNewUser({
      first_name: "",
      last_name: "",
      email: "",
      phone_number: "",
      role_name: "",
    });
    setSelectedUser(null);
    setIsModalOpen(false);
  };

  const openUpdateModal = (user: Partial<UserStruct>) => {
    setSelectedUser(user);
    setNewUser(user);
    setIsModalOpen(true);
  };

  const confirmDelete = () => {
    if (user !== null) {
      handleDelete(user.id);
      setIsConfirmDeleteModalOpen(false);
    }
  };

  const openDeleteModal = (id: number) => {
    setIsConfirmDeleteModalOpen(true);
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target;
    setNewUser((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handlePhoneChange = (value: string) => {
    setNewUser((prevData) => ({
      ...prevData,
      phone_number: value,
    }));
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div className="text-red-500">Error: {error}</div>;

  const usageData = {
    labels: ["January", "February", "March", "April", "May", "June"],
    datasets: [
      {
        label: "Usage",
        data: [65, 59, 80, 81, 56, 55],
        backgroundColor: "rgba(75,192,192,0.4)",
        borderColor: "rgba(75,192,192,1)",
        borderWidth: 1,
      },
    ],
  };

  const loginsData = {
    labels: ["Week 1", "Week 2", "Week 3", "Week 4"],
    datasets: [
      {
        label: "Logins",
        data: [12, 19, 3, 5],
        backgroundColor: "rgba(153,102,255,0.4)",
        borderColor: "rgba(153,102,255,1)",
        borderWidth: 1,
      },
    ],
  };

  const activityData = {
    labels: ["Search", "View", "Edit", "Delete"],
    datasets: [
      {
        label: "Activity",
        data: [300, 50, 100, 40],
        backgroundColor: [
          "rgba(255,99,132,0.4)",
          "rgba(54,162,235,0.4)",
          "rgba(255,206,86,0.4)",
          "rgba(75,192,192,0.4)",
        ],
        borderColor: [
          "rgba(255,99,132,1)",
          "rgba(54,162,235,1)",
          "rgba(255,206,86,1)",
          "rgba(75,192,192,1)",
        ],
        borderWidth: 1,
      },
    ],
  };

  const searchHistoryData = {
    labels: ["Car", "Bike", "Truck", "Bus"],
    datasets: [
      {
        label: "Search History",
        data: [200, 150, 100, 50],
        backgroundColor: "rgba(255,159,64,0.4)",
        borderColor: "rgba(255,159,64,1)",
        borderWidth: 1,
      },
    ],
  };

  return (
    <div className="pb-6 pt-4">
      <div className="mb-4 flex items-center justify-between">
        <h1 className="whitespace-nowrap text-3xl font-bold">
          {user.first_name} {user.last_name}&nbsp;
          <span className="rounded-full bg-wbv-theme px-2 py-1 text-xs font-semibold text-gray-100">
            {user.role_name}
          </span>
        </h1>
        <Link href="/users" className="text-blue-500 hover:underline">
          Back to Users
        </Link>
      </div>
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="flex-1 bg-white p-4 shadow-md">
          <h2 className="mb-2 text-xl font-semibold">Personal Details</h2>
          {user?.title && (
            <p className="text-sm text-gray-700">
              <strong>Title:</strong> {user.title}
            </p>
          )}
          <p className="text-sm text-gray-700">
            <strong>First Name:</strong> {user.first_name}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Last Name:</strong> {user.last_name}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Email:</strong> {user.email}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Phone Number:</strong> {user.phone_number}
          </p>
        </div>
        <div className="flex-1 bg-white p-4 shadow-md">
          <h2 className="mb-2 text-xl font-semibold">Role Information</h2>
          <p className="text-sm text-gray-700">
            <strong>Role Name:</strong> {user.role_name}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Role Description:</strong> {user.role_description}
          </p>
        </div>
        <div className="flex-1 bg-white p-4 shadow-md">
          <h2 className="mb-2 text-xl font-semibold">Timestamps</h2>
          <p className="text-sm text-gray-700">
            <strong>Created At:</strong> {formatDateTime(user.created_at)}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Updated At:</strong> {formatDateTime(user.updated_at)}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Last Login:</strong>{" "}
            {user.last_login !== "0001-01-01T02:00:00+02:00"
              ? formatDateTime(user.last_login)
              : "Never"}
          </p>
        </div>
        <div className="flex-1 bg-white p-4 shadow-md">
          <h2 className="mb-2 text-xl font-semibold">Audit Information</h2>
          <p className="text-sm text-gray-700">
            <strong>Created By:</strong> {user.created_by}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Updated By:</strong> {user.updated_by}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Deleted By:</strong> {user.deleted_by}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Deleted At:</strong>{" "}
            {user.deleted_at ? formatDateTime(user.deleted_at) : "N/A"}
          </p>
        </div>
      </div>
      <div className="mt-4 flex space-x-4">
        <button
          className="rounded bg-wbv-theme px-4 py-2 text-white hover:bg-opacity-90"
          onClick={() => openUpdateModal(user)}
        >
          Update
        </button>
        <button
          onClick={() => openDeleteModal(user?.id)}
          className="rounded bg-red-500 px-4 py-2 text-white hover:bg-opacity-90"
        >
          Delete
        </button>
      </div>
      <div className="mt-4 bg-white p-4 shadow-md">
        <h2 className="mb-2 text-xl font-semibold">User Statistics</h2>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <h3 className="text-lg font-semibold">Usage</h3>
            <Bar data={usageData} />
          </div>
          <div>
            <h3 className="text-lg font-semibold">Logins Made</h3>
            <Line data={loginsData} />
          </div>
          <div>
            <h3 className="text-lg font-semibold">Activity Summary</h3>
            <Pie data={activityData} />
          </div>
          <div>
            <h3 className="text-lg font-semibold">Search History</h3>
            <Bar data={searchHistoryData} />
          </div>
        </div>
      </div>
      {isModalOpen && (
        <UserModal
          selectedUser={selectedUser}
          onSubmit={onSubmit}
          newUser={newUser}
          handleChange={handleChange}
          handlePhoneChange={handlePhoneChange}
          setIsModalOpen={setIsModalOpen}
        />
      )}
      <ConfirmModal
        isOpen={isConfirmDeleteModalOpen}
        onClose={() => setIsConfirmDeleteModalOpen(false)}
        onConfirm={confirmDelete}
        title="Confirm Deletion"
        message={`Are you sure you want to delete the user <strong>${user?.first_name} ${user?.last_name}?</strong>`}
        cta="Delete"
        ctaBtnColor="danger"
      />
    </div>
  );
};

export default User;
