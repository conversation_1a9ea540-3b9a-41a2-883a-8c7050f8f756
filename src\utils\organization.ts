import { engineAxiosInstance } from "@/utils/axios";

interface Organization {
  id: number;
  name: string;
  // registration_number: string;
  // tax_id: string;
  // organization_type_id: number;
  // parent_id: number;
  email: string;
  phone_number: string;
  postal_address: string;
  // town_id: number;
  street_address: string;
  // created_by: number;
  // updated_by: number;
  // deleted_by: number;
}

export const createOrganization = async (newOrganization: Organization) => {
  try {
    const response = await engineAxiosInstance.post(
      "/organization",
      JSON.stringify(newOrganization),
    );
    return response.data;
  } catch (error) {
    console.error("Error creating organization:", error);
    throw error;
  }
};

export const getOrganizationInfo = async (organizationId: number) => {
  try {
    const response = await engineAxiosInstance.get(
      `/organization/${organizationId}`,
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching organization info:", error);
    return null;
  }
};
