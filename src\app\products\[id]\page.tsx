"use client";

import React, { useState } from "react";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import ProductPreview from "@/components/Products/ProductPreview";
import { useParams, useRouter } from "next/navigation";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import { Product } from "@/types/product/index";
import { transformDetailsForApi } from "@/utils/productUtils";
import InlineLoader from "@/components/common/Loader/InlineLoader";
import { toast } from "react-toastify";
import { APIErrorResponse } from "@/utils/axios";

// Define UploadedImageData locally
interface UploadedImageData {
   id?: number;
   name: string;
   type: string;
   is_default: boolean;
   file: string;
   path: string;
}

const ProductDetailPage = () => {
  const params = useParams();
  const router = useRouter();
  const [pageLoading, setPageLoading] = useState<boolean>(false);

  const productIdParam = params?.id;
  const productId = productIdParam
    ? parseInt(Array.isArray(productIdParam) ? productIdParam[0] : productIdParam, 10)
    : NaN;

  const handleUpdate = async (productSubmitData: Partial<Product> & { imagesData?: UploadedImageData[] }): Promise<void> => {
    if (!productId || isNaN(productId)) return;
    setPageLoading(true);
    // Create a single toast ID for this update operation
    const toastId = toast.info("Updating product...", { autoClose: false });
    try {
      const productDetailsForApi = transformDetailsForApi(productSubmitData.productDetails);

      // Create a properly formatted payload for the API
      const apiPayload: Record<string, unknown> = {
        ...productSubmitData, // Spread incoming data first
        type: productSubmitData.productType,
        product_details: productDetailsForApi, // Use the transformed object
        images: productSubmitData.imagesData, // Include the image data array

        // Handle variant field properly - convert from number to object if needed
        variant_id: productSubmitData.variant ? Number(productSubmitData.variant) : undefined,
        variant: undefined, // Remove the numeric variant field

        // Remove frontend-specific fields
        productType: undefined,
        currency_code: undefined,
        productDetails: undefined, // Remove the array form
        imagesData: undefined, // Remove the extra key used for passing data
      };
      Object.keys(apiPayload).forEach(key =>
        (apiPayload[key] === undefined || apiPayload[key] === null) && delete apiPayload[key]
      );

      const response = await fetch(`/api/products/${productId}`, {
        method: "PUT",
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(apiPayload),
      });
      if (!response.ok) {
        const errorData: unknown = await response.json().catch(() => ({ message: `Failed to update product (status: ${response.status})` }));
        let message = `Failed to update product`;
        const err = errorData as APIErrorResponse;
        if (
          err &&
          typeof err === "object" &&
          err.response &&
          typeof err.response === "object"
        ) {
          if (typeof err.response.data?.error === "string") {
            message = err.response.data.error;
          } else if (typeof err.response.data?.message === "string") {
            message = err.response.data.message;
          } else if (typeof err.response.message === "string") {
            message = err.response.message;
          }
        } else if (
          errorData &&
          typeof errorData === "object" &&
          "error" in errorData &&
          typeof (errorData as { error?: string }).error === "string"
        ) {
          message = (errorData as { error: string }).error;
        } else if (
          errorData &&
          typeof errorData === "object" &&
          "message" in errorData &&
          typeof (errorData as { message?: string }).message === "string"
        ) {
          message = (errorData as { message: string }).message;
        }
        throw new Error(message);
      }

      // Update the toast with success message
      toast.update(toastId, { type: 'success', render: "Product updated successfully", autoClose: 3000 });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to update product";
      toast.update(toastId, { type: 'error', render: errorMessage, autoClose: 3000 });
      throw err;
    } finally {
      setPageLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    setPageLoading(true);
    const toastId = toast.info("Deleting product...", { autoClose: false });
    try {
      const response = await fetch(`/api/products/${id}`, {
        method: "DELETE",
      });
      if (!response.ok) {
        const errorData: unknown = await response.json().catch(() => ({ message: 'Failed to delete product' }));
        let message = 'Failed to delete product';
        const err = errorData as APIErrorResponse;
        if (
          err &&
          typeof err === "object" &&
          err.response &&
          typeof err.response === "object"
        ) {
          if (typeof err.response.data?.error === "string") {
            message = err.response.data.error;
          } else if (typeof err.response.data?.message === "string") {
            message = err.response.data.message;
          } else if (typeof err.response.message === "string") {
            message = err.response.message;
          }
        } else if (
          errorData &&
          typeof errorData === "object" &&
          "error" in errorData &&
          typeof (errorData as { error?: string }).error === "string"
        ) {
          message = (errorData as { error: string }).error;
        } else if (
          errorData &&
          typeof errorData === "object" &&
          "message" in errorData &&
          typeof (errorData as { message?: string }).message === "string"
        ) {
          message = (errorData as { message: string }).message;
        }
        throw new Error(message);
      }

      toast.update(toastId, { type: 'success', render: "Product deleted successfully", autoClose: 3000 });
      router.push("/products");
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to delete product";
      toast.update(toastId, { type: 'error', render: errorMessage, autoClose: 3000 });
      throw err;
    } finally {
      setPageLoading(false);
    }
  };

  let content;
  if (pageLoading) {
    content = (
      <div className="rounded-sm border border-stroke bg-white p-6 shadow-default dark:border-strokedark dark:bg-boxdark">
        <InlineLoader message="Processing product..." size="large" />
      </div>
    );
  } else if (isNaN(productId)) {
    content = <div className="p-4 text-red-500 text-center rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">Invalid Product ID in URL.</div>;
  } else {
    content = (
      <ProductPreview
        productId={productId}
        handleUpdateProduct={handleUpdate}
        handleDeleteProduct={handleDelete}
      />
    );
  }

  return (
    <DefaultLayout>
      <Breadcrumb pageName={isNaN(productId) ? "Invalid Product" : "Product Details"} />
      {content}
    </DefaultLayout>
  );
};

export default ProductDetailPage;
