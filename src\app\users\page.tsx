"use client";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import React, { useEffect, useState } from "react";
import Users from "@/components/Users/<USER>";
import { useSession } from "next-auth/react";
import { ToastContainer, toast } from "react-toastify";

interface OrganizationUser {
  id: number;
  first_name: string;
  last_name: string;
  role_name: string;
  email: string;
  phone_number: string;
}

interface StaffMember {
  id: number;
  user_id: number;
  organization_id: number;
  position_id: number | null;
}

interface User {
  id: number;
  email: string;
  phone_number: string;
  title: string;
  first_name: string;
  last_name: string;
  role_id: number;
  organization_id: number;
  staff_members?: StaffMember[];
}

const UsersPage: React.FC = () => {
  const [organizationUsers, setOrganizationUsers] = useState<
    OrganizationUser[]
  >([]);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<any>(null);
  const { data: session, status } = useSession();
  const [loggedInUser, setLoggedInUser] = useState<{
    first_name: string;
    last_name: string;
    role_name: string;
    organization_id?: number;
  } | null>(null);
  const [toastMessage, setToastMessage] = useState<string | null>(null);
  const [toastError, setToastError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        if (!session?.user?.id) {
          throw new Error("No user id found in session");
        }

        const userResponse = await fetch(`/api/users/${session?.user?.id}`, {
          method: "GET",
        });
        if (!userResponse.ok) {
          throw new Error(`HTTP error! status: ${userResponse.status}`);
        }
        const userData = await userResponse.json();
        setLoggedInUser({
          first_name: userData.first_name,
          last_name: userData.last_name,
          role_name: userData.role_name,
          organization_id: userData.staff_members
            ? userData.staff_members[0].organization_id
            : undefined,
        });
        if (!session?.user?.token) {
          throw new Error("No user token found in session");
        }
        const usersResponse = await fetch("/api/users", {
          method: "GET",
          headers: {
            token: session?.user?.token || "",
          },
        });
        if (!usersResponse.ok) {
          setError(
            `Failed to fetch users: HTTP error! status: ${usersResponse.status}`,
          );
          setLoading(false);
          console.error("Failed to fetch users: ", error);
        } else {
          setLoading(false);
        }
        const usersData = await usersResponse.json();
        setOrganizationUsers(usersData.data);
      } catch (error) {
        setError(`Failed to fetch users: ${error}`);
        console.error("Failed to fetch users: ", error);
      }
    };

    if (session) {
      fetchUserInfo();
    }
  }, [session]);

  useEffect(() => {
    if (toastMessage) {
      toast.success(toastMessage, {
        position: "bottom-right",
      });
      setToastMessage(null);
    }
    if (toastError) {
      toast.error(toastError, {
        position: "bottom-right",
      });
      setToastError(null);
    }
  }, [toastMessage, toastError]);

  const handleSubmit = async (newUser: Partial<User>) => {
    setLoading(true);
    if (newUser.role_id) {
      newUser.role_id = Number(newUser.role_id);
    }
    if (loggedInUser?.organization_id) {
      newUser.organization_id = loggedInUser.organization_id;
      newUser.staff_members = [
        {
          id: 0,
          user_id: 0,
          organization_id: loggedInUser.organization_id,
          position_id: null,
        },
      ];
    } else {
      setError("No organization_id found for the user");
      setToastError("Error adding user. Please try again.");
      setLoading(false);
      return;
    }
    try {
      const response = await fetch("/api/users", {
        method: "POST",
        body: JSON.stringify(newUser),
      });
      if (!response.ok) {
        setIsModalOpen(false);
        setToastError("Error adding user. Please try again.");
      } else {
        const result = await response.json();
        setOrganizationUsers((prev) =>
          Array.isArray(prev) ? [...prev, result.data] : [result.data],
        );
        setIsModalOpen(false);
        setToastMessage("User added successfully!");
      }
    } catch (err) {
      setError("Failed to add user");
      setToastError("Error adding user. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleUpdate = async (updatedUser: Partial<User>) => {
    if (updatedUser.role_id) {
      updatedUser.role_id = Number(updatedUser.role_id);
    }
    try {
      const response = await fetch("/api/users", {
        method: "PUT",
        body: JSON.stringify(updatedUser),
      });
      if (!response.ok) {
        setIsModalOpen(false);
        setToastError("Error updating user. Please try again.");
      } else {
        const result = await response.json();
        setOrganizationUsers((prev) =>
          Array.isArray(prev)
            ? prev.map((user) =>
                user.id === updatedUser.id ? result.data : user,
              )
            : [result.data],
        );
        setIsModalOpen(false);
        setToastMessage("User updated successfully!");
      }
    } catch (err) {
      setError("Failed to update user");
      setToastError("Error updating user. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    setLoading(true);
    try {
      const response = await fetch("/api/users", {
        method: "DELETE",
        body: JSON.stringify({ id }),
      });
      if (!response.ok) {
        setIsModalOpen(false);
        setToastError("Error deleting user. Please try again.");
      } else {
        setOrganizationUsers((prev) =>
          Array.isArray(prev) ? prev.filter((org) => org.id !== id) : [],
        );
        setToastMessage("User deleted successfully!");
      }
    } catch (err) {
      setError("Failed to delete user");
      setToastError("Error deleting user. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <DefaultLayout>
      <Breadcrumb pageName="Users" />
      <Users
        organizationUsers={organizationUsers}
        loading={loading}
        error={error}
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        handleUpdate={handleUpdate}
        handleSubmit={handleSubmit}
        handleDelete={handleDelete}
      />
      <ToastContainer />
    </DefaultLayout>
  );
};

export default UsersPage;
