import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSpinner, faCheckCircle, faExclamationCircle, faTrash } from '@fortawesome/free-solid-svg-icons';
import path from 'path';

// Interface for uploaded image data
interface UploadedImageData {
  id?: number;      // Image ID from the backend
  name: string;
  type: string;
  is_default: boolean;
  file: string;
  path: string;
}

// Interface to track upload status locally
interface ImageUploadStatus extends UploadedImageData {
  _localId: string; // Temporary local ID for tracking
  status: 'uploading' | 'success' | 'error';
  errorMessage?: string;
  originalFile?: File; // Keep original file for potential preview
}

interface ProductImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  productId: number;
  productName: string;
  onImagesUploaded: (imagesAdded: boolean) => void; // Callback when images are uploaded successfully
}

const ProductImageModal: React.FC<ProductImageModalProps> = ({
  isOpen,
  onClose,
  productId,
  productName,
  onImagesUploaded
}) => {
  const [imageUploads, setImageUploads] = useState<ImageUploadStatus[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setImageUploads([]);
      setIsSubmitting(false);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  // Handle file selection
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;

    const filesToUpload = Array.from(e.target.files);
    e.target.value = "";

    for (const file of filesToUpload) {
      const localId = `${Date.now()}-${Math.random()}`;
      // Generate a unique filename similar to what the server will create
      const timestamp = Date.now();
      const uniqueFilename = `${timestamp}-${file.name.replace(/[^a-zA-Z0-9.]/g, '_')}`;
      const fileExtension = path.extname(file.name);
      const uniqueNameWithoutExt = uniqueFilename.substring(0, uniqueFilename.lastIndexOf('.'));

      const newUploadStatus: ImageUploadStatus = {
        _localId: localId,
        name: uniqueNameWithoutExt, // Unique name without extension
        type: fileExtension,
        is_default: false,
        file: uniqueFilename, // Unique name with extension
        path: '',
        status: 'success', // Mark as success immediately since we're not uploading yet
        originalFile: file,
      };

      // Add to state immediately
      setImageUploads(prev => [...prev, newUploadStatus]);
    }
  };

  // Upload images to MinIO and then to the backend
  const uploadImagesToProduct = async (imagesToUpload: ImageUploadStatus[]): Promise<UploadedImageData[]> => {
    const uploadResults: UploadedImageData[] = [];

    for (const upload of imagesToUpload) {
      // Skip images that don't have an original file
      if (!upload.originalFile) continue;

      // Update status to uploading
      setImageUploads(prev =>
        prev.map(item =>
          item._localId === upload._localId
            ? { ...item, status: 'uploading' }
            : item
        )
      );

      try {
        // First upload to MinIO
        const formData = new FormData();
        formData.append("image", upload.originalFile);
        formData.append("productId", String(productId));

        const response = await fetch('/api/products/upload-image', {
          method: 'POST',
          body: formData,
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || `Upload failed with status ${response.status}`);
        }

        // Now send the image data to the product images endpoint
        // The upload-image API now returns:
        // - name: unique name without extension
        // - file: unique name with extension
        // - type: file extension
        // - path: full path in storage
        const imageData = {
          id: result.id, // Include the ID if it exists
          name: result.name,
          type: result.type,
          is_default: false, // Will be set based on order later
          file: result.file,
          path: result.path
        };

        // Update the specific upload status to 'success' with data from API
        setImageUploads(prev =>
          prev.map(item =>
            item._localId === upload._localId
              ? { ...item, ...result, status: 'success' }
              : item
          )
        );

        // Add to results
        uploadResults.push(imageData);
      } catch (error) {
        // Update the specific upload status to 'error'
        setImageUploads(prev =>
          prev.map(item =>
            item._localId === upload._localId
              ? { ...item, status: 'error', errorMessage: error instanceof Error ? error.message : "Unknown upload error" }
              : item
          )
        );
      }
    }

    return uploadResults;
  };

  // Function to remove an image
  const handleRemoveImage = async (localIdToRemove: string) => {
    const imageToRemove = imageUploads.find(upload => upload._localId === localIdToRemove);

    if (!imageToRemove) {
      return;
    }

    // If the image has already been uploaded to the backend
    if (imageToRemove.id) {
      try {
        // Set status to uploading to show loading state
        setImageUploads(prev => prev.map(upload =>
          upload._localId === localIdToRemove
            ? { ...upload, status: 'uploading' }
            : upload
        ));

        // Make API call to remove the image using the RESTful endpoint
        const response = await fetch(`/api/products/${productId}/images/${imageToRemove.id}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to delete image: ${response.status}`);
        }

        // If successful, remove from state
        setImageUploads(prev => prev.filter(upload => upload._localId !== localIdToRemove));
      } catch (error) {


        // Update status to error
        setImageUploads(prev => prev.map(upload =>
          upload._localId === localIdToRemove
            ? {
                ...upload,
                status: 'error',
                errorMessage: error instanceof Error ? error.message : "Failed to delete image"
              }
            : upload
        ));
      }
    } else if (imageToRemove.path) {


      try {


        // Make a request to get all images for this product
        const response = await fetch(`/api/products/${productId}/images`);

        if (!response.ok) {
          throw new Error(`Failed to fetch images: ${response.status}`);
        }

        const data = await response.json();

        if (data.images && Array.isArray(data.images)) {
          // Try to find the image by matching the filename in the path
          const filename = imageToRemove.path.split('/').pop();
          const matchingImage = data.images.find((img: any) => {
            // Check various properties that might contain the filename
            if (img.path && img.path.includes(filename)) return true;
            if (img.file && img.file === filename) return true;
            if (img.name && img.name === filename) return true;
            return false;
          });

          if (matchingImage && matchingImage.id) {


            // Set status to uploading to show loading state
            setImageUploads(prev => prev.map(upload =>
              upload._localId === localIdToRemove
                ? { ...upload, status: 'uploading' }
                : upload
            ));

            // Make API call to remove the image using the RESTful endpoint
            const deleteResponse = await fetch(`/api/products/${productId}/images/${matchingImage.id}`, {
              method: 'DELETE',
            });

            if (!deleteResponse.ok) {
              const errorData = await deleteResponse.json();
              throw new Error(errorData.error || `Failed to delete image: ${deleteResponse.status}`);
            }

            // If successful, remove from state
            setImageUploads(prev => prev.filter(upload => upload._localId !== localIdToRemove));
            return;
          }
        }
      } catch (error) {

      }

      // If we get here, we couldn't find or delete the image via the backend


      // Just remove from state since we don't have a valid image ID
      setImageUploads(prev => prev.filter(upload => upload._localId !== localIdToRemove));
    } else {
      // For images that haven't been uploaded to the backend yet, just remove from state
      setImageUploads(prev => prev.filter(upload => upload._localId !== localIdToRemove));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Get all images that need to be uploaded
      const imagesToUpload = imageUploads.filter(upload => upload.originalFile);

      if (imagesToUpload.length === 0) {
        // No images to upload, just close the modal and indicate no images were added
        onImagesUploaded(false);
        return;
      }

      // Upload images to MinIO
      const uploadedImages = await uploadImagesToProduct(imagesToUpload);

      // Set the first image as default
      const imagesWithDefault = uploadedImages.map((img, index) => ({
        ...img,
        is_default: index === 0
      }));

      // Send the image data to the backend
      const response = await fetch(`/api/products/${productId}/images`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(imagesWithDefault),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update product images: ${response.status}`);
      }

      // Get the response data which should include the image IDs
      const responseData = await response.json();

      // Update the image uploads with the IDs from the response if available
      if (responseData && Array.isArray(responseData.data)) {
        setImageUploads(prev => {
          return prev.map(upload => {
            // Try to find the corresponding image in the response
            const matchingImage = responseData.data.find((img: any) =>
              img.name === upload.name && img.file === upload.file
            );

            if (matchingImage && matchingImage.id) {
              return {
                ...upload,
                id: matchingImage.id
              };
            }
            return upload;
          });
        });
      }

      // Call the callback to refresh the product list and indicate images were added
      onImagesUploaded(true);

      // No need to call onClose() as onImagesUploaded will handle closing the modal
    } catch (error) {

    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className="w-full max-w-4xl rounded-lg bg-white shadow-lg flex flex-col max-h-[90vh]">
        <h2 className="flex-shrink-0 border-b p-6 text-xl font-bold">
          Add Images to "{productName}"
        </h2>
        <form onSubmit={handleSubmit} className="flex flex-grow flex-col overflow-hidden">
          <div className="flex-grow overflow-y-auto px-6 py-4">
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-4">
                Your product has been created successfully! Now, let's add some images to showcase your product.
              </p>

              <label className="mb-1 block text-sm font-medium">Upload Images</label>
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handleImageUpload}
                className="mb-2 w-full rounded border border-gray-300 p-2 focus:border-wbv-theme focus:outline-none focus:ring-2 focus:ring-wbv-theme"
              />

              {/* Display Upload Status/Preview */}
              <div className="mt-4 grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4">
                {imageUploads.map((upload) => (
                  <div key={upload._localId} className="relative rounded border p-2 flex flex-col">
                    <div className="aspect-square overflow-hidden rounded mb-2">
                      {upload.originalFile && (
                        <img
                          src={URL.createObjectURL(upload.originalFile)}
                          alt={upload.name}
                          className="h-full w-full object-cover"
                          onLoad={(e) => URL.revokeObjectURL((e.target as HTMLImageElement).src)}
                        />
                      )}
                      {upload.status === 'uploading' && (
                        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                          <FontAwesomeIcon icon={faSpinner} spin className="text-white text-2xl" />
                        </div>
                      )}
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="truncate text-xs" title={upload.name}>
                        {upload.name.length > 15 ? upload.name.substring(0, 12) + '...' : upload.name}
                      </span>
                      <div className="flex items-center">
                        {upload.status === 'success' && <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 mr-1" />}
                        {upload.status === 'error' && <FontAwesomeIcon icon={faExclamationCircle} className="text-red-500 mr-1" title={upload.errorMessage} />}
                        <button
                          type="button"
                          onClick={() => handleRemoveImage(upload._localId)}
                          className="text-red-500 hover:text-red-700"
                          title="Remove Image"
                          disabled={upload.status === 'uploading'}
                        >
                          <FontAwesomeIcon icon={faTrash} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="flex flex-shrink-0 items-center justify-end space-x-2 border-t p-4">
            <button
              type="button"
              onClick={() => onImagesUploaded(false)}
              className="rounded bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
            >
              Skip
            </button>
            <button
              type="submit"
              disabled={isSubmitting || imageUploads.length === 0}
              className="rounded bg-wbv-theme px-4 py-2 text-white hover:bg-opacity-90 disabled:bg-opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <FontAwesomeIcon icon={faSpinner} spin className="mr-2" />
                  Uploading...
                </>
              ) : (
                'Upload Images'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProductImageModal;
