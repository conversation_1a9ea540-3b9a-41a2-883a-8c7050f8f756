import { faker } from "@faker-js/faker";

export function createRandomUser(): {
  profile: string;
  firstName: string;
  lastName: string;
  age: number;
  visits: number;
  progress: number;
} {
  return {
    profile: faker.image.avatar(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    age: faker.number.int({ min: 18, max: 60 }),
    visits: faker.number.int({ min: 1, max: 1000 }),
    progress: faker.number.int({ min: 1, max: 100 }),
  };
}

export const users: Array<{
  profile: string;
  firstName: string;
  lastName: string;
  age: number;
  visits: number;
  progress: number;
}> = faker.helpers.multiple(createRandomUser, {
  count: 30,
});
