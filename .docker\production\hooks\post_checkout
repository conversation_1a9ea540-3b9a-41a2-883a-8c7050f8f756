#!/bin/bash
# hooks/post_checkout
# https://docs.docker.com/docker-cloud/builds/advanced/

echo "[***] Post checkout hook..."
echo "Install jq..."

apt-get install -y jq

cat <<EOF >gh_source
export GH_TOKEN=$GITHUB_DEPLOY_TOKEN
export GH_OWNER=kualatechzm
export GH_REPO=orange-book-admin
export GH_BRANCH=$(git rev-parse --abbrev-ref HEAD)
export GH_ENV=development
EOF

source gh_source

cat <<EOF >queue.json
{
  "ref": "$GH_BRANCH",
  "task": "deploy",
  "auto_merge": false,
  "environment": "$GH_ENV",
  "required_contexts": [],
  "description": "Deployment queued from DockerHub post_checkout hook."
}
EOF

cat <<EOF >in_progress.json
{
  "environment": "$GH_ENV",
  "state": "in_progress",
  "description": "Deployment initiated from DockerHub build hook."
}
EOF

cat <<EOF >failure.json
{
  "environment": "$GH_ENV",
  "state": "failure",
  "description": "Deployment failed in DockerHub build hook."
}
EOF

cat <<EOF >success.json
{
  "environment": "$GH_ENV",
  "state": "success",
  "description": "Successful deployment from DockerHub post_build hook."
}
EOF

queue_content=$(<queue.json)

curl -sSL \
  -X POST \
  -H "Accept: application/vnd.github+json" \
  -H "Authorization: Bearer $GH_TOKEN" \
  -H "X-GitHub-Api-Version: 2022-11-28" \
  https://api.github.com/repos/$GH_OWNER/$GH_REPO/deployments \
  -d "$queue_content" \
  >response.json

echo "Deployment response..."
# cat response.json
deployment_id=$(cat response.json | jq '.id')
echo -e "\n\nDeployment ID: $deployment_id"

cat <<EOF >>gh_source
export GH_DEPLOYMENT_ID=$(cat response.json | jq '.id')
EOF
