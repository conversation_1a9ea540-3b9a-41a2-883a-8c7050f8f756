import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faExclamationTriangle } from "@fortawesome/free-solid-svg-icons";

interface ConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  cta: string;
  ctaBtnColor: string;
}

const ConfirmModal: React.FC<ConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  cta,
  ctaBtnColor,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-lg">
        <div className="flex flex-col items-center">
          <FontAwesomeIcon
            icon={faExclamationTriangle}
            className={`mb-2 text-3xl ${ctaBtnColor === "warning" ? "text-yellow-500" : ctaBtnColor === "danger" ? "text-red-500" : ctaBtnColor === "theme" ? "text-wbv-theme" : "text-gray-500"}`}
          />
          <h2 className="mb-2 text-center text-xl font-bold">{title}</h2>
          <div className={`mb-4 h-1 w-full bg-gradient-to-r from-transparent ${ctaBtnColor === "warning" ? "via-yellow-500" : ctaBtnColor === "danger" ? "via-red-500" : ctaBtnColor === "theme" ? "via-wbv-theme" : "via-gray-500"} to-transparent`}></div>
        </div>
        <p
          className="mb-4 text-center"
          dangerouslySetInnerHTML={{ __html: message }}
        ></p>
        <div className="mt-4 flex justify-center">
          <button
            type="button"
            onClick={onClose}
            className="mr-2 rounded bg-gray-500 p-2 text-white hover:bg-gray-600"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={onConfirm}
            className={`rounded p-2 text-white hover:bg-opacity-90 ${ctaBtnColor === "warning" ? "bg-yellow-500" : ctaBtnColor === "danger" ? "bg-red-500" : ctaBtnColor === "theme" ? "bg-wbv-theme" : ""}`}
          >
            {cta}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmModal;
