"use client";

import OnboardingTabs from "@/components/FormElements/OnboardingTabs";
import StepIndicator from "@/components/FormElements/StepIndicator";
import WBVFormHeader from "@/components/FormElements/WBVFormHeader";
import OnboardIndividualForm from "@/components/Forms/Onboarding/Individual/OnboardIndividualForm";
import AddAdminUserForm from "@/components/Forms/Onboarding/Organization/AddAdminUserForm";
import AddRegisteredUserForm from "@/components/Forms/Onboarding/Organization/AddRegisteredUserForm";
import OrganizationDetailsForm from "@/components/Forms/Onboarding/Organization/OrganizationDetailsForm";
import {
  adminUserSchema,
  organizationDetailsSchema,
  registeredUserSchema,
} from "@/components/Forms/Onboarding/schemas/schemas";
import OnboardingSummary from "@/components/Onboarding/summary/OnboardingSummary";
import AdminUsersTable from "@/components/Tables/Onboarding/AdminUsersTable";
import RegisteredUsersTable from "@/components/Tables/Onboarding/RegisteredUsersTable";
import { RegisterData, registerSchema } from "@/schemas/registerSchema";
import { registerUser } from "@/utils/auth";
import axios from "axios";
import React, { useEffect, useState } from "react";
import { toast, ToastContainer } from "react-toastify";
import { z } from "zod";
import { Role } from "../../../utils/types/core.whitebook.types";

interface BusinessType {
  id: number;
  name: string;
  description: string;
}

interface OrganizationAddress {
  street: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
}

const SignUpPage: React.FC = () => {
  // Individual signup
  const initialFormData: RegisterData = {
    first_name: "",
    last_name: "",
    email: "",
    password: "",
    phone_number: "",
    // country_code: "+1", // Default country code
  };
  const [showIndividialFormPassword, setShowIndividialFormPassword] =
    useState<boolean>(false);
  const [individialFormData, setIndividialFormData] =
    useState<RegisterData>(initialFormData);
  const [individialFormErrors, setIndividialFormErrors] = useState<{
    [key: string]: string;
  }>({});

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setIndividialFormData((prevData: any) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handlePhoneChange = (value: string) => {
    setIndividialFormData((prevData) => ({
      ...prevData,
      phone_number: value,
    }));
  };

  const handleIndividualSubmit = async (e: React.FormEvent, roles: Role[]) => {
    e.preventDefault();

    try {
      // Validate form data using zod
      registerSchema.parse(individialFormData);
      const individualClientRole = roles.find(
        (role) => role.name.toLowerCase() === "individual",
      );
      if (!individualClientRole) {
        throw new Error("Role not found");
      }

      // Add role_id and organization to the form data
      const formDataToSend = {
        ...individialFormData,
        role_id: individualClientRole.id,
      };

      // Send form data to backend
      const response = await registerUser(formDataToSend);
      if (response == "email already exists in database") {
        setToastError("User already exists with this email address.");
      }
      // Redirect to home page if status success
      else if (response.data && response.status === 201) {
        window.location.href = "/";
      } else {
        setToastError("Sign up failed, please contact admin.");
      }

      // Clear the form
      setIndividialFormData(initialFormData);
      setIndividialFormErrors({});
    } catch (error) {
      if (error instanceof z.ZodError) {
        const zodErrors: { [key: string]: string } = {};
        error.errors.forEach((err) => {
          if (err.path && err.path.length > 0) {
            zodErrors[err.path[0]] = err.message;
          }
        });
        setIndividialFormErrors(zodErrors);
      } else {
        console.error("Error during Register:", error);
        if (
          axios.isAxiosError(error) &&
          error.response &&
          error.response.data
        ) {
          setIndividialFormErrors(error.response.data.errors);
        }
      }
    }
  };

  // UI control elements state
  const [activeTab, setActiveTab] = useState<"individual" | "organization">(
    "individual",
  );
  const [currentStep, setCurrentStep] = useState<number>(1);

  // organization details state
  const [companyName, setCompanyName] = useState<string>("");
  const [organizationEmail, setOrganizationEmail] = useState<string>("");
  const [tpin, setTpin] = useState<string>("");
  const [phoneNumber, setPhoneNumber] = useState<string>("");
  const [address, setAddress] = useState<OrganizationAddress>({
    street: "",
    city: "",
    state: "",
    zip_code: "",
    country: "",
  });
  const [businessTypeName, setBusinessTypeName] = useState<string>("");
  const [businessTypeId, setBusinessTypeId] = useState<number>(0);
  const [incorporationDocuments, setIncorporationDocuments] = useState<File[]>(
    [],
  );

  // error state
  const [organizationDetailsFormErrors, setOrganizationDetailsFormErrors] =
    useState<{ [key: string]: string }>({});
  const [adminUserFormErrors, setAdminUserFormErrors] = useState<{
    [key: string]: string;
  }>({});
  const [registeredUserFormErrors, setRegisteredUserFormErrors] = useState<{
    [key: string]: string;
  }>({});
  const [adminUsersAddedError, setAdminUsersAddedError] = useState<string>("");
  const [registeredUsersAddedError, setRegisteredUsersAddedError] =
    useState<string>("");
  const [documentsAddedError, setDocumentsAddedError] = useState<string>("");

  // admin user state
  const [adminUsers, setAdminUsers] = useState<
    {
      adminUserTitle: string;
      adminUserFirstName: string;
      adminUserLastName: string;
      adminUserEmail: string;
    }[]
  >([]);

  const [adminUserTitle, setAdminUserTitle] = useState<string>("");
  const [adminUserFirstName, setAdminUserFirstName] = useState<string>("");
  const [adminUserLastName, setAdminUserLastName] = useState<string>("");
  const [adminUserEmail, setAdminUserEmail] = useState<string>("");

  // registered user state
  const [registeredUsers, setRegisteredUsers] = useState<
    {
      registeredUserTitle: string;
      registeredUserFirstName: string;
      registeredUserLastName: string;
      registeredUserEmail: string;
      registeredUserRoleId: number;
    }[]
  >([]);
  const [registeredUserTitle, setRegisteredUserTitle] = useState<string>("");
  const [registeredUserFirstName, setRegisteredUserFirstName] =
    useState<string>("");
  const [registeredUserLastName, setRegisteredUserLastName] =
    useState<string>("");
  const [registeredUserEmail, setRegisteredUserEmail] = useState<string>("");
  const [registeredUserRoleId, setRegisteredUserRoleId] = useState<number>(0);
  const [roles, setRoles] = useState<Role[]>([]);
  const [error, setError] = useState<any>(null);

  const [businessTypes, setBusinessTypes] = useState<BusinessType[]>([]);

  const [toastMessage, setToastMessage] = useState<string | null>(null);
  const [toastError, setToastError] = useState<string | null>(null);

  const [submitted, setSubmitted] = useState<boolean>(false);

  // State to manage accordion visibility
  const [visibleForm, setVisibleForm] = useState<"admin" | "registered" | null>(
    null,
  );
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    if (registeredUsers.length > 0) {
      setSuccessMessage(
        `${registeredUsers.length} ${registeredUsers.length === 1 ? "user has" : "users have"} been added`,
      );
    } else {
      setSuccessMessage(null);
    }
  }, [registeredUsers]);

  if (toastMessage) {
    toast.success(toastMessage, {
      position: "bottom-right",
    });
    setToastMessage(null);
  }
  if (toastError) {
    toast.error(toastError, {
      position: "bottom-right",
    });
    setToastError(null);
  }

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch business types
        const businessTypesResponse = await fetch("/api/organizationTypes", {
          method: "GET",
        });
        const businessTypesData = await businessTypesResponse.json();
        if (!businessTypesResponse.ok) {
          throw new Error(
            `HTTP error! status: ${businessTypesResponse.status}`,
          );
        }
        setBusinessTypes(businessTypesData.data);

        // Fetch roles data
        const rolesResponse = await fetch("/api/roles", { method: "GET" });
        if (!rolesResponse.ok) {
          throw new Error(`HTTP error! status: ${rolesResponse.status}`);
        }
        const rolesResult = await rolesResponse.json();
        const filteredRoles = rolesResult.data.filter(
          (role: Role) => role.name.toLowerCase() !== "super admin",
        );
        setRoles(filteredRoles);
      } catch (error) {
        console.error("Error fetching data:", error);
        setError(error);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const selectedBusinessType = businessTypes.find(
      (type) => type.id === businessTypeId,
    );
    if (selectedBusinessType) {
      setBusinessTypeName(selectedBusinessType.name);
    }
  }, [businessTypeId, businessTypes]);

  const handleTabChange = (tab: "individual" | "organization") => {
    setActiveTab(tab);
    setCurrentStep(1);
  };

  const handleNextStep = () => {
    if (activeTab === "organization" && currentStep === 1) {
      const validationResult = organizationDetailsSchema.safeParse({
        companyName,
        organizationEmail,
        tpin,
        phoneNumber,
        address: {
          street: address.street,
          city: address.city,
          state: address.state,
          zip_code: address.zip_code,
          country: address.country,
        },
        businessTypeId,
        incorporationDocuments,
      });

      if (!validationResult.success) {
        const newErrors: { [key: string]: string } = {};
        validationResult.error.errors.forEach((error) => {
          console.log("error in handleNextStep", error);
          const path = error.path.join(".");
          newErrors[path] = error.message;
        });
        if (
          Object.keys(newErrors).length === 1 &&
          newErrors.hasOwnProperty("incorporationDocuments") &&
          incorporationDocuments.length > 0
        ) {
          setDocumentsAddedError("");
          setCurrentStep((prevStep) => prevStep + 1);
          return;
        }
        console.log("newErrors", newErrors);
        setOrganizationDetailsFormErrors(newErrors);
        return;
      } else {
        setOrganizationDetailsFormErrors({});
      }

      if (incorporationDocuments.length === 0) {
        setDocumentsAddedError(
          "At least one incorporation document is required",
        );
        return;
      } else {
        setDocumentsAddedError("");
      }
    } else if (activeTab === "organization" && currentStep === 2) {
      if (adminUsers.length === 0) {
        setAdminUsersAddedError("At least one admin user is required");
        return;
      }
      if (registeredUsers.length === 0) {
        setRegisteredUsersAddedError(
          "At least one registered user is required",
        );
        return;
      }
    }
    setCurrentStep((prevStep) => prevStep + 1);
  };

  const handlePrevStep = () => {
    setCurrentStep((prevStep) => prevStep - 1);
  };

  const handleStepChange = (step: number) => {
    setCurrentStep(step);
  };

  const handleAddAdminUser = (
    adminUserTitle: string,
    adminUserFirstName: string,
    adminUserLastName: string,
    adminUserEmail: string,
  ) => {
    const validationResult = adminUserSchema.safeParse({
      adminUserTitle,
      adminUserFirstName,
      adminUserLastName,
      adminUserEmail,
    });
    if (!validationResult.success) {
      const newErrors: { [key: string]: string } = {};
      validationResult.error.errors.forEach((error) => {
        newErrors[error.path[0]] = error.message;
      });
      setAdminUserFormErrors(newErrors);
      return;
    } else {
      setAdminUserFormErrors({});
      setAdminUsers([
        ...adminUsers,
        {
          adminUserTitle,
          adminUserFirstName,
          adminUserLastName,
          adminUserEmail,
        },
      ]);
    }
  };

  const handleAddRegisteredUser = (
    registeredUserTitle: string,
    registeredUserFirstName: string,
    registeredUserLastName: string,
    registeredUserEmail: string,
    registeredUserRoleId: number,
  ) => {
    // Check if the email already exists in registeredUsers
    const emailExists = registeredUsers.some(
      (user) => user.registeredUserEmail === registeredUserEmail,
    );

    if (emailExists) {
      setRegisteredUserFormErrors({
        registeredUserEmail: "Email has already been added",
      });
      return;
    }

    const validationResult = registeredUserSchema.safeParse({
      registeredUserTitle,
      registeredUserFirstName,
      registeredUserLastName,
      registeredUserEmail,
      registeredUserRoleId,
    });

    if (!validationResult.success) {
      const newErrors: { [key: string]: string } = {};
      validationResult.error.errors.forEach((error) => {
        newErrors[error.path[0]] = error.message;
      });
      setRegisteredUserFormErrors(newErrors);
      return;
    } else {
      setRegisteredUserFormErrors({});
      const role =
        roles.find((role) => role.id === registeredUserRoleId)?.name || "";
      setRegisteredUsers([
        ...registeredUsers,
        {
          registeredUserTitle,
          registeredUserFirstName,
          registeredUserLastName,
          registeredUserEmail,
          registeredUserRoleId: registeredUserRoleId,
        },
      ]);
      // Clear the form fields only after successful addition
      setRegisteredUserTitle("");
      setRegisteredUserFirstName("");
      setRegisteredUserLastName("");
      setRegisteredUserEmail("");
      setRegisteredUserRoleId(0);
      setVisibleForm(null);
    }
  };

  const handleEditAdminUser = (
    index: number,
    updatedUser: {
      adminUserTitle: string;
      adminUserFirstName: string;
      adminUserLastName: string;
      adminUserEmail: string;
    },
  ) => {
    const updatedAdminUsers = [...adminUsers];
    updatedAdminUsers[index] = updatedUser;
    setAdminUsers(updatedAdminUsers);
  };

  const handleRemoveAdminUser = (index: number) => {
    const updatedAdminUsers = adminUsers.filter((_, i) => i !== index);
    setAdminUsers(updatedAdminUsers);
  };

  const handleEditRegisteredUser = (
    index: number,
    updatedUser: {
      registeredUserTitle: string;
      registeredUserFirstName: string;
      registeredUserLastName: string;
      registeredUserEmail: string;
      registeredUserRoleId: number;
    },
  ) => {
    const updatedRegisteredUsers = [...registeredUsers];
    updatedRegisteredUsers[index] = updatedUser;
    setRegisteredUsers(updatedRegisteredUsers);
  };

  const handleRemoveRegisteredUser = (index: number) => {
    const updatedRegisteredUsers = registeredUsers.filter(
      (_, i) => i !== index,
    );
    setRegisteredUsers(updatedRegisteredUsers);
  };

  const handleAddDocument = (file: File) => {
    setIncorporationDocuments([...incorporationDocuments, file]);
  };

  const handleRemoveDocument = (index: number) => {
    const updatedDocuments = incorporationDocuments.filter(
      (_, i) => i !== index,
    );
    setIncorporationDocuments(updatedDocuments);
  };

  const handleOrganizationSubmit = async () => {
    const formData = new FormData();
    formData.append("companyName", companyName);
    formData.append("tpin", tpin);
    formData.append("phoneNumber", phoneNumber);
    formData.append("organizationEmail", organizationEmail);
    formData.append("address", JSON.stringify(address));
    formData.append("businessTypeId", businessTypeId.toString());

    incorporationDocuments.forEach((file) => {
      formData.append("incorporationDocuments", file);
    });
    formData.append("adminUsers", JSON.stringify(adminUsers));
    formData.append("registeredUsers", JSON.stringify(registeredUsers));
    try {
      const response = await fetch("/api/onboard/organization", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const responseJson = await response.json()
        if (responseJson.error == 'email already exists in database') {
          setToastError("User with this email already exists");
        }
        else if( responseJson.error == 'invalid document type'){
          setToastError("Invalid document type submitted. Please upload the correct document type.");     
        } else if (responseJson.error == 'organization name already exists in database') {
          setToastError("Organization name already exists in database");
        }
        else {
          setToastError("An error occurred. Please try again.");
        }
        setSubmitted(false);
      } else {
        setSubmitted(true);
        setToastMessage("Onboarding request submitted Successfully. Please wait for an approval email.");
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  const isStep2Disabled =
    !companyName ||
    !organizationEmail ||
    !tpin ||
    !phoneNumber ||
    !address ||
    !businessTypeId ||
    incorporationDocuments.length === 0;

  const isStep3Disabled =
    isStep2Disabled || adminUsers.length === 0 || registeredUsers.length === 0;

  return (
    <>
      <div className="flex min-h-screen items-center justify-center bg-gray-100">
        <div
          className={`my-10 w-full ${activeTab === "organization" ? "max-w-3xl" : "max-w-md"} space-y-6 rounded-lg bg-white px-8 pb-8 pt-0 shadow-md`}
        >
          <WBVFormHeader title="Sign Up" />
          <OnboardingTabs
            activeTab={activeTab}
            handleTabChange={handleTabChange}
            tab1="Individual"
            tab2="Organization"
          />
          {activeTab === "organization" && (
            <div className="mb-6 flex w-full justify-between">
              <StepIndicator
                step={1}
                currentStep={currentStep}
                onClick={() => handleStepChange(1)}
                title="Details"
              />
              <StepIndicator
                step={2}
                currentStep={currentStep}
                onClick={() => handleStepChange(2)}
                title="Users"
                disabled={isStep2Disabled}
              />
              <StepIndicator
                step={3}
                currentStep={currentStep}
                onClick={() => handleStepChange(3)}
                title="Summary"
                disabled={isStep3Disabled}
              />
            </div>
          )}
          {activeTab === "individual" ? (
            <OnboardIndividualForm
              formData={individialFormData}
              errors={individialFormErrors}
              handleChange={handleChange}
              handlePhoneChange={handlePhoneChange}
              showPassword={showIndividialFormPassword}
              setShowPassword={setShowIndividialFormPassword}
              handleSubmit={handleIndividualSubmit}
              roles={roles}
            />
          ) : (
            <>
              {currentStep === 1 && (
                <OrganizationDetailsForm
                  companyName={companyName}
                  tpin={tpin}
                  phoneNumber={phoneNumber}
                  email={organizationEmail}
                  address={address}
                  incorporationDocuments={incorporationDocuments}
                  setCompanyName={setCompanyName}
                  setTpin={setTpin}
                  setPhoneNumber={setPhoneNumber}
                  setEmail={setOrganizationEmail}
                  setAddress={setAddress}
                  handleAddDocument={handleAddDocument}
                  handleRemoveDocument={handleRemoveDocument}
                  errors={organizationDetailsFormErrors}
                  documentsAddedError={documentsAddedError}
                  businessTypes={businessTypes}
                  businessTypeId={businessTypeId}
                  setBusinessTypeId={setBusinessTypeId}
                />
              )}
              {currentStep === 2 && (
                <div>
                  <div className="flex flex-col space-y-4 md:flex-row md:space-x-4 md:space-y-0">
                    <div
                      className={`w-full rounded-md bg-white p-4 shadow-md md:w-1/2 ${visibleForm === "admin" ? "h-auto" : "h-20"}`}
                    >
                      <h3
                        className={`cursor-pointer rounded-md px-4 py-2 text-lg font-semibold ${visibleForm === "admin" ? "bg-gray-200" : "bg-gray-100 hover:bg-gray-200"}`}
                        onClick={() =>
                          setVisibleForm(
                            visibleForm === "admin" ? null : "admin",
                          )
                        }
                      >
                        Add Admin User
                      </h3>
                      {visibleForm === "admin" &&
                        (adminUsers.length === 0 ? (
                          <AddAdminUserForm
                            handleAddAdminUser={handleAddAdminUser}
                            adminUserTitle={adminUserTitle}
                            setAdminUserTitle={setAdminUserTitle}
                            adminUserFirstName={adminUserFirstName}
                            setAdminUserFirstName={setAdminUserFirstName}
                            adminUserLastName={adminUserLastName}
                            setAdminUserLastName={setAdminUserLastName}
                            adminUserEmail={adminUserEmail}
                            setAdminUserEmail={setAdminUserEmail}
                            errors={adminUserFormErrors}
                          />
                        ) : (
                          <p className="text-sm text-gray-500">
                            An admin user has been added.
                          </p>
                        ))}
                    </div>
                    <div
                      className={`w-full rounded-md bg-white p-4 shadow-md md:w-1/2 ${visibleForm === "registered" ? "h-auto" : "h-20"}`}
                    >
                      <h3
                        className={`cursor-pointer rounded-md px-4 py-2 text-lg font-semibold ${visibleForm === "registered" ? "bg-gray-200" : "bg-gray-100 hover:bg-gray-200"}`}
                        onClick={() =>
                          setVisibleForm(
                            visibleForm === "registered" ? null : "registered",
                          )
                        }
                      >
                        Add Registered Users
                      </h3>
                      {visibleForm === "registered" && (
                        <>
                          {successMessage && (
                            <p className="text-sm text-green-500">
                              {successMessage}
                            </p>
                          )}
                          <AddRegisteredUserForm
                            handleAddRegisteredUser={handleAddRegisteredUser}
                            registeredUserTitle={registeredUserTitle}
                            setRegisteredUserTitle={setRegisteredUserTitle}
                            registeredUserFirstName={registeredUserFirstName}
                            setRegisteredUserFirstName={
                              setRegisteredUserFirstName
                            }
                            registeredUserLastName={registeredUserLastName}
                            setRegisteredUserLastName={
                              setRegisteredUserLastName
                            }
                            registeredUserEmail={registeredUserEmail}
                            setRegisteredUserEmail={setRegisteredUserEmail}
                            registeredUserRoleId={registeredUserRoleId}
                            setRegisteredUserRoleId={setRegisteredUserRoleId}
                            errors={registeredUserFormErrors}
                            roles={roles}
                          />
                        </>
                      )}
                    </div>
                  </div>
                  {adminUsers.length > 0 ? (
                    <div className="mt-6">
                      <h3 className="text-lg font-semibold">Admin User</h3>
                      <AdminUsersTable
                        users={adminUsers}
                        handleEditUser={handleEditAdminUser}
                        handleRemoveUser={handleRemoveAdminUser}
                      />
                    </div>
                  ) : (
                    <div className="mt-6">
                      <p className="text-sm text-red-500">
                        {adminUsersAddedError}
                      </p>
                    </div>
                  )}
                  {registeredUsers.length > 0 ? (
                    <div className="mt-6">
                      <h3 className="text-lg font-semibold">
                        Registered Users
                      </h3>
                      <RegisteredUsersTable
                        roles={roles}
                        users={registeredUsers}
                        handleEditUser={handleEditRegisteredUser}
                        handleRemoveUser={handleRemoveRegisteredUser}
                      />
                    </div>
                  ) : (
                    <div className="mt-6">
                      <p className="text-sm text-red-500">
                        {registeredUsersAddedError}
                      </p>
                    </div>
                  )}
                </div>
              )}
              {currentStep === 3 && !submitted ? (
                <OnboardingSummary
                  companyName={companyName}
                  tpin={tpin}
                  phoneNumber={phoneNumber}
                  organizationEmail={organizationEmail}
                  address={address}
                  businessTypeName={businessTypeName}
                  adminUsers={adminUsers}
                  registeredUsers={registeredUsers}
                  incorporationDocuments={incorporationDocuments}
                  handlePrevStep={handlePrevStep}
                  handleSubmit={handleOrganizationSubmit}
                  roles={roles}
                />
              ) : currentStep === 3 && submitted ? (
                <div className="text-center">
                  <div className="mb-2 flex justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-8 w-8 text-green-500"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 00-1.414 0L9 11.586 5.707 8.293a1 1 0 10-1.414 1.414l4 4a1 1 0 001.414 0l7-7a1 1 0 000-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <p className="text-lg font-semibold text-wbv-theme">
                    Onboarding request submitted Successfully.
                  </p>
                </div>
              ) : null}
              <div className="mt-4 flex justify-between">
                {currentStep > 1 && currentStep < 3 && (
                  <button
                    className="rounded-md bg-gray-200 px-4 py-2 font-semibold text-gray-700 transition duration-300 hover:bg-gray-300"
                    onClick={handlePrevStep}
                  >
                    Previous
                  </button>
                )}
                {currentStep < 3 && (
                  <button
                    className={`rounded-md bg-wbv-theme px-4 py-2 font-semibold text-white transition duration-300 hover:bg-opacity-90 ${currentStep === 1 ? "ml-auto" : ""}`}
                    onClick={handleNextStep}
                  >
                    Next
                  </button>
                )}
              </div>
            </>
          )}
        </div>
      </div>
      <ToastContainer />
    </>
  );
};

export default SignUpPage;
