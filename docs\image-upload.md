# Product Image Upload Documentation

This document provides detailed information about the product image upload functionality and its integration with MinIO in the WhitebookValue Admin Portal.

## Overview

The WhitebookValue Admin Portal allows users to upload, view, and manage product images. These images are stored in a MinIO object storage server, which provides an S3-compatible API for storing and retrieving objects. When removing an image from a product, only the reference to the image is removed from the backend, while the actual image file remains in storage for potential future use.

## Configuration

The image upload functionality relies on the following environment variables:

| Variable | Description | Example |
|----------|-------------|---------|
| `S3_ENDPOINT` | MinIO server endpoint | `minio.example.com` |
| `S3_PORT` | MinIO server port |
| `S3_USE_SSL` | Whether to use SSL for MinIO connections | `true` |
| `S3_ACCESS_KEY` | MinIO access key | `minioadmin` |
| `S3_SECRET_KEY` | MinIO secret key | `minioadmin` |
| `S3_BUCKET` | MinIO bucket name | `whitebook` |
| `S3_PRODUCT_IMAGES_PATH` | Base path for product images | `product_images` |

These variables should be defined in your `.env` file or environment configuration.

## API Endpoints

### Upload Image

Uploads a product image to MinIO storage.

**Endpoint:** `POST /api/products/upload-image`

**Request:**
- Content-Type: `multipart/form-data`
- Body:
  - `image`: The image file to upload
  - `productId` (optional): The ID of the product to associate with the image

**Response:**
- Status: `201 Created` on success
- Body:
  ```json
  {
    "name": "example.jpg",
    "type": ".jpg",
    "is_default": false || true, 
    "file": "example.jpg",
    "path": "product_images/123/1620000000000-example.jpg"
  }
  ```

**Error Responses:**
- `400 Bad Request`: No image file provided
- `500 Internal Server Error`: Server configuration error or upload failure

**Example:**
```javascript
// Using fetch API
const formData = new FormData();
formData.append("image", imageFile);
formData.append("productId", "123");

const response = await fetch('/api/products/upload-image', {
  method: 'POST',
  body: formData,
});

const result = await response.json();
```

### View Image

Retrieves a product image from MinIO storage.

**Endpoint:** `GET /api/products/view/{path}`

**Parameters:**
- `path`: The path to the image in MinIO storage

**Response:**
- Redirects to a presigned URL for the image
- The presigned URL is valid for 24 hours

**Error Responses:**
- `400 Bad Request`: Image path is required
- `500 Internal Server Error`: Failed to process image request

**Example:**
```html
<img src="/api/products/view/product_images/123/1620000000000-example.jpg" alt="Product Image" />
```

### Delete Image Path

Removes a product image path from the backend without deleting the actual image from MinIO storage.

**Endpoint:** `DELETE /api/products/delete-image`

**Request:**
- Content-Type: `application/json`
- Body:
  ```json
  {
    "path": "product_images/123/1620000000000-example.jpg",
    "productId": "123"
  }
  ```

**Response:**
- Status: `200 OK` on success
- Body:
  ```json
  {
    "success": true,
    "message": "Image path removed successfully",
    "path": "product_images/123/1620000000000-example.jpg"
  }
  ```

**Error Responses:**
- `400 Bad Request`: Image path is required
- `500 Internal Server Error`: Failed to remove image path

**Example:**
```javascript
// Using fetch API
const response = await fetch('/api/products/delete-image', {
  method: 'DELETE',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    path: 'product_images/123/1620000000000-example.jpg',
    productId: '123',
  }),
});

const result = await response.json();
```

## Frontend Integration

### ProductModal Component

The `ProductModal` component in `src/components/Modals/ProductModal.tsx` provides the UI for uploading product images. It handles:

1. Selecting images from the file system
2. Uploading images to the server
3. Displaying upload progress and status
4. Managing existing images

### Image Upload Process

1. User selects one or more image files using the file input
2. For each file:
   - A temporary local ID is generated
   - The file is added to the state with 'uploading' status
   - A FormData object is created with the image and productId
   - The file is uploaded to the server
   - The state is updated with the upload result (success or error)
3. On form submission, both existing and newly uploaded images are included in the product data

### Example Usage

```jsx
// In a component that needs to upload images
const handleImageUpload = async (event) => {
  const files = event.target.files;
  if (!files || files.length === 0) return;

  const filesToUpload = Array.from(files);

  for (const file of filesToUpload) {
    const formData = new FormData();
    formData.append("image", file);
    if (productId) {
      formData.append("productId", productId);
    }

    try {
      const response = await fetch('/api/products/upload-image', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || `Upload failed with status ${response.status}`);
      }

      // Handle successful upload
      console.log('Upload successful:', result);
    } catch (error) {
      // Handle upload error
      console.error('Upload failed:', error);
    }
  }
};
```

## Error Handling

The image upload functionality includes comprehensive error handling:

1. **Client-side validation**:
   - Checks if files are selected
   - Updates UI with upload status (uploading, success, error)
   - Displays error messages for failed uploads

2. **Server-side validation**:
   - Checks if MinIO environment variables are configured
   - Validates that an image file is provided
   - Handles MinIO errors with specific error messages

3. **Common errors and solutions**:
   - `No image file provided`: Ensure you're sending a file in the 'image' field
   - `Server configuration error`: Check MinIO environment variables
   - `MinIO Error (AccessDenied)`: Check MinIO credentials and bucket permissions
   - `MinIO Error (NoSuchBucket)`: Ensure the bucket exists in MinIO
   - `Upload failed`: Check network connection and server logs

## Troubleshooting

### Image Upload Fails

1. Check that MinIO is running and accessible
2. Verify that the environment variables are correctly set
3. Ensure the bucket exists in MinIO
4. Check that the user has permission to write to the bucket
5. Verify that the image file is valid and not corrupted
6. Check the server logs for specific error messages

### Image Not Displaying

1. Verify that the image path is correct
2. Check that MinIO is running and accessible
3. Ensure the image exists in MinIO
4. Verify that the user has permission to read from the bucket
5. Check the browser console for network errors

## Security Considerations

1. **Access Control**: MinIO credentials are stored in environment variables and not exposed to clients
2. **File Validation**: The server validates that an image file is provided
3. **Unique Filenames**: Uploaded files are given unique names to prevent collisions
4. **Presigned URLs**: Images are served via presigned URLs with expiration times
5. **Error Handling**: Error messages are sanitized to prevent information disclosure

## Performance Considerations

1. **File Size**: Consider implementing file size limits for uploads
2. **Image Optimization**: Consider implementing image optimization before storage
3. **Caching**: Consider implementing caching for frequently accessed images
4. **Batch Uploads**: The frontend supports uploading multiple files in parallel
