// components/VoiceFlowChat.tsx
'use client';

import { useEffect } from 'react';

interface VoiceFlowConfig {
  verify: {
    projectID: string;
  };
  url: string;
  versionID: string;
  voice: {
    url: string;
  };
}

interface VoiceFlowWindow extends Window {
  voiceflow?: {
    chat: {
      load: (config: VoiceFlowConfig) => void;
    };
  };
}

declare const window: VoiceFlowWindow;

export default function VoiceFlowChat() {
  useEffect(() => {
    const loadVoiceFlow = () => {
      const scriptElement = document.createElement('script');
      const firstScript = document.getElementsByTagName('script')[0];

      scriptElement.onload = function () {
        if (window.voiceflow?.chat) {
          window.voiceflow.chat.load({
            verify: { 
              projectID: process.env.NEXT_PUBLIC_VOICE_FLOW_PROJECT_ID || ''
            },
            url: process.env.NEXT_PUBLIC_GENERAL_RUNTIME_URL || '',
            versionID: 'production',
            voice: {
              url: process.env.NEXT_PUBLIC_RUNTIME_URL || ''
            }
          });
        }
      };

      scriptElement.src = process.env.NEXT_PUBLIC_BUNDLE_URL || '';
      scriptElement.type = 'text/javascript';

      if (firstScript?.parentNode) {
        firstScript.parentNode.insertBefore(scriptElement, firstScript);
      }
    };

    loadVoiceFlow();

    return () => {
      // Cleanup function to remove script tag
      const existingScript = document.querySelector(`script[src="${process.env.NEXT_PUBLIC_BUNDLE_URL}"]`);
      if (existingScript?.parentNode) {
        existingScript.parentNode.removeChild(existingScript);
      }
    };
  }, []);

  return null;
}
