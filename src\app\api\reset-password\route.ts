import { NextResponse } from "next/server";

export async function POST(request: Request) {
  type ResetPasswordRequestBody = {
    user_id: number;
    email: string;
    password: string;
    confirmPassword: string;
    key: string;
  };
  try {
    const body: ResetPasswordRequestBody = await request.json();
    const { user_id, email, password, confirmPassword, key } = body;

    if (password !== confirmPassword) {
      return NextResponse.json(
        { error: "Passwords do not match." },
        { status: 400 },
      );
    }

    const resetPasswordResponse = await fetch(
      `${process.env.WHITEBOOK_API_URL_V2}/auth/reset-password`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          user_id,
          email,
          new_password: password,
          confirm_password: confirmPassword,
          key,
        }),
      },
    );

    if (!resetPasswordResponse.ok) {
      console.error(
        "Failed to reset password:",
        resetPasswordResponse.status,
        resetPasswordResponse.statusText,
      );

      return NextResponse.json(
        { error: "Failed to reset password." },
        { status: resetPasswordResponse.status },
      );
    }

    return NextResponse.json(
      { message: "Password has been reset successfully." },
      { status: 200 },
    );
  } catch (error) {
    console.error("Error handling reset password request:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred. Please try again." },
      { status: 500 },
    );
  }
}
