"use client";

import React, { useEffect, useState } from "react";
import Link from "next/link";
import { useParams } from "next/navigation";

interface PaymentResponse {
  id: number;
  amount: number;
  transaction_id: string;
  payment_id: number;
  external_id: string;
  narration: string;
  final_status: number;
  payer_number: string;
  account_number: string;
  response_message: string;
}

interface PaymentData {
  amount: number;
  created_at: string;
  created_by: number;
  currency_id: number;
  deleted_at: string | null;
  deleted_by: number;
  id: number;
  invoice_id: number;
  payment_response: PaymentResponse;
  status: string;
  updated_at: string;
  updated_by: number;
}

const CallbackPage: React.FC = () => {
  const [paymentStatus, setPaymentStatus] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [paymentData, setPaymentData] = useState<PaymentData | null>(null);
  const params = useParams();
  const id = params.id;

  const fetchPaymentData = async () => {
    if (!id) {
      setError("Invalid payment ID");
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const url = `/api/billing/payments/${id}`;
      console.log(`Fetching payment data from URL: ${url}`);

      const response = await fetch(url, {
        method: "GET",
      });

      if (!response.ok) {
        throw new Error("Failed to fetch payment data");
      }

      const responseData = await response.json();
      const paymentData: PaymentData = responseData.data;
      setPaymentStatus(paymentData.payment_response.final_status);
      setPaymentData(paymentData);
    } catch (error) {
      console.error("Failed to fetch payment data", error);
      setError("An unexpected error occurred.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPaymentData();
  }, [id]);

  return (
    <div className="mx-auto max-w-md p-6">
      <div className="mb-10 text-center">
        <img
          src="/images/logo/whitebook-value-logo.png"
          alt="Logo"
          className="mx-auto max-h-45"
        />
        <hr
          className="mx-auto mt-4 w-full border-0"
          style={{
            height: "1px",
            background:
              "linear-gradient(to right, transparent, #06b6d4, transparent)",
          }}
        />
      </div>
      {loading ? (
        <div className="flex items-center justify-center">
          <div role="status">
            <svg
              aria-hidden="true"
              className="h-8 w-8 animate-spin fill-wbv-theme text-gray-200 dark:text-gray-600"
              viewBox="0 0 100 101"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                fill="currentColor"
              />
              <path
                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                fill="currentFill"
              />
            </svg>
            <span className="sr-only">Loading...</span>
          </div>
          <p className="ml-2 text-wbv-theme">Processing your payment...</p>
        </div>
      ) : error ? (
        <p className="text-red-500">{error}</p>
      ) : paymentStatus === 300 ? (
        <div>
          <p className="font-semibold text-green-500">Payment successful</p>
          <div className="mt-4 rounded border bg-green-50 p-4">
            <h2 className="mb-4 text-lg font-semibold">Payment Summary</h2>
            <p>
              <strong>Transaction ID:</strong>{" "}
              {paymentData?.payment_response.transaction_id}
            </p>
            <p>
              <strong>Amount:</strong> {paymentData?.amount}
            </p>
            <p>
              <strong>External ID:</strong>{" "}
              {paymentData?.payment_response.external_id}
            </p>
            <p>
              <strong>Narration:</strong>{" "}
              {paymentData?.payment_response.narration}
            </p>
            <p>
              <strong>Payer Number:</strong>{" "}
              {paymentData?.payment_response.payer_number}
            </p>
            <p>
              <strong>Account Number:</strong>{" "}
              {paymentData?.payment_response.account_number}
            </p>
            <p>
              <strong>Response Message:</strong>{" "}
              {paymentData?.payment_response.response_message}
            </p>
          </div>
        </div>
      ) : paymentStatus === 301 ? (
        <div>
          <p className="font-semibold text-red-500">Payment failed</p>
          <div className="mt-4 rounded border bg-red-50 p-4">
            <h2 className="mb-4 text-lg font-semibold">Payment Summary</h2>
            <p>
              <strong>Transaction ID:</strong>{" "}
              {paymentData?.payment_response.transaction_id}
            </p>
            <p>
              <strong>Amount:</strong> {paymentData?.amount}
            </p>
            <p>
              <strong>External ID:</strong>{" "}
              {paymentData?.payment_response.external_id}
            </p>
            <p>
              <strong>Narration:</strong>{" "}
              {paymentData?.payment_response.narration}
            </p>
            <p>
              <strong>Payer Number:</strong>{" "}
              {paymentData?.payment_response.payer_number}
            </p>
            <p>
              <strong>Account Number:</strong>{" "}
              {paymentData?.payment_response.account_number}
            </p>
            <p>
              <strong>Response Message:</strong>{" "}
              {paymentData?.payment_response.response_message}
            </p>
          </div>
        </div>
      ) : (
        <p className="text-red-500">
          Payment failed with status code {paymentStatus}
        </p>
      )}
      <div className="mt-6 text-right">
        <Link href="/billing" className="text-blue-500 hover:underline">
          Back to Billing
        </Link>
      </div>
    </div>
  );
};

export default CallbackPage;
