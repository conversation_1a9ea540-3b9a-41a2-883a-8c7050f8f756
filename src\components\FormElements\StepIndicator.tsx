import React from 'react';

interface StepIndicatorProps {
    step: number;
    currentStep: number;
    onClick: () => void;
    title: string;
    disabled?: boolean;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({ step, currentStep, onClick, title, disabled }) => (
    <div
        className={`flex flex-col items-center cursor-pointer w-full ${disabled ? 'cursor-not-allowed' : ''}`}
        onClick={!disabled ? onClick : undefined}
    >
        <div className="flex items-center w-full ml-17 md:ml-50">
            <div className={`w-8 h-8 flex items-center justify-center rounded-full border-2 ${currentStep >= step ? 'border-wbv-theme bg-wbv-theme text-white' : 'border-gray-300 text-gray-500'}`}>
                {step}
            </div>
            {step < 3 && <div className={`flex-1 h-0.5 ${currentStep > step ? 'bg-wbv-theme' : 'bg-gray-300'}`}></div>}
        </div>
        <span className="mt-2 text-sm font-medium text-gray-700">{title}</span>
    </div>
);

export default StepIndicator;