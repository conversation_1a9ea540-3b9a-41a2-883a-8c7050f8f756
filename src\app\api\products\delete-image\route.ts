import { NextResponse } from 'next/server';
import type { APIErrorResponse } from "@/utils/axios";

export async function DELETE(request: Request) {
  try {
    const body = (await request.json()) as { path?: unknown };
    const path = typeof body.path === "string" ? body.path : undefined;

    if (!path) {
      return NextResponse.json({ error: 'Image path is required' }, { status: 400 });
    }

    // Clean the path (remove any leading slashes)
    const cleanPath = path.replace(/^\/+/, '');

    return NextResponse.json({
      success: true,
      message: 'Image path removed successfully',
      path: cleanPath
    });
  } catch (error: unknown) {
    const err = error as APIErrorResponse;
    let code = 'UNKNOWN';
    if (err && typeof err === "object" && "code" in err && typeof (err as { code?: string }).code === "string") {
      code = (err as { code: string }).code;
    }

    return NextResponse.json(
      {
        error: err && typeof err === "object" && "message" in err && typeof (err as { message?: string }).message === "string"
          ? (err as { message: string }).message
          : 'Failed to remove image path',
        code
      },
      { status: 500 }
    );
  }
}