"use client";

import React, { useState } from "react";
import Image from "next/image";
import axios from "axios";
import { z } from "zod";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEnvelope } from "@fortawesome/free-solid-svg-icons";

const forgotPasswordSchema = z.object({
  email: z.string().email("Invalid email address"),
});

type ForgotPasswordData = z.infer<typeof forgotPasswordSchema>;

const initialFormData = {
  email: "",
};

const ForgotPasswordPage: React.FC = () => {
  const [formData, setFormData] = useState<ForgotPasswordData>(initialFormData);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [message, setMessage] = useState<string>("");

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    setMessage("");
    e.preventDefault();

    try {
      // Validate form data using zod
      forgotPasswordSchema.parse(formData);

      // Send form data to backend
      const response = await axios.post("/api/forgot-password", formData, {
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.status === 200) {
        setMessage("Password reset link has been sent to your email.");
        setFormData(initialFormData);
        setErrors({});
      } else {
        setErrors({ general: "Failed to send password reset link." });
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const zodErrors = error.errors.reduce((acc: any, curr: any) => {
          acc[curr.path[0]] = curr.message;
          return acc;
        }, {});
        setErrors(zodErrors);
      } else {
        console.error("Error during password reset:", error);
        if (
          axios.isAxiosError(error) &&
          error?.response &&
          error?.response?.data
        ) {
          setErrors({ general: "Failed to send password reset link." });
        } else {
          setErrors({
            general: "An unexpected error occurred. Please try again.",
          });
        }
      }
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-100">
      <div className="my-10 w-full max-w-md space-y-6 rounded-lg bg-white px-8 pb-8 pt-0 shadow-md">
        <div className="mb-10 text-center">
          <Image
            src="/images/logos/Whitebook ALT 8.png"
            priority
            width={200} // This will be overridden by CSS
            height={800} // This will be overridden by CSS
            alt="Logo"
            className="mx-auto h-[800px] max-h-45 w-auto"
          />
          <h2 className="mt-2 text-2xl font-bold text-gray-900">
            Forgot Password
          </h2>
          <hr
            className="mx-auto mt-4 w-full border-0"
            style={{
              height: "1px",
              background:
                "linear-gradient(to right, transparent, #06b6d4, transparent)",
            }}
          />
        </div>
        <form onSubmit={handleSubmit} className="space-y-4">
          {errors.general && (
            <div className="rounded bg-red-100 p-4 text-red-700">
              <p className="text-center text-sm">{errors.general}</p>
            </div>
          )}
          {message && (
            <div className="rounded bg-green-100 p-4 text-green-700">
              <p className="text-center text-sm">{message}</p>
            </div>
          )}
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700"
            >
              Email
            </label>
            <div className="relative">
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 text-sm leading-5">
                <FontAwesomeIcon icon={faEnvelope} />
              </div>
            </div>
            {errors.email && (
              <p className="mt-1 text-xs text-red-500">{errors.email}</p>
            )}
          </div>
          <button
            type="submit"
            className="w-full rounded-md bg-wbv-theme px-4 py-2 text-white transition duration-300 hover:bg-opacity-90"
          >
            Send Reset Link
          </button>
        </form>
        <div className="border-t pt-2 text-center">
          <p className="text-sm text-gray-600">
            Remember your password?&nbsp;
            <a href="/auth/sign-in" className="text-blue-500 hover:underline">
              Sign In
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
