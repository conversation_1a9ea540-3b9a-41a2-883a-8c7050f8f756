import React from "react";
import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
  getFilteredRowModel,
  getSortedRowModel,
  Updater,
  SortingState,
} from "@tanstack/react-table";

interface TanStackTableProps {
  data: any[];
  columns: any[];
  globalFilter: string;
  sorting: any[];
  setGlobalFilter: (filterValue: string) => void;
  setSorting: (sorting: Updater<SortingState>) => void;
  pageSizeOptions: number[];
}

const TanStackTable: React.FC<TanStackTableProps> = ({
  data,
  columns,
  globalFilter,
  sorting,
  setGlobalFilter,
  setSorting,
  pageSizeOptions,
}) => {
  const totalRows = data?.length;
  const defaultPageSize = totalRows >= 5 && totalRows <= 10 ? totalRows : 10;
  const table = useReactTable({
    data,
    columns,
    state: {
      globalFilter,
      sorting,
    },
    initialState: { pagination: { pageSize: defaultPageSize } },
    onGlobalFilterChange: setGlobalFilter,
    onSortingChange: (updater) => setSorting(updater),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    globalFilterFn: (row, columnId, filterValue) => {
      const cellValue = row?.getValue(columnId);
      if (!cellValue) {
        return false;
      }
      return cellValue
        .toString()
        .toLowerCase()
        .includes(filterValue.toLowerCase());
    },
  });

  if (!data) {
    return <div className="text-red-500">Error fetching table data</div>;
  }

  return (
    <>
      <table className="min-w-full divide-y divide-gray-200">
        {/* {Table Header} */}
        <thead className="bg-wbv-theme">
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <th
                  key={header.id}
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-bold uppercase tracking-wider text-white"
                  onClick={header.column.getToggleSortingHandler()}
                >
                  {flexRender(
                    header.column.columnDef.header,
                    header.getContext(),
                  )}
                  {{
                    asc: " 🔼",
                    desc: " 🔽",
                  }[header.column.getIsSorted() as string] ?? null}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        {/* {Table Body} */}
        <tbody className="divide-y divide-gray-200 bg-white">
          {table?.getRowModel().rows &&
          table.getRowModel().rows.length !== 0 ? (
            table.getRowModel().rows.map((row, i) => (
              <tr
                key={row.id}
                className={`${i % 2 === 0 ? "bg-slate-100" : "bg-slate-200"}`}
              >
                {row.getVisibleCells().map((cell) => (
                  <td key={cell.id} className="whitespace-nowrap px-6 py-4">
                    <div className="flex items-center">
                      <div className="text-xs font-medium text-gray-900">
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </div>
                    </div>
                  </td>
                ))}
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={columns.length} className="py-4 text-center">
                No records found
              </td>
            </tr>
          )}
        </tbody>
      </table>
      {/* {Pagination} */}
      <div className="mt-2 flex items-center justify-end gap-2 text-sm">
        {data?.length > 5 && (
          <>
            <button
              onClick={() => {
                table.previousPage();
              }}
              disabled={!table.getCanPreviousPage()}
              className="rounded border border-gray-500 p-1 px-2 disabled:opacity-30"
            >
              {"<"}
            </button>
            <button
              onClick={() => {
                table.nextPage();
              }}
              disabled={!table.getCanNextPage()}
              className="rounded border border-gray-500 p-1 px-2 disabled:opacity-30"
            >
              {">"}
            </button>
          </>
        )}
        <span className="flex items-center gap-1 px-1">
          <div>Page</div>
          <strong>
            {table.getState().pagination.pageIndex + 1} of{" "}
            {table.getPageCount()}
          </strong>
        </span>
        {data?.length > 5 && (
          <span className="flex items-center gap-1">
            <span className="pr-1">|</span> Go to page:
            <input
              type="number"
              defaultValue={table.getState().pagination.pageIndex + 1}
              onChange={(e) => {
                const page = e.target.value ? Number(e.target.value) - 1 : 0;
                table.setPageIndex(page);
              }}
              min="1"
              max={`${table.getPageCount()}`}
            />
          </span>
        )}
        {data?.length >= 5 && (
          <select
            value={table.getState().pagination.pageSize}
            onChange={(e) => {
              table.setPageSize(Number(e.target.value));
            }}
          >
            {pageSizeOptions.map((pageSize) => (
              <option key={pageSize} value={pageSize}>
                Show {data.length == pageSize ? "All" : pageSize}
              </option>
            ))}
          </select>
        )}
      </div>
    </>
  );
};

export default TanStackTable;
