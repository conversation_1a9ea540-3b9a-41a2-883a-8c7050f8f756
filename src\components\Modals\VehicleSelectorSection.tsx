import React from 'react';
import { VehicleSelectorSectionProps } from '@/types/product/modal';

const VehicleSelectorSection: React.FC<VehicleSelectorSectionProps> = ({
  isVehicleSelectorChecked,
  setIsVehicleSelectorChecked,
  vehicleDetails,
  setIsVehicleModalOpen,
  variant,
  isLoading = false
}) => {
  return (
    <div className="flex flex-col">
      <label className="mb-1 text-sm font-medium">Vehicle Model</label>
      <div className="flex items-center space-x-2">
        <input
          type="text"
          name="variant_display"
          value={vehicleDetails.displayText || (variant ? `ID: ${variant}` : '')}
          readOnly
          disabled={isLoading}
          className={`flex-grow rounded border border-gray-300 bg-gray-50 p-2 focus:border-wbv-theme focus:outline-none focus:ring-2 focus:ring-wbv-theme ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          placeholder="Select a vehicle using the checkbox"
        />
        <div className="flex items-center">
          <input
            type="checkbox"
            id="useVehicleSelector"
            className={`mr-1 h-4 w-4 rounded border-gray-300 text-wbv-theme focus:ring-wbv-theme ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
            checked={isVehicleSelectorChecked}
            disabled={isLoading}
            onChange={(e) => {
              if (isLoading) return;

              // Update the checkbox state
              setIsVehicleSelectorChecked(e.target.checked);

              // Only open the modal when the checkbox is checked
              if (e.target.checked) {
                setIsVehicleModalOpen(true);
                // When checked, we keep the existing vehicle details if any
              }
              // Note: We don't reset the vehicle details here
              // This will be handled in the parent component (ProductModal)
              // to ensure proper state management
            }}
          />
          <label htmlFor="useVehicleSelector" className="text-xs">
            Link to Whitebook Vehicle
          </label>
        </div>
      </div>
    </div>
  );
};

export default VehicleSelectorSection;
