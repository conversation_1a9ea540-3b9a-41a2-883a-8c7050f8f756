import { NextRequest, NextResponse } from "next/server";
import { engineAxiosInstance, v2EngineAxiosInstance } from "@/utils/axios";

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  const { id } = params;

  try {
    const response = await v2EngineAxiosInstance.get(`/organizations/${Number(id)}`);

    const organization = response.data;
    return NextResponse.json(organization);
  } catch (error) {
    console.error("Error fetching organization:", error);
    return NextResponse.json(
      { error: "Error fetching organization" },
      { status: 500 },
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  const { id } = params;
  try {
    const body = await req.json();
    const { ...updateData } = body;
    const response = await engineAxiosInstance.put(
      `/organization/${id}`,
      updateData,
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    );
    return NextResponse.json(response.data);
  } catch (error) {
    console.error("Error updating organization:", error);
    return NextResponse.json(
      { error: "Error updating organization" },
      { status: 500 },
    );
  }
}
