from pandas import (
    DataFrame,
    DateOffset,
    Series,
    Timestamp,
    concat,
    json_normalize,
    read_json,
    to_datetime,
)
from datetime import datetime
from json import loads as json_loads


def get_mode_or_unique(x: Series) -> str:
    """
    Returns the mode of the given pandas Series or a string indicating all values are unique.
    Parameters:
    x (Series): A pandas Series object.
    Returns:
    str: The mode of the Series if there is a single mode, or "all unique" if there are multiple modes with the same frequency.
    """

    modes = x.mode()
    if len(modes) > 1 and (x == modes[0]).sum() == (x == modes[1]).sum():
        return "all unique"
    return modes[0]


def summarize_searches_per_interval(
    dataframe: DataFrame, groupby_columns: list, interval: str
) -> DataFrame:
    """
    Summarizes search data by specified intervals and groups.
    Args:
        dataframe (DataFrame): The input DataFrame containing search data.
        groupby_columns (list): A list of column names to group by.
        interval (str): The column name representing the interval for aggregation.
    Returns:
        DataFrame: A DataFrame with the summarized search data, including:
            - count: The number of searches per interval.
            - make_mode: The most frequent 'make' value or unique value if only one.
            - model_mode: The most frequent 'model' value or unique value if only one.
            - variant_mode: The most frequent 'variant' value or unique value if only one.
    """

    searches_per_interval_df = (
        dataframe.groupby(groupby_columns)
        .agg(
            search_count=(interval, "size"),
            makes_mode=("make", get_mode_or_unique),
            models_mode=("model", get_mode_or_unique),
            variants_mode=("variant", get_mode_or_unique),
        )
        .reset_index()
    )
    return searches_per_interval_df


def get_today_time() -> datetime:
    """
    Returns the current date and time.
    Returns:
        datetime: The current date and time.
    """
    return Timestamp.now().tz_localize("UTC")


def get_current_week_number() -> int:
    """
    Returns the current week number.
    Returns:
        int: The current week number.
    """
    return get_today_time().isocalendar()[1]


def get_last_week_number() -> int:
    """
    Returns the last week number.
    Returns:
        int: The last week number.
    """
    return get_current_week_number() - 1 if get_current_week_number() > 1 else 52


def get_last_week_friday() -> datetime:
    """
    Calculate the date of the last week's Friday based on the given date.
    Args:
        today (datetime): The current date from which to calculate the last week's Friday.
    Returns:
        datetime: The date of the last week's Friday.
    """
    last_week_friday = (
        get_today_time() - DateOffset(days=(get_today_time().weekday() + 3) % 7 + 7)
    ).normalize()
    return last_week_friday


def get_this_week_thursday() -> datetime:
    """
    Calculate the date of this week's Thursday based on the given date.
    Args:
        today (datetime): The current date from which to calculate this week's Thursday.
    Returns:
        datetime: The date of this week's Thursday at 23:59:59.999.
    """
    this_week_thursday = get_today_time() + DateOffset(
        days=(3 - get_today_time().weekday()) % 7
    )
    this_week_thursday = this_week_thursday.replace(
        hour=23, minute=59, second=59, microsecond=999999
    )
    return this_week_thursday


def get_friday_to_thursday_df(dataframe: DataFrame) -> DataFrame:
    """
    Filters the given DataFrame to include only the data from the last Friday to this week's Thursday.
    The function calculates the date range from the last Friday to this week's Thursday based on the current date and time.
    It then filters the DataFrame to include only the rows where the 'time' column falls within this date range.
    Additionally, it excludes rows where the 'time' column is on a Thursday of the last week.
    Args:
        dataframe (DataFrame): The input DataFrame containing a 'time' column with datetime values.
    Returns:
        DataFrame: A DataFrame filtered to include only the data from the last Friday to this week's Thursday.
    """

    last_week_friday = get_last_week_friday()
    this_week_thursday = get_this_week_thursday()
    last_week_thursday = last_week_friday + DateOffset(
        hours=167, minutes=59, seconds=59, microseconds=999999
    )
    last_week_friday_hours_offset = (
        get_today_time() - last_week_friday
    ).total_seconds() / 3600

    if get_today_time() > this_week_thursday:
        this_week_thursday_hours_offset = (
            get_today_time() - this_week_thursday
        ).total_seconds() / 3600
    elif get_today_time() < this_week_thursday:
        this_week_thursday_hours_offset = (
            get_today_time() - last_week_thursday
        ).total_seconds() / 3600
    else:
        this_week_thursday_hours_offset = 0

    this_week_thursday_date = get_today_time() - DateOffset(
        hours=this_week_thursday_hours_offset
    )
    last_week_friday_date = get_today_time() - DateOffset(
        hours=last_week_friday_hours_offset
    )
    dataframe = dataframe[
        (dataframe["time"] >= last_week_friday_date)
        & (dataframe["time"] <= this_week_thursday_date)
    ]
    dataframe = dataframe[
        ~(
            (dataframe["time"].dt.day_name() == "Thursday")
            & (dataframe["time"].dt.isocalendar().week == get_last_week_number())
        )
    ]
    return dataframe


def get_searches_df(searches_json_path: str) -> DataFrame:
    """
    Reads a JSON file containing search history data, processes it, and returns a DataFrame with additional time-based columns.
    Args:
        searches_json_path (str): The file path to the JSON file containing search history data.
    Returns:
        DataFrame: A pandas DataFrame with the following columns:
            - time: The timestamp of the search.
            - day: The day of the week when the search was made.
            - week: The ISO calendar week number when the search was made.
            - month: The month name when the search was made.
            - quarter: The quarter of the year when the search was made.
            - year: The year when the search was made.
            - make: The manufacturer of the vehicle searched.
            - model: The make and model of the vehicle searched.
            - variant: The make, model, and submodel of the vehicle searched.
    """

    dataframe = read_json(searches_json_path)
    dataframe["CreatedAt"] = to_datetime(dataframe["CreatedAt"])
    dataframe = dataframe.sort_values(by="CreatedAt", ascending=False)
    dataframe["day"] = dataframe["CreatedAt"].dt.day_name()
    dataframe["week"] = dataframe["CreatedAt"].dt.isocalendar().week
    dataframe["month"] = dataframe["CreatedAt"].dt.month_name()
    dataframe["quarter"] = "Q" + dataframe["CreatedAt"].dt.quarter.astype(str)
    dataframe["year"] = dataframe["CreatedAt"].dt.year
    dataframe["search_term_dict"] = dataframe["search_term"].apply(json_loads)
    search_term_df = json_normalize(dataframe["search_term"])
    dataframe = concat([dataframe, search_term_df], axis=1)
    dataframe = dataframe.drop(columns=["search_term"])
    dataframe = dataframe.copy()[
        ["search_term_dict", "CreatedAt", "day", "week", "month", "quarter", "year"]
    ]
    dataframe["make"] = dataframe["search_term_dict"].apply(
        lambda x: x.get("Manufacturer")
    )
    dataframe["model"] = dataframe["search_term_dict"].apply(lambda x: x.get("Model"))

    dataframe["variant"] = dataframe["search_term_dict"].apply(
        lambda x: x.get("SubModel")
    )
    dataframe["model"] = dataframe["make"] + " - " + dataframe["model"]
    dataframe["variant"] = dataframe["model"] + " - " + dataframe["variant"]
    dataframe = dataframe.rename(columns={"CreatedAt": "time"})
    dataframe = dataframe.drop(columns=["search_term_dict"])
    return dataframe


def get_searches_per_day_df(dataframe: DataFrame) -> DataFrame:
    """
    Aggregates search data by day and adds additional time-related columns.
    Args:
        dataframe (DataFrame): A pandas DataFrame containing search data with a 'time' column.
    Returns:
        DataFrame: A pandas DataFrame with aggregated search data per day, including columns for:
            - date: The date of the searches.
            - day: The day of the week.
            - week: The ISO calendar week number.
            - month: The month name.
            - year: The year.
            - quarter: The quarter of the year.
            - search_count: The number of searches on that date.
            - makes_mode: The most frequent make searched on that date.
            - models_mode: The most frequent model searched on that date.
            - variants_mode: The most frequent variant searched on that date.
    """

    dataframe = (
        dataframe.groupby(dataframe["time"].dt.date)
        .agg(
            search_count=("time", "size"),
            makes_mode=("make", get_mode_or_unique),
            models_mode=("model", get_mode_or_unique),
            variants_mode=("variant", get_mode_or_unique),
        )
        .reset_index()
    )
    dataframe = dataframe.rename(columns={"time": "date"})
    dataframe["day"] = to_datetime(dataframe["date"]).dt.day_name()
    dataframe["week"] = to_datetime(dataframe["date"]).dt.isocalendar().week
    dataframe["month"] = to_datetime(dataframe["date"]).dt.month_name()
    dataframe["quarter"] = "Q" + to_datetime(dataframe["date"]).dt.quarter.astype(str)
    dataframe["year"] = to_datetime(dataframe["date"]).dt.year
    return dataframe[
        [
            "date",
            "day",
            "week",
            "month",
            "year",
            "quarter",
            "search_count",
            "makes_mode",
            "models_mode",
            "variants_mode",
        ]
    ]


def get_writable_df(dataframe: DataFrame):
    """
    Converts the 'time' column in the given DataFrame to a timezone-naive datetime format
    and resets the DataFrame index.
    Parameters:
    dataframe (DataFrame): The input DataFrame containing a 'time' column with datetime strings.
    Returns:
    DataFrame: The modified DataFrame with the 'time' column as timezone-naive datetime objects and the index reset.
    """
    dataframe["time"] = to_datetime(
        dataframe["time"], format="%Y-%m-%d %H:%M:%S %p"
    ).dt.tz_localize(None)
    dataframe.reset_index(drop=True, inplace=True)
    return dataframe
